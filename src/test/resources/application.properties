quarkus.log.level=DEBUG
quarkus.log.category."org.hibernate".level=info
# url test
quarkus.rest-client.urlassimoco.url=https://10.165.8.80
assimoco.login.user=TAA0491
assimoco.login.password=assimoco
assimoco.login.provider=password
assimoco.login.return=/portal/auth.jsp
quarkus.tls.trust-all=true
unimatica.login.user=toolkit_user
unimatica.login.password=Assimoco123!
unimatica.codice.ente=ASSIMOCO
unimatica.secret=AssimocoSecret
unimatica.funzione.connect=ConnectSchedule
unimatica.funzione.info=ScheduleInfo
unimatica.etica.login.user=toolkit_bancaetica-test
unimatica.etica.login.password=password_bancaetica
unimatica.etica.codice.ente= BANCAETICA
unimatica.etica.secret=secret_bancaetica
quarkus.rest-client.unimatica.url=https://uniportale-test.unimaticaspa.it
quarkus.http.enable-compression=true
quarkus.rest-client.urlbilanciatore.url=https://************
quarkus.rest-client.logging.scope=request-response
quarkus.rest-client.logging.body-limit=9024
quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG
quarkus.rest-client.urlanamov.url=http://assiinteap01-t.assimoco.net:8080/
quarkus.rest-client.urlanag.url=http://pltf-assimocopassportal-int.ad.rgigroup.com:8081/
quarkus.rest-client.urlCancellation.url=http://pltf-assimocopassportal-int.ad.rgigroup.com:8081/
quarkus.rest-client.urlPolizza.url=https://layer-v.assimoco.it/
quarkus.rest-client.rgi.url=https://iad-v.assimoco.it/
type=type-test
host.iadv=iad-v.assimoco.it
mail.document=anag
smallrye.jwt.encrypt.key=publicKey.pem
#grpc
quarkus.grpc.server.port=9003
quarkus.grpc.server.host=localhost
idNode=1
codAgenzia = 999
codAgenziaEtica = 444
codLocal = it
archiveCode=ANAGASSIMOCO
quarkus.opentelemetry.enabled=true
quarkus.opentelemetry.tracer.enabled=false
quarkus.log.console.format=%d{HH:mm:ss} %-5p traceId=%X{traceId}, spanId=%X{spanId},[%c] -> %s%e%n
quarkus.http.auth.permission.basic-or-bearer.paths=/api/v1/anamov/documents/*
#Jwt Configuration
mp.jwt.verify.publickey.location=publicKey.pem
smallrye.jwt.new-token.issuer=https://example.com/issuer
smallrye.jwt.new-token.key-encryption-algorithm=RSA-OAEP-256
mp.jwt.decrypt.key.location=keyEncryptPrivate.pem
smallrye.jwt.decrypt.algorithm=RSA_OAEP_256
# gRPC client configuration
quarkus.grpc.clients.jwtGrpc.host=localhost
quarkus.grpc.clients.jwtGrpc.port=9005
quarkus.grpc.clients.raiffeisenGrpc.host=localhost
quarkus.grpc.clients.raiffeisenGrpc.port=9002
quarkus.grpc.clients.utilityGrpc.host=localhost
quarkus.grpc.clients.utilityGrpc.port=9010
quarkus.jackson.fail-on-empty-beans=false
quarkus.http.auth.permission.basic-or-bearer.policy=authenticated
quarkus.http.port=8087
key.INFO=DOCUMENT
fromemail=<EMAIL>;
quarkus.cache.caffeine."cache-token".expire-after-write=82800S
quarkus.grpc.clients.comunicationManagerGrpcClient.host=localhost
quarkus.grpc.clients.comunicationManagerGrpcClient.port=9006
quarkus.rest-client.urlassimoco.connect-timeout=60000
mail.document=<EMAIL>
type.documents=documents
quarkus.http.test-port=8090
