package it.yolo.resources;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import it.yolo.entity.WarrantyPremium;
import it.yolo.model.BaseData;
import it.yolo.model.OrderQuoteRequest;
import it.yolo.model.WarrantyDetail;
import it.yolo.service.IamService;
import it.yolo.service.OrderService;
import it.yolo.service.WarrantyPricingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;

import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@QuarkusTest
@TestSecurity(authorizationEnabled = false)
class WarrantyPricingResourceIntegrationTest {

    @Inject
    WarrantyPricingResource warrantyPricingResource;

    @InjectMock
    OrderService orderService;

    @InjectMock
    WarrantyPricingService warrantyPricingService;

    @InjectMock
    IamService iamService;

    private ObjectMapper objectMapper;
    private JsonNode mockOrderJson;
    private List<WarrantyDetail> allWarrantyDetails;
    private List<Integer> chosenWarrantyIds;
    private List<WarrantyPremium> mockPremiums;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        
        // Setup mock data
        allWarrantyDetails = Arrays.asList(
            new WarrantyDetail(1001, "Warranty 1", "1000.00", "W001"),
            new WarrantyDetail(1002, "Warranty 2", "2000.00", "W002"),
            new WarrantyDetail(1003, "Warranty 3", "3000.00", "W003")
        );
        
        chosenWarrantyIds = Arrays.asList(1001, 1002); // Only first two are chosen
        
        mockPremiums = createMockPremiums();
        mockOrderJson = createMockOrderJson();
    }

    @Test
    void testGetQuoteFromOrder_NewLogic() throws Exception {
        // Given
        String orderCode = "ORDER-001";
        BaseData<OrderQuoteRequest> request = new BaseData<>(new OrderQuoteRequest(orderCode));

        // Mock service calls
        when(iamService.getToken()).thenReturn("mock-token");
        when(orderService.getOrder(orderCode, "mock-token")).thenReturn(mockOrderJson);
        when(orderService.extractAllWarrantyDetailsFromOrder(mockOrderJson)).thenReturn(allWarrantyDetails);
        when(orderService.extractChosenWarrantyIdsFromOrder(mockOrderJson)).thenReturn(chosenWarrantyIds);
        when(orderService.extractPackageDurationFromOrder(mockOrderJson)).thenReturn(1);
        
        // Mock pricing service calls
        when(warrantyPricingService.getPremiums(allWarrantyDetails, 1)).thenReturn(mockPremiums);
        when(warrantyPricingService.getMonthlyPremiums(allWarrantyDetails)).thenReturn(mockPremiums);
        
        // Mock total calculations - all warranties total vs selected warranties total
        when(warrantyPricingService.calculateTotalPremiumForSelected(mockPremiums, chosenWarrantyIds))
            .thenReturn(new BigDecimal("270.00")); // Only selected warranties (1001 + 1002)
        
        when(warrantyPricingService.getPremiumCurrency(mockPremiums)).thenReturn("EUR");
        when(warrantyPricingService.getBenefitCurrency(mockPremiums)).thenReturn("EUR");

        // Mock order update
        Response mockUpdateResponse = Response.ok("{\"success\": true}").build();
        when(orderService.updateOrderWithQuotation(
            eq(orderCode), 
            eq(mockPremiums), 
            eq(new BigDecimal("270.00")), 
            eq("EUR"), 
            eq("EUR"), 
            eq(mockPremiums), 
            eq(new BigDecimal("270.00")), 
            eq(1), 
            eq("mock-token"),
            eq(chosenWarrantyIds)
        )).thenReturn(mockUpdateResponse);

        // When
        Response response = warrantyPricingResource.getQuoteFromOrder(request);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getStatus());
        
        // Verify that the service was called with all warranties for pricing
        // but only selected warranties for total calculation
        // This is verified through the mock setup above
    }

    @Test
    void testGetQuoteFromOrder_NoFilterWarranties() throws Exception {
        // Given
        String orderCode = "ORDER-002";
        BaseData<OrderQuoteRequest> request = new BaseData<>(new OrderQuoteRequest(orderCode));

        // Mock service calls
        when(iamService.getToken()).thenReturn("mock-token");
        when(orderService.getOrder(orderCode, "mock-token")).thenReturn(mockOrderJson);
        when(orderService.extractAllWarrantyDetailsFromOrder(mockOrderJson)).thenReturn(List.of()); // Empty list

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            warrantyPricingResource.getQuoteFromOrder(request);
        });
        
        assertTrue(exception.getMessage().contains("No warranty details found in filterWarranties"));
    }

    @Test
    void testGetQuoteFromOrder_AllWarrantiesQuotedButOnlySelectedInTotal() throws Exception {
        // Given - scenario where 3 warranties are available but only 1 is selected
        String orderCode = "ORDER-003";
        BaseData<OrderQuoteRequest> request = new BaseData<>(new OrderQuoteRequest(orderCode));

        List<Integer> singleChosenWarrantyId = Arrays.asList(1001); // Only first warranty chosen

        // Mock service calls
        when(iamService.getToken()).thenReturn("mock-token");
        when(orderService.getOrder(orderCode, "mock-token")).thenReturn(mockOrderJson);
        when(orderService.extractAllWarrantyDetailsFromOrder(mockOrderJson)).thenReturn(allWarrantyDetails);
        when(orderService.extractChosenWarrantyIdsFromOrder(mockOrderJson)).thenReturn(singleChosenWarrantyId);
        when(orderService.extractPackageDurationFromOrder(mockOrderJson)).thenReturn(1);
        
        // Mock pricing service calls - all warranties are priced
        when(warrantyPricingService.getPremiums(allWarrantyDetails, 1)).thenReturn(mockPremiums);
        when(warrantyPricingService.getMonthlyPremiums(allWarrantyDetails)).thenReturn(mockPremiums);
        
        // Mock total calculations - only selected warranty total (just first one)
        when(warrantyPricingService.calculateTotalPremiumForSelected(mockPremiums, singleChosenWarrantyId))
            .thenReturn(new BigDecimal("120.00")); // Only first warranty
        
        when(warrantyPricingService.getPremiumCurrency(mockPremiums)).thenReturn("EUR");
        when(warrantyPricingService.getBenefitCurrency(mockPremiums)).thenReturn("EUR");

        // Mock order update
        Response mockUpdateResponse = Response.ok("{\"success\": true}").build();
        when(orderService.updateOrderWithQuotation(
            eq(orderCode), 
            eq(mockPremiums), 
            eq(new BigDecimal("120.00")), 
            eq("EUR"), 
            eq("EUR"), 
            eq(mockPremiums), 
            eq(new BigDecimal("120.00")), 
            eq(1), 
            eq("mock-token"),
            eq(singleChosenWarrantyId)
        )).thenReturn(mockUpdateResponse);

        // When
        Response response = warrantyPricingResource.getQuoteFromOrder(request);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getStatus());
        
        // This test verifies that:
        // 1. All 3 warranties are sent to pricing service (allWarrantyDetails)
        // 2. But only 1 warranty is used for total calculation (singleChosenWarrantyId)
        // 3. The total reflects only the selected warranty (120.00 instead of full 390.00)
    }

    private List<WarrantyPremium> createMockPremiums() {
        it.yolo.entity.AnagWarranty anag1 = new it.yolo.entity.AnagWarranty();
        anag1.setId(1001L);
        anag1.setInternalCode("W001");
        
        it.yolo.entity.AnagWarranty anag2 = new it.yolo.entity.AnagWarranty();
        anag2.setId(1002L);
        anag2.setInternalCode("W002");
        
        it.yolo.entity.AnagWarranty anag3 = new it.yolo.entity.AnagWarranty();
        anag3.setId(1003L);
        anag3.setInternalCode("W003");

        WarrantyPremium premium1 = new WarrantyPremium( "1000.00", 12, new BigDecimal("120.00"), "EUR", "EUR");
        premium1.setWarranty(anag1);
        
        WarrantyPremium premium2 = new WarrantyPremium("2000.00", 12, new BigDecimal("150.00"), "EUR", "EUR");
        premium2.setWarranty(anag2);
        
        WarrantyPremium premium3 = new WarrantyPremium("3000.00", 12, new BigDecimal("120.00"), "EUR", "EUR");
        premium3.setWarranty(anag3);

        return Arrays.asList(premium1, premium2, premium3);
    }

    private JsonNode createMockOrderJson() {
        ObjectNode root = objectMapper.createObjectNode();
        ObjectNode data = root.putObject("data");
        ArrayNode orderItems = data.putArray("orderItem");
        ObjectNode orderItem = orderItems.addObject();
        ObjectNode instance = orderItem.putObject("instance");

        // Add packetDuration
        ObjectNode packetDuration = instance.putObject("packetDuration");
        packetDuration.put("duration", 1);
        packetDuration.put("durationType", "year");

        return root;
    }
}
