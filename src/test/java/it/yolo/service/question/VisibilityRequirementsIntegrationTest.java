package it.yolo.service.question;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.response.client.product.Question;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test that verifies all the specific visibility requirements:
 * 1. Count of optional warranties > 0
 * 2. tipologiaProprietario in ["P", "PL"]
 * 3. Specific warranty exists AND tipologiaFabbricato = "VM"
 * 4. Simple condition: tipologiaFabbricato = "VM"
 */
class VisibilityRequirementsIntegrationTest {

    private QuestionProcessorService processorService;
    private QuestionTemplatingService templatingService;
    private QuestionVisibilityService visibilityService;
    private ObjectMapper objectMapper;
    private OrderEntity orderEntity;

    @BeforeEach
    void setUp() {
        // Create real instances for integration testing
        templatingService = new QuestionTemplatingService();
        visibilityService = new QuestionVisibilityService();
        processorService = new QuestionProcessorService();
        
        // Manually inject dependencies
        processorService.templatingService = templatingService;
        processorService.visibilityService = visibilityService;
        
        objectMapper = new ObjectMapper();
        
        setupOrderEntityForRequirements();
    }

    private void setupOrderEntityForRequirements() {
        orderEntity = new OrderEntity();
        orderEntity.setId(123L);
        orderEntity.setOrderCode("REQ-TEST-001");

        OrderItemEntity orderItemEntity = new OrderItemEntity();
        orderItemEntity.setPrice("2500.00");
        orderItemEntity.setPolicy_number("POL-REQ-001");

        // Setup insured_item with specific test data
        ObjectNode insuredItem = objectMapper.createObjectNode();
        insuredItem.put("tipologiaProprietario", "P"); // Proprietario - should match requirement 2
        insuredItem.put("tipologiaFabbricato", "VM"); // Villa/Villino - should match requirements 3 & 4
        insuredItem.put("tipologiaUsoAbitazione", "S"); // Seconda casa
        insuredItem.put("value", "500000");
        
        ObjectNode company = objectMapper.createObjectNode();
        company.put("scoreESG", "4");
        company.put("name", "Test Company");
        insuredItem.set("company", company);

        orderItemEntity.setInsured_item(insuredItem);

        List<OrderItemEntity> orderItems = new ArrayList<>();
        orderItems.add(orderItemEntity);
        orderEntity.setOrderItem(orderItems);

        // Setup chosenWarranties with specific warranties for requirement 3
        setupChosenWarrantiesForRequirements();
    }

    private void setupChosenWarrantiesForRequirements() {
        ObjectNode chosenWarranties = objectMapper.createObjectNode();
        ArrayNode warranties = objectMapper.createArrayNode();

        // Mandatory warranties
        ObjectNode fabbricato = objectMapper.createObjectNode();
        fabbricato.put("name", "Fabbricato");
        fabbricato.put("mandatory", true);
        warranties.add(fabbricato);

        ObjectNode contenuto = objectMapper.createObjectNode();
        contenuto.put("name", "Contenuto");
        contenuto.put("mandatory", true);
        warranties.add(contenuto);

        // Optional warranties (requirement 1: count > 0)
        ObjectNode rc = objectMapper.createObjectNode();
        rc.put("name", "Responsabilità Civile");
        rc.put("mandatory", false);
        warranties.add(rc);

        ObjectNode tutela = objectMapper.createObjectNode();
        tutela.put("name", "Tutela Legale");
        tutela.put("mandatory", false);
        warranties.add(tutela);

        // Specific warranty for requirement 3
        ObjectNode furto = objectMapper.createObjectNode();
        furto.put("name", "Furto e Rapina");
        furto.put("mandatory", false);
        warranties.add(furto);

        // Create the correct structure: instance.chosenWarranties.data.warranties
        ObjectNode chosenWarrantiesData = objectMapper.createObjectNode();
        chosenWarrantiesData.set("warranties", warranties);
        chosenWarranties.set("data", chosenWarrantiesData);

        // Set in orderItem.instance.chosenWarranties
        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);
        ObjectNode instanceNode = objectMapper.createObjectNode();
        instanceNode.set("chosenWarranties", chosenWarranties);
        orderItem.setInstance(instanceNode);
    }

    @Test
    void testRequirement1_CountOptionalWarranties() {
        // Requirement 1: Show question only if number of optional warranties > 0
        List<Question> questions = new ArrayList<>();
        
        Question q1 = new Question();
        q1.setId(1);
        q1.setContent("Hai selezionato {{chosenWarranties.warranties[mandatory=false]}} come garanzie aggiuntive. Vuoi aggiungerne altre?");
        
        // Rule: count of optional warranties > 0
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "chosenWarranties.warranties[mandatory=false]");
        inclusionRule.put("operator", "count");
        inclusionRule.put("value", "0");
        inclusionRule.put("comparison", "gt");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);
        q1.setRule(rule);
        
        questions.add(q1);
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(1, result.size());
        assertEquals("Hai selezionato Responsabilità Civile, Tutela Legale e Furto e Rapina come garanzie aggiuntive. Vuoi aggiungerne altre?", 
                    result.get(0).getContent());
    }

    @Test
    void testRequirement2_TipologiaProprietarioMultipleValues() {
        // Requirement 2: Show question only if tipologiaProprietario is "P" or "PL"
        List<Question> questions = new ArrayList<>();
        
        Question q1 = new Question();
        q1.setId(2);
        q1.setContent("Come proprietario dell'immobile, hai diritto a sconti speciali.");
        
        // Rule: tipologiaProprietario in ["P", "PL"]
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "tipologiaProprietario");
        inclusionRule.put("operator", "in");
        ArrayNode values = objectMapper.createArrayNode();
        values.add("P");
        values.add("PL");
        inclusionRule.set("value", values);
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);
        q1.setRule(rule);
        
        questions.add(q1);
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(1, result.size());
        assertEquals("Come proprietario dell'immobile, hai diritto a sconti speciali.", result.get(0).getContent());
    }

    @Test
    void testRequirement3_SpecificWarrantyAndFabbricato() {
        // Requirement 3: Show question only if specific warranty exists AND tipologiaFabbricato = "VM"
        List<Question> questions = new ArrayList<>();
        
        Question q1 = new Question();
        q1.setId(3);
        q1.setContent("Per la tua villa con garanzia Furto e Rapina, raccomandiamo un sistema di allarme.");
        
        // Rule: warranties contains "Furto e Rapina" AND tipologiaFabbricato = "VM"
        ObjectNode rule = objectMapper.createObjectNode();
        rule.put("logicOperator", "AND");
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        
        ObjectNode rule1 = objectMapper.createObjectNode();
        rule1.put("field", "chosenWarranties.warranties");
        rule1.put("operator", "contains");
        rule1.put("value", "Furto e Rapina");
        inclusionRules.add(rule1);
        
        ObjectNode rule2 = objectMapper.createObjectNode();
        rule2.put("field", "tipologiaFabbricato");
        rule2.put("operator", "eq");
        rule2.put("value", "VM");
        inclusionRules.add(rule2);
        
        rule.set("inclusionRules", inclusionRules);
        q1.setRule(rule);
        
        questions.add(q1);
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(1, result.size());
        assertEquals("Per la tua villa con garanzia Furto e Rapina, raccomandiamo un sistema di allarme.", result.get(0).getContent());
    }

    @Test
    void testRequirement4_SimpleFabbricatoCondition() {
        // Requirement 4: Show question only if tipologiaFabbricato = "VM"
        List<Question> questions = new ArrayList<>();
        
        Question q1 = new Question();
        q1.setId(4);
        q1.setContent("Le ville richiedono valutazioni specifiche per la copertura assicurativa.");
        
        // Rule: tipologiaFabbricato = "VM"
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "tipologiaFabbricato");
        inclusionRule.put("operator", "eq");
        inclusionRule.put("value", "VM");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);
        q1.setRule(rule);
        
        questions.add(q1);
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(1, result.size());
        assertEquals("Le ville richiedono valutazioni specifiche per la copertura assicurativa.", result.get(0).getContent());
    }

    @Test
    void testAllRequirementsTogether() {
        // Test all requirements in a single processing run
        List<Question> questions = createAllRequirementQuestions();
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        // All 4 questions should be visible with current test data
        assertEquals(4, result.size());
        
        // Verify each question content (with templating applied where applicable)
        assertEquals("Hai selezionato Responsabilità Civile, Tutela Legale e Furto e Rapina come garanzie aggiuntive. Vuoi aggiungerne altre?", 
                    result.get(0).getContent());
        assertEquals("Come proprietario dell'immobile, hai diritto a sconti speciali.", 
                    result.get(1).getContent());
        assertEquals("Per la tua villa con garanzia Furto e Rapina, raccomandiamo un sistema di allarme.", 
                    result.get(2).getContent());
        assertEquals("Le ville richiedono valutazioni specifiche per la copertura assicurativa.", 
                    result.get(3).getContent());
    }

    @Test
    void testRequirementsWithDifferentData() {
        // Test with data that should hide some questions
        
        // Change tipologiaProprietario to "L" (locatario) - should hide requirement 2
        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);
        ObjectNode insuredItem = (ObjectNode) orderItem.getInsured_item();
        insuredItem.put("tipologiaProprietario", "L");
        
        // Remove "Furto e Rapina" warranty - should hide requirement 3
        ObjectNode chosenWarranties = (ObjectNode) orderEntity.getChosenWarranties();
        ArrayNode warranties = (ArrayNode) chosenWarranties.get("warranties");
        for (int i = warranties.size() - 1; i >= 0; i--) {
            ObjectNode warranty = (ObjectNode) warranties.get(i);
            if ("Furto e Rapina".equals(warranty.get("name").asText())) {
                warranties.remove(i);
                break;
            }
        }
        
        List<Question> questions = createAllRequirementQuestions();
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        // Only requirements 1 and 4 should be visible
        assertEquals(2, result.size());
        
        // Verify which questions are visible
        assertEquals("Hai selezionato Responsabilità Civile e Tutela Legale come garanzie aggiuntive. Vuoi aggiungerne altre?", 
                    result.get(0).getContent()); // Requirement 1
        assertEquals("Le ville richiedono valutazioni specifiche per la copertura assicurativa.", 
                    result.get(1).getContent()); // Requirement 4
    }

    @Test
    void testRequirementsWithNoOptionalWarranties() {
        // Remove all optional warranties - should hide requirement 1
        ObjectNode chosenWarranties = (ObjectNode) orderEntity.getChosenWarranties();
        ArrayNode warranties = (ArrayNode) chosenWarranties.get("warranties");
        for (int i = warranties.size() - 1; i >= 0; i--) {
            ObjectNode warranty = (ObjectNode) warranties.get(i);
            if (warranty.get("mandatory").asBoolean() == false) {
                warranties.remove(i);
            }
        }
        
        List<Question> questions = createAllRequirementQuestions();
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        // Requirements 2, 3 (no Furto e Rapina), and 4 should be visible
        // Requirement 1 should be hidden (no optional warranties)
        // Requirement 3 should be hidden (no Furto e Rapina)
        assertEquals(2, result.size());
        
        assertEquals("Come proprietario dell'immobile, hai diritto a sconti speciali.", 
                    result.get(0).getContent()); // Requirement 2
        assertEquals("Le ville richiedono valutazioni specifiche per la copertura assicurativa.", 
                    result.get(1).getContent()); // Requirement 4
    }

    private List<Question> createAllRequirementQuestions() {
        List<Question> questions = new ArrayList<>();
        
        // Requirement 1: Count optional warranties > 0
        Question q1 = new Question();
        q1.setId(1);
        q1.setContent("Hai selezionato {{chosenWarranties.warranties[mandatory=false]}} come garanzie aggiuntive. Vuoi aggiungerne altre?");
        ObjectNode rule1 = objectMapper.createObjectNode();
        ArrayNode inclusionRules1 = objectMapper.createArrayNode();
        ObjectNode inclusionRule1 = objectMapper.createObjectNode();
        inclusionRule1.put("field", "chosenWarranties.warranties[mandatory=false]");
        inclusionRule1.put("operator", "count");
        inclusionRule1.put("value", "0");
        inclusionRule1.put("comparison", "gt");
        inclusionRules1.add(inclusionRule1);
        rule1.set("inclusionRules", inclusionRules1);
        q1.setRule(rule1);
        questions.add(q1);
        
        // Requirement 2: tipologiaProprietario in ["P", "PL"]
        Question q2 = new Question();
        q2.setId(2);
        q2.setContent("Come proprietario dell'immobile, hai diritto a sconti speciali.");
        ObjectNode rule2 = objectMapper.createObjectNode();
        ArrayNode inclusionRules2 = objectMapper.createArrayNode();
        ObjectNode inclusionRule2 = objectMapper.createObjectNode();
        inclusionRule2.put("field", "tipologiaProprietario");
        inclusionRule2.put("operator", "in");
        ArrayNode values2 = objectMapper.createArrayNode();
        values2.add("P");
        values2.add("PL");
        inclusionRule2.set("value", values2);
        inclusionRules2.add(inclusionRule2);
        rule2.set("inclusionRules", inclusionRules2);
        q2.setRule(rule2);
        questions.add(q2);
        
        // Requirement 3: Specific warranty + fabbricato type
        Question q3 = new Question();
        q3.setId(3);
        q3.setContent("Per la tua villa con garanzia Furto e Rapina, raccomandiamo un sistema di allarme.");
        ObjectNode rule3 = objectMapper.createObjectNode();
        rule3.put("logicOperator", "AND");
        ArrayNode inclusionRules3 = objectMapper.createArrayNode();
        ObjectNode inclusionRule3a = objectMapper.createObjectNode();
        inclusionRule3a.put("field", "chosenWarranties.warranties");
        inclusionRule3a.put("operator", "contains");
        inclusionRule3a.put("value", "Furto e Rapina");
        inclusionRules3.add(inclusionRule3a);
        ObjectNode inclusionRule3b = objectMapper.createObjectNode();
        inclusionRule3b.put("field", "tipologiaFabbricato");
        inclusionRule3b.put("operator", "eq");
        inclusionRule3b.put("value", "VM");
        inclusionRules3.add(inclusionRule3b);
        rule3.set("inclusionRules", inclusionRules3);
        q3.setRule(rule3);
        questions.add(q3);
        
        // Requirement 4: Simple fabbricato condition
        Question q4 = new Question();
        q4.setId(4);
        q4.setContent("Le ville richiedono valutazioni specifiche per la copertura assicurativa.");
        ObjectNode rule4 = objectMapper.createObjectNode();
        ArrayNode inclusionRules4 = objectMapper.createArrayNode();
        ObjectNode inclusionRule4 = objectMapper.createObjectNode();
        inclusionRule4.put("field", "tipologiaFabbricato");
        inclusionRule4.put("operator", "eq");
        inclusionRule4.put("value", "VM");
        inclusionRules4.add(inclusionRule4);
        rule4.set("inclusionRules", inclusionRules4);
        q4.setRule(rule4);
        questions.add(q4);
        
        return questions;
    }
}
