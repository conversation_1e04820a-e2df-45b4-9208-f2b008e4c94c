package it.yolo.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.OrderClientV3;
import it.yolo.entity.WarrantyPremium;
import it.yolo.model.WarrantyDetail;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import jakarta.enterprise.inject.Instance;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class OrderServiceTest {

    private OrderService orderService;
    private OrderClientV3 orderClient;
    private ObjectMapper objectMapper;
    private JsonNode mockOrderJson;

    @BeforeEach
    void setUp() {
        orderClient = Mockito.mock(OrderClientV3.class);
        orderService = new OrderService(orderClient, null, objectMapper);
        objectMapper = new ObjectMapper();
        mockOrderJson = createMockOrderJson();
    }

    @Test
    void testExtractAllWarrantyDetailsFromOrder() {
        // When
        List<WarrantyDetail> result = orderService.extractAllWarrantyDetailsFromOrder(mockOrderJson);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // Verify first warranty
        WarrantyDetail warranty1 = result.get(0);
        assertEquals(Integer.valueOf(1001), warranty1.id());
        assertEquals("Warranty 1", warranty1.name());
        assertEquals("1000.00", warranty1.benefitAmount());
        assertEquals("W001", warranty1.internalCode());
        
        // Verify second warranty
        WarrantyDetail warranty2 = result.get(1);
        assertEquals(Integer.valueOf(1002), warranty2.id());
        assertEquals("Warranty 2", warranty2.name());
        assertEquals("2000.00", warranty2.benefitAmount());
        assertEquals("W002", warranty2.internalCode());
        
        // Verify third warranty
        WarrantyDetail warranty3 = result.get(2);
        assertEquals(Integer.valueOf(1003), warranty3.id());
        assertEquals("Warranty 3", warranty3.name());
        assertEquals("3000.00", warranty3.benefitAmount());
        assertEquals("W003", warranty3.internalCode());
    }

    @Test
    void testExtractChosenWarrantyIdsFromOrder() {
        // When
        List<Integer> result = orderService.extractChosenWarrantyIdsFromOrder(mockOrderJson);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(1001));
        assertTrue(result.contains(1002));
        assertFalse(result.contains(1003)); // This one is not chosen
    }

    @Test
    void testExtractWarrantyDetailsFromOrder() {
        // When
        List<WarrantyDetail> result = orderService.extractWarrantyDetailsFromOrder(mockOrderJson);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // Only chosen warranties
        
        // Verify first chosen warranty
        WarrantyDetail warranty1 = result.get(0);
        assertEquals(Integer.valueOf(1001), warranty1.id());
        assertEquals("Warranty 1", warranty1.name());
        assertEquals("1000.00", warranty1.benefitAmount());
        assertEquals("W001", warranty1.internalCode());
        
        // Verify second chosen warranty
        WarrantyDetail warranty2 = result.get(1);
        assertEquals(Integer.valueOf(1002), warranty2.id());
        assertEquals("Warranty 2", warranty2.name());
        assertEquals("2000.00", warranty2.benefitAmount());
        assertEquals("W002", warranty2.internalCode());
    }

    @Test
    void testExtractAllWarrantyDetailsFromOrder_EmptyFilterWarranties() {
        // Given
        JsonNode emptyOrderJson = createEmptyFilterWarrantiesOrderJson();

        // When
        List<WarrantyDetail> result = orderService.extractAllWarrantyDetailsFromOrder(emptyOrderJson);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testExtractChosenWarrantyIdsFromOrder_EmptyChosenWarranties() {
        // Given
        JsonNode emptyOrderJson = createEmptyChosenWarrantiesOrderJson();

        // When
        List<Integer> result = orderService.extractChosenWarrantyIdsFromOrder(emptyOrderJson);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testUpdateOrderWithQuotation() {
        // Given
        Response mockResponse = mock(Response.class);
        when(mockResponse.getStatus()).thenReturn(200);
        when(mockResponse.readEntity(String.class)).thenReturn("{\"success\": true}");
        when(orderClient.updateQuotation(anyString(), any(JsonNode.class), anyString())).thenReturn(mockResponse);

        List<WarrantyPremium> premiums = createMockPremiums();
        List<Integer> selectedIds = Arrays.asList(1001, 1002);

        // When
        Response result = orderService.updateOrderWithQuotation(
            "ORDER-001",
            premiums,
            new BigDecimal("270.00"),
            "EUR",
            "EUR",
            premiums,
            new BigDecimal("22.50"),
            1,
            "token",
            selectedIds
        );

        // Then
        assertNotNull(result);
        assertEquals(200, result.getStatus());
    }

    private JsonNode createMockOrderJson() {
        ObjectNode root = objectMapper.createObjectNode();
        ObjectNode data = root.putObject("data");
        ArrayNode orderItems = data.putArray("orderItem");
        ObjectNode orderItem = orderItems.addObject();
        ObjectNode instance = orderItem.putObject("instance");

        // Create filterWarranties with 3 warranties
        ObjectNode filterWarranties = instance.putObject("filterWarranties");
        ObjectNode filterData = filterWarranties.putObject("data");
        ArrayNode filterWarrantiesArray = filterData.putArray("warranties");

        // Add 3 warranties to filterWarranties
        for (int i = 1; i <= 3; i++) {
            ObjectNode warranty = filterWarrantiesArray.addObject();
            ObjectNode anagWarranty = warranty.putObject("anagWarranty");
            anagWarranty.put("id", 1000 + i);
            anagWarranty.put("name", "Warranty " + i);
            anagWarranty.put("internal_code", "W00" + i);
            
            ObjectNode ceilings = warranty.putObject("ceilings");
            ceilings.put("selected", (i * 1000) + ".00");
        }

        // Create chosenWarranties with only 2 warranties (1001 and 1002)
        ObjectNode chosenWarranties = instance.putObject("chosenWarranties");
        ObjectNode chosenData = chosenWarranties.putObject("data");
        ArrayNode chosenWarrantiesArray = chosenData.putArray("warranties");

        // Add only first 2 warranties to chosenWarranties
        for (int i = 1; i <= 2; i++) {
            ObjectNode warranty = chosenWarrantiesArray.addObject();
            ObjectNode anagWarranty = warranty.putObject("anagWarranty");
            anagWarranty.put("id", 1000 + i);
            anagWarranty.put("name", "Warranty " + i);
            anagWarranty.put("internal_code", "W00" + i);
            
            ObjectNode ceilings = warranty.putObject("ceilings");
            ceilings.put("selected", (i * 1000) + ".00");
        }

        return root;
    }

    private JsonNode createEmptyFilterWarrantiesOrderJson() {
        ObjectNode root = objectMapper.createObjectNode();
        ObjectNode data = root.putObject("data");
        ArrayNode orderItems = data.putArray("orderItem");
        ObjectNode orderItem = orderItems.addObject();
        ObjectNode instance = orderItem.putObject("instance");
        ObjectNode filterWarranties = instance.putObject("filterWarranties");
        ObjectNode filterData = filterWarranties.putObject("data");
        filterData.putArray("warranties"); // Empty array

        return root;
    }

    private JsonNode createEmptyChosenWarrantiesOrderJson() {
        ObjectNode root = objectMapper.createObjectNode();
        ObjectNode data = root.putObject("data");
        ArrayNode orderItems = data.putArray("orderItem");
        ObjectNode orderItem = orderItems.addObject();
        ObjectNode instance = orderItem.putObject("instance");
        ObjectNode chosenWarranties = instance.putObject("chosenWarranties");
        ObjectNode chosenData = chosenWarranties.putObject("data");
        chosenData.putArray("warranties"); // Empty array

        return root;
    }

    private List<WarrantyPremium> createMockPremiums() {
        it.yolo.entity.AnagWarranty anag1 = new it.yolo.entity.AnagWarranty();
        anag1.setId(1001L);
        anag1.setInternalCode("W001");
        
        it.yolo.entity.AnagWarranty anag2 = new it.yolo.entity.AnagWarranty();
        anag2.setId(1002L);
        anag2.setInternalCode("W002");

        WarrantyPremium premium1 = new WarrantyPremium("1000.00", 12, new BigDecimal("120.00"), "EUR", "EUR");
        premium1.setWarranty(anag1);
        
        WarrantyPremium premium2 = new WarrantyPremium("2000.00", 12, new BigDecimal("150.00"), "EUR", "EUR");
        premium2.setWarranty(anag2);

        return Arrays.asList(premium1, premium2);
    }
}
