package it.yolo.model.request;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerCareRequest {

    @JsonProperty("productId")
    private Long productId;


    @JsonProperty("packetId")
    private Long packetId;

    @JsonProperty("orderCode")
    private String orderCode;

    @JsonProperty("policyCode")
    private String policyCode;

    @JsonProperty("instance")
    private JsonNode instance;
    @JsonProperty("orderCode")
    public String getOrderCode() {
        return orderCode;
    }
    @JsonProperty("orderCode")
    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    @JsonProperty("instance")
    public JsonNode getInstance() {
        return instance;
    }
    @JsonProperty("instance")
    public void setInstance(JsonNode instance) {
        this.instance = instance;
    }

    @JsonProperty("productId")
    public Long getProductId() {
        return productId;
    }

    @JsonProperty("productId")
    public void setProductId(Long productId) {
        this.productId = productId;
    }

    @JsonProperty("packetId")
    public Long getPacketId() {
        return packetId;
    }

    @JsonProperty("packetId")
    public void setPacketId(Long packetId) {
        this.packetId = packetId;
    }


    @JsonProperty("policyCode")
    public String getPolicyCode() {
        return policyCode;
    }

    @JsonProperty("policyCode")
    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }
}
