package it.yolo.model.v2;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.model.CategoryReferenceBoundaryRequest;
import it.yolo.model.SplitBoundaryRequest;
import it.yolo.model.SurveyQuestionBoundaryRequest;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Schema(description = "Product data")
public class ProductBoundaryRequestV2 {

    @Getter
    @Setter
    @NotBlank
    @Schema(description = "Code")
    private String code;

    @Getter
    @Setter
    @Schema(description = "Description")
    private String description;

    @Getter
    @Setter
    @Schema(description = "Start date")
    private LocalDate startDate;

    @Getter
    @Setter
    @Schema(description = "End date")
    private LocalDate endDate;

    @Getter
    @Setter
    @Schema(description = "Whether this product is recurring")
    private boolean recurring;

    @Getter
    @Setter
    @Schema(description = "External ID")
    private String externalId;

    @Getter
    @Setter
    @Schema(description = "Splits")
    private List<SplitBoundaryRequest> splits;

    @Getter
    @Setter
    @Schema(description = "Product Asset")
    private JsonNode asset;

    @Getter
    @Setter
    @Schema(description = "Insurance company")
    private String insuranceCompany;

    @Valid
    @Getter
    @Setter
    @NotNull
    @Schema(description = "Survey questions")
    private List<SurveyQuestionBoundaryRequest> questions;

    @Valid
    @Getter
    @Setter
    @NotNull
    @Schema(description = "Categories")
    private List<CategoryReferenceBoundaryRequest> categories;

    @Getter
    @Setter
    @Schema(description = "Insurance Company Logo")
    private String insuranceCompanyLogo;

    @Getter
    @Setter
    @Schema(description = "Quotator type")
    private String quotatorType;

    @Getter
    @Setter
    @Schema(description = "product_type")
    private String productType;

    @Getter
    @Setter
    @Schema(description = "Legacy")
    private JsonNode legacy;

    @Getter
    @Setter
    @Schema(description = "Duration type")
    private String duration_type;
}
