package it.yolo.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;

public record WarrantyDetail(
    @JsonProperty("id")
    @NotNull(message = "Warranty ID cannot be null")
    Long id,

    @JsonProperty("name")
    String name,

    @JsonProperty("benefitAmount")
    @NotNull(message = "Benefit amount cannot be null")
    String benefitAmount,

    @JsonProperty("internalCode")
    String internalCode
) {}
