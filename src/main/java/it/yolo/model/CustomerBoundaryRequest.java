package it.yolo.model;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.List;

@Schema(description = "Customer data")
public class CustomerBoundaryRequest {

    @Getter
    @Setter
    @Schema(description = "ID")
    private Long id;


    @Getter
    @Setter
    @Schema(description = "Customer code")
    private String customer_code;

    @Getter
    @Setter
    private JsonNode external_code;

    @Getter
    @Setter
    private String username;

    @Getter
    @Setter
    private String name;


    @Getter
    @Setter
    private String surname;

    @Getter
    @Setter
    private String date_of_birth;

    @Getter
    @Setter
    private String birth_city;

    @Getter
    @Setter
    private String birth_country;

    @Getter
    @Setter
    private String tax_code;

    @Getter
    @Setter
    private String gender;

    @Getter
    @Setter
    private String street;

    @Getter
    @Setter
    private String street_number;

    @Getter
    @Setter
    private String education;

    @Getter
    @Setter
    private String salary;

    @Getter
    @Setter
    private String profession;

    @Getter
    @Setter
    private String birth_state;

    @Getter
    @Setter
    private String city;

    @Getter
    @Setter
    private String country;

    @Getter
    @Setter
    private String zip_code;

    @Getter
    @Setter
    private String primary_mail;


    @Getter
    @Setter
    private String secondary_mail;

    @Getter
    @Setter
    private String primary_phone;

    @Getter
    @Setter
    private String secondary_phone;

    @Getter
    @Setter
    private String language;

    @Getter
    @Setter
    private String company;


    @Getter
    @Setter
    private Boolean legal_form;

    @Getter
    @Setter
    private String ndg;

    @Getter
    @Setter
    private Integer city_id;

    @Getter
    @Setter
    private Long country_id;

    @Getter
    @Setter
    private Integer birth_state_id;

    @Getter
    @Setter
    private Integer birth_city_id;

    @Getter
    @Setter
    private Integer birth_country_id;

    @Getter
    @Setter
    private String state;

    @Getter
    @Setter
    private Long state_id;

    @Getter
    @Setter
    private String state_abbr;

    @Getter
    @Setter
    private String birth_state_abbr;

    @Getter
    @Setter
    private String id_card;


    @Getter
    @Setter
    List<UserAcceptancesBoundaryReferenceRequest> userAcceptances;

    @Getter
    @Setter
    private String retired_code;

    @Getter
    @Setter
    private String utm_source;

    @Getter
    @Setter
    private String vat_code;

    @Getter
    @Setter
    private String vatcode;

    @Getter
    @Setter
    private JsonNode registration_info;

    @Getter
    @Setter
    private String sdi_code;

    @Getter
    @Setter
    private String certified_mail;

    @Getter
    @Setter
    List<LegalRepresentativeInfoBoundaryReferenceRequest> legalRepresentativeInfo;

}
