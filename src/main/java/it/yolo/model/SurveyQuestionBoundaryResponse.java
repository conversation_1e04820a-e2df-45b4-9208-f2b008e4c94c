package it.yolo.model;

import java.util.List;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Schema(description = "Product-Question entity")
public class SurveyQuestionBoundaryResponse {

    @Getter
    @Setter
    @Schema(description = "ID")
    private Long id;

    @Getter
    @Setter
    @Schema(description = "Content")
    private String content;

    @Getter
    @Setter
    @Schema(description = "Position")
    private int position;

    @Getter
    @Setter
    @Schema(description = "External code")
    private String externalCode;

    @Getter
    @Setter
    @Schema(description = "Packet ID")
    private Long packet_id;

    @Getter
    @Setter
    @Schema(description = "rule")
    private JsonNode rule;

    @Getter
    @Setter
    @Schema(description = "Acceptable answers")
    private List<SurveyAnswerBoundaryResponse> answers;



    @Override
    public String toString() {
        return "{" +
                "\"id\":" + id +
                ", \"content\":\"" + content + "\"" +
                ", \"position\":" + position +
                ", \"externalCode\":\"" + externalCode + "\"" +
                ", \"answers\":" + answers +
                ", \"packet_id\":" + packet_id +
                ", \"rule\":" + rule +
                "}";
    }
}
