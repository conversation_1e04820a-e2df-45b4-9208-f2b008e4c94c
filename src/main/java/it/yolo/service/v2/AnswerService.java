package it.yolo.service.v2;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.primitives.Longs;
import io.opentelemetry.instrumentation.annotations.SpanAttribute;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.OrderClient;
import it.yolo.client.ProductClient;
import it.yolo.client.dto.QuestionDto;
import it.yolo.client.dto.SurveyAnswerDto;
import it.yolo.entity.AnswerEntity;
import it.yolo.entity.QuestionEntity;
import it.yolo.exception.EntityNotFoundException;
import it.yolo.repository.AnswerRepository;
import it.yolo.repository.QuestionRepository;
import it.yolo.service.client.v2.InvokeProductManager;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@RequestScoped
public class AnswerService {
    @Inject
    AnswerRepository repo;

    @Inject
    QuestionRepository questionRepository;

    @Inject
    InvokeProductManager productManagerClient;

    @Inject
    @RestClient
    OrderClient orderClient;

    @Inject
    @RestClient
    ProductClient productClient;

    @Transactional
    @WithSpan("AnswerService.createAnswer")
    public List<AnswerEntity> createAnswers(
            @SpanAttribute("arg.entity") List<AnswerEntity> entities,
            String token
    ) {
        repo.persist(entities);
        return entities;
    }

    @WithSpan("AnswerService.readAnswer")
    public Optional<AnswerEntity> readAnswer(
            @SpanAttribute("arg.id") long id,
            String token
    ) {
        return repo.findByIdOptional(id);
    }

    @WithSpan("AnswerService.readQuestion")
    public Optional<QuestionEntity> readQuestion(
            @SpanAttribute("arg.id") long id,
            String token
    ) {
        return questionRepository.findByIdOptional(id);
    }

    @Transactional
    @WithSpan("AnswerService.updateAnswer")
    public AnswerEntity updateAnswer(
            @SpanAttribute("arg.id") long id,
            @SpanAttribute("arg.entity") AnswerEntity entity,
            String token
    ) {
        AnswerEntity updating = repo.findByIdOptional(id).orElseThrow(() ->
                new EntityNotFoundException(String.format(
                        "Entity answer by id=%d not found", id)));

        // TODO
        repo.getEntityManager().merge(updating);
        return updating;
    }

    @Transactional
    @WithSpan("AnswerService.deleteAnswer")
    public void deleteAnswer(@SpanAttribute("arg.id") long id, String token) {
        repo.deleteById(id);
    }

    @WithSpan("AnswerService.listAnswers")
    public List<AnswerEntity> listAnswers(String token) {
        return repo.listAll();
    }

    @WithSpan("AnswerService.listAnswers")
    public List<AnswerEntity> listOrderAnswers(String order) {
        return repo.find("order_code", order).list();
    }

    public boolean answerChecks(List<AnswerEntity> productSurvey, String token, String sessionId) {
        ObjectMapper mapper = new ObjectMapper();
        String orderCode = productSurvey.stream().findFirst().get().getOrderCode();
        if (!token.contains("Bearer")) token = "Bearer " + token;
        JsonNode orderJson = orderClient.getOrder(token,sessionId,orderCode).readEntity(JsonNode.class);
        QuestionDto[] questions;
        try {
            questions = mapper.treeToValue(orderJson.get("data").get("product").get("data").withArray("questions"), QuestionDto[].class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        validateAnswers(productSurvey, orderJson, token);
        validateAnswersQuestionsValues(productSurvey, questions);
        validateAnswersCount(productSurvey, orderJson);
        return true;
    }

    private boolean validateAnswers(List<AnswerEntity> entities, JsonNode orderJson, String token) {
        if (productManagerClient.countProductQuestionsById(orderJson) != entities.size()) {
            return false;
        }
        for (AnswerEntity a : entities) {
//			if(getAnswerValueFromIadResource(a.getAnswerId(), token).equalsIgnoreCase("NO")) return false;
            /*
             * 2 cose:
             * 1 notazione alla yoda - non va in npe se il getAnswer etc restituisce null (anche se non lo fa)
             * 2 not si fa andare in fault anche se la risposta e' stringa vuota o un testo qualsiasi
             * (valori sporchi su db)
             */
            if ("prevent_checkout".equalsIgnoreCase(getRuleFromIadResource(a.getAnswerId(), orderJson, token)))
                return false;
        }

        return true;
    }

    private boolean validateAnswersQuestionsValues(List<AnswerEntity> entities, QuestionDto[] questions){
        HashMap<Long, List<Long>> map = productManagerClient.productQuestionAnswerMap(questions);
        for(AnswerEntity a : entities) {

            if(!map.get(a.getQuestionId()).contains(a.getAnswerId())){
                return true;
            }
        }

        return false;
    }

    private boolean validateAnswersCount(List<AnswerEntity> entities, JsonNode orderJson) {
        return productManagerClient.countProductQuestionsById(orderJson) == entities.size();
    }

    private boolean validateAnswersProductRelationship(List<AnswerEntity> entities, QuestionDto[] questions){ //passare product questions
        List<Long> allowedValues = Lists.newArrayList(Longs.asList(productManagerClient.getProductQuestionsIDs(questions)));
        List<Long> userValues = Lists.newArrayList(Longs.asList(entities.stream().mapToLong(AnswerEntity::getQuestionId).toArray()));
        userValues.removeAll(allowedValues);
        return userValues.isEmpty();
    }

    private boolean validateAnswersPacket(List<AnswerEntity> entities, String token,long productId,long packetId) {
        for(AnswerEntity e : entities) {
            QuestionEntity questionEntity = readQuestion(e.getQuestionId(), token).orElseThrow(() ->
                    new EntityNotFoundException(String.format(
                            "Entity question by id=%d not found", e.getQuestionId())));
            if (questionEntity.getPacketId() != null) {
                if (questionEntity.getPacketId() != packetId ) {
                    return false;
                }
            }
        }
        return true;
    }

    private String getRuleFromIadResource(Long answerId, JsonNode orderResponse, String token) {
        SurveyAnswerDto answer = productManagerClient.readById(answerId, orderResponse, token);
        if (answer != null && answer.getRule() != null) {
            return answer.getRule();
        }

        return "";
    }
}
