package it.yolo.service.client.V2;

import com.fasterxml.jackson.databind.JsonNode;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.adp.order.AdpOrderClient;
import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.order.dto.request.OrderRequestDto;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.client.order.v2.OrderClientV2;
import it.yolo.common.OrderStateEnum;
import it.yolo.exception.InvokeOrderEx;
import it.yolo.exception.OrderException;
import it.yolo.model.request.OrderManagerRequest;
import it.yolo.service.ServiceCustomer;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import javax.annotation.PostConstruct;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.json.JsonString;
import javax.ws.rs.core.Response;
import java.util.*;

import org.jboss.logging.Logger;

import static it.yolo.costants.Costant.ERROR_MSG;

@RequestScoped
public class InvokeInvokeOrderImplV2 implements InvokeOrderV2 {

    @Inject
    @RestClient
    OrderClientV2 orderClientV2;

    @Inject
    @RestClient
    AdpOrderClient adpOrderClient;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    ServiceCustomer serviceCustomer;

    Map<String, String> mapAnagStates;

    @Inject
    Logger logger;

    @PostConstruct
    void init() {
        mapAnagStates = new HashMap<>();
        mapAnagStates.put("insurance_info", OrderStateEnum.ELABORATION.getValue());
        mapAnagStates.put("address", OrderStateEnum.ELABORATION.getValue());
        mapAnagStates.put("survey", OrderStateEnum.ELABORATION.getValue());
        mapAnagStates.put("payment", OrderStateEnum.ELABORATION.getValue());
        mapAnagStates.put("confirm", OrderStateEnum.COMPLETED.getValue());
        mapAnagStates.put("completed", OrderStateEnum.COMPLETED.getValue());
    }

    @WithSpan("InvokeOrder.create")
    @Override
    public OrderResponseDto create(OrderManagerRequest request) {
        try {
            Response responseAdp = adpOrderClient.trasfomrRequest(request);

            Set<String> groups = extractGroupsCognito();
            String ndg=jsonWebToken.getClaim("username");
            String token = "Bearer " + jsonWebToken.getRawToken();
            OrderRequestDto requestOrder = responseAdp.readEntity(OrderRequestDto.class);
            if (!groups.contains("technical-users") || !groups.contains("anonimo") || !groups.contains("intermediary-anonimo")) {
                logger.info("Fetch customer data...");
                CustomerResponseDto customerResponse=serviceCustomer.findByNdg(token, ndg.toUpperCase());
                requestOrder.getDataDtoRequest().setCustomerId(customerResponse.getData().getId());
                requestOrder.getDataDtoRequest().setCustomerCode(customerResponse.getData().getCustomerCode());
            }
            Response responseOrder;
            try {
                responseOrder= orderClientV2.create(token,requestOrder);
            } catch (Exception e){
                throw new OrderException(e.getMessage());
            }
            OrderResponseDto orderResponseDto = responseOrder.readEntity(OrderResponseDto.class);
            return orderResponseDto;
        } catch (Exception ex) {
            throw new InvokeOrderEx(ERROR_MSG + "errore durante la chiamata al client order"
                    + ex.getCause().toString());
        }
    }

    @WithSpan("InvokeOrder.update")
    @Override
    public OrderResponseDto update(String id, OrderManagerRequest request) {
        try {
            request.setOrderCode(id);
            Response responseAdp = adpOrderClient.trasfomrRequest(request);
            OrderRequestDto requestOrder = responseAdp.readEntity(OrderRequestDto.class);
            requestOrder.getDataDtoRequest().setAnagState(mapAnagStates.get(request.getState()));
            if (request.getPayment_token()!=null) {
                requestOrder.getDataDtoRequest().setPaymentToken(request.getPayment_token());
            }
            if (request.getOrder().getFrequency()!=null) {
                requestOrder.getDataDtoRequest().setFrequency(request.getOrder().getFrequency());
            }
            if (request.getPaymentTransactionId()!=null) {
                requestOrder.getDataDtoRequest().setPaymentTransactionId(request.getPaymentTransactionId());
            }
            if(request.getPaymentType() != null){
                requestOrder.getDataDtoRequest().setPaymentType(request.getPaymentType());
            }
            //verifica codice sconto
            if(request.getDiscount()!=null){
                requestOrder.getDataDtoRequest().setDiscount(request.getDiscount());
            }
            if(request.getBillCustomerId()!=null) {
                requestOrder.getDataDtoRequest().setCustomerId(request.getBillCustomerId());
            }
            if(request.getAdditionalProperties().get("quotatorPrice")!=null) {
                requestOrder.getDataDtoRequest().getOrderItem().stream().forEach(orderItemRequest -> {
                    orderItemRequest.setPrice(request.getAdditionalProperties().get("quotatorPrice").toString());
                });
            }
            if(request.getAdditionalProperties().get("quotatorAddons")!=null) {
//                requestOrder.getDataDtoRequest().setCustomerId(request.getBillCustomerId());
            }
            if(request.getAdditionalProperties().get("quotatorPrice")!=null) {
                requestOrder.getDataDtoRequest().getOrderItem().stream().forEach(orderItemRequest -> {
                    orderItemRequest.setPrice(request.getAdditionalProperties().get("quotatorPrice").toString());
                });
            }
            if(request.getOrder().getLineItemsAttributes()!=null) {
                if(request.getOrder().getLineItemsAttributes().getNode().get("expiration_date")!=null){
                    requestOrder.getDataDtoRequest().getOrderItem().stream().forEach(orderItemRequest -> {
                        String exspire= request.getOrder().getLineItemsAttributes().getNode().get("expiration_date").asText();

                        if(exspire.contains("+")){
                            exspire=StringUtils.substringBefore(exspire,"+");
                        }

                        orderItemRequest.setExpirationDate(exspire);
                    });
                }
            }
            if(request.getAdditionalProperties().get("quotatorAddons")!=null) {
                requestOrder.getDataDtoRequest().getOrderItem().stream().forEach(orderItemRequest -> {
                    orderItemRequest.setQuotation((JsonNode) request.getAdditionalProperties().get("quotatorAddons"));
                });
            }
            String token = "Bearer " + jsonWebToken.getRawToken();
            Response responseOrder;
            try {
                responseOrder = orderClientV2.update(token,id,requestOrder);
            }catch (Exception e){
                throw new OrderException(e.getMessage());
            }
            OrderResponseDto orderResponseDto = responseOrder.readEntity(OrderResponseDto.class);
            return orderResponseDto;
        } catch (Exception ex) {
            throw new InvokeOrderEx(ERROR_MSG + "errore durante la chiamata al client order"
                    + ex.getCause().toString());
        }

    }

    public Set<String> extractGroupsCognito() {
        List<JsonString> groupsJson=jsonWebToken.getClaim("cognito:groups");
        Set<String> groups=new HashSet<>();
        for (JsonString group : groupsJson) {
            groups.add(group.getString());
        }
        return groups;
    }


}
