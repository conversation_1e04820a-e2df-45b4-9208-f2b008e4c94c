package it.yolo.service;

import it.yolo.client.product.ProductClient;
import it.yolo.client.product.response.ProductResponse;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.ClientWebApplicationException;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
public class ServiceProduct {

    private static final Logger LOGGER = Logger.getLogger(ServiceProduct.class);

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    @RestClient
    ProductClient productClient;

    public ProductResponse findById(Long productId) throws Exception {
        String token = "Bearer " + jsonWebToken.getRawToken();
        try(Response response = productClient.getProduct(token, productId)) {
            LOGGER.debugv("Product response status: {0}", response.getStatus());
            return response.readEntity(ProductResponse.class);
        } catch (ClientWebApplicationException ex) {
            String errorMessage = ex.getResponse().readEntity(String.class);
            LOGGER.errorv("Error response from product service: {0}", errorMessage);
            throw new Exception("errore durante la chiamata al servizio product : " + ex.getMessage());
        }
    }
}
