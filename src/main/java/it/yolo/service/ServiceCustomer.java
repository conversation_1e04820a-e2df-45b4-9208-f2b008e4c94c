package it.yolo.service;

import com.fasterxml.jackson.annotation.JsonProperty;
import it.yolo.client.customer.CustomerClient;
import it.yolo.client.customer.dto.CustomerResponseDto;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
public class ServiceCustomer {

    @Inject
    JsonWebToken jsonWebToken;

    @RestClient
    @Inject
    CustomerClient customerClient;

    public CustomerResponseDto findByNdg(){
        String ndg=jsonWebToken.getClaim("username");
        String token = "Bearer " + jsonWebToken.getRawToken();
        Response resCustomer= customerClient.findByNdg(token,ndg);
        return resCustomer.readEntity(CustomerResponseDto.class);
    }

    public CustomerResponseDto findByNdgNoAuth(String ndg){
        String token = "Bearer " + jsonWebToken.getRawToken();
        Response resCustomer= customerClient.findByNdgNoAuth(token,ndg);
        return resCustomer.readEntity(CustomerResponseDto.class);
    }

    public CustomerResponseDto findById(final Long id) {
        String token = "Bearer " + jsonWebToken.getRawToken();
        return customerClient.findById(token, id)
                .readEntity(CustomerResponseDto.class);
    }

}
