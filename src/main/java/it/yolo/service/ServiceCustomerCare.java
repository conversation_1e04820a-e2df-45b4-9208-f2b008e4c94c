package it.yolo.service;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.order.request.OrderRequestDto;
import it.yolo.client.order.response.OrderDtoResponse;
import it.yolo.client.policy.dto.PolicyDtoRequest;
import it.yolo.client.policy.response.PolicyResponse;
import it.yolo.client.price.request.PriceBoundaryRequest;
import it.yolo.client.price.request.PriceRequest;
import it.yolo.mapper.CustomerCareMapper;
import it.yolo.mapper.PriceMapper;
import it.yolo.model.request.CustomerCareRequest;
import it.yolo.model.response.CustomerCareResponse;
import it.yolo.model.response.RedirectTokenResponse;
import org.jboss.logging.Logger;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

@RequestScoped
public class ServiceCustomerCare {

    @Inject
    Logger logger;

    @Inject
    ServiceOrder serviceOrder;

    @Inject
    ServicePrice servicePrice;


    @Inject
    ServicePolicy servicePolicy;


    @Inject
    RedirectJwtService redirectJwtService;


    public CustomerCareResponse updateWarranties(CustomerCareRequest req) throws Exception {
        logger.info("ServiceCustomerCare.updateWarranties() start" + req);
        CustomerCareMapper customerCareMapper = new CustomerCareMapper();
        OrderRequestDto requestOrder = customerCareMapper.requestToOrderRequestDto(req);
        logger.info("ServiceCustomerCare.customerCareMapper trasformazione req in richiesta per order" + requestOrder);
        //creazione nuovo ordine --> chiamata alla order
        logger.info("ServiceCustomerCare.serviceOrder invoke service" + requestOrder);
        OrderDtoResponse orderDtoResponse = serviceOrder.duplicatedOrder(requestOrder);
        //quotation price --> chiamata al price
        PriceMapper priceMapper = new PriceMapper();
        PriceRequest priceRequest = priceMapper.orderResToPriceReq(orderDtoResponse);
        PriceBoundaryRequest request= new PriceBoundaryRequest(priceRequest);
        logger.info("ServiceCustomerCare.priceMapper trasformazione da OrderRes a priceReq " + priceRequest);
        //invocazione price per quotare il nuovo ordine
        logger.info("ServiceCustomerCare.servicePrice  invoke service  " + priceRequest);
        JsonNode res = servicePrice.quote(request);

        logger.info("ServiceCustomerCare.servicePrice map policy request " + res);
        PolicyDtoRequest policyReq = customerCareMapper.policyRequestToCustomerCareReq(req);
        policyReq.getDataPolicyRequest().setOrderIdCode(orderDtoResponse.getData().getId());
        orderDtoResponse.getData().getOrderItem().forEach(item -> {
            if (req.getProductId() == item.getProductId().intValue()) {
                policyReq.getDataPolicyRequest().setOrderItemId(item.getId());
            }
        });

        logger.info("ServiceCustomerCare.servicePolicy invoke service");
        PolicyResponse policyRes = servicePolicy.saveByUpdatedWarranties(policyReq);

        CustomerCareResponse customerCareResponse = new CustomerCareResponse.CustomerCareResponseBuilder()
                .setOrderDtoResponse(orderDtoResponse)
                .setQuote(res)
                .setPolicy(policyRes)
                .build();
        logger.info("ServiceCustomerCare end with response " + customerCareResponse);
        return customerCareResponse;
    }

    public RedirectTokenResponse duplicateOrderAndCreateRedirectToken(String authToken, String orderId) throws Exception {
        logger.info("ServiceCustomerCare.duplicateOrderAndCreateRedirectToken() start with orderId: " + orderId);

        try {
            // 1. Recupera l'ordine originale
            OrderDtoResponse originalOrder = serviceOrder.findById(Long.valueOf(orderId));

            // 2. Duplica l'ordine usando la logica esistente
            CustomerCareMapper customerCareMapper = new CustomerCareMapper();
            OrderRequestDto duplicateRequest = customerCareMapper.orderToDuplicateRequest(originalOrder);

            logger.info("ServiceCustomerCare.duplicateOrderAndCreateRedirectToken() calling order duplication");
            OrderDtoResponse newOrder = serviceOrder.duplicatedOrder(duplicateRequest);

            // 3. Estrai il token JWT dall'header (rimuovi "Bearer ")
            String originalJwt = authToken.startsWith("Bearer ") ? authToken.substring(7) : authToken;

            // 4. Crea il redirect token
            String redirectToken = redirectJwtService.createRedirectJwt(
                    originalJwt,
                    newOrder.getData().getOrderCode(),
                    originalOrder.getData().getOrderCode()
            );

            // 5. Costruisci la response
            RedirectTokenResponse response = new RedirectTokenResponse();
            response.setRedirectToken(redirectToken);
            response.setNewOrderCode(newOrder.getData().getOrderCode());
            response.setExpiresAt(redirectJwtService.getExpirationDateTime());

            logger.info("ServiceCustomerCare.duplicateOrderAndCreateRedirectToken() completed successfully");
            return response;

        } catch (Exception e) {
            logger.error("ServiceCustomerCare.duplicateOrderAndCreateRedirectToken() error: " + e.getMessage());
            throw new Exception("Error during order duplication and token creation: " + e.getMessage());
        }
    }
}
