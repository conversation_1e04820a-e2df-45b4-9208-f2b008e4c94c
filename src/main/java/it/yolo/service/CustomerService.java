package it.yolo.service;

import be.digitech.iamutility.model.Credentials;
import be.digitech.iamutility.service.IAMUtility;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.opentelemetry.instrumentation.annotations.SpanAttribute;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.quarkus.hibernate.orm.panache.PanacheQuery;
import io.quarkus.logging.Log;
import io.quarkus.security.UnauthorizedException;
import io.smallrye.common.annotation.Blocking;
import it.yolo.client.address.gateway.AddressClient;
import it.yolo.client.address.gateway.model.AddressGatewayResponse;
import it.yolo.client.assimoco.ArssoClient;
import it.yolo.client.comunicationManager.CommunicationManagerClient;
import it.yolo.client.iadUtility.UtilityClient;
import it.yolo.common.Utility;
import it.yolo.entity.CustomerEntity;
import it.yolo.entity.FlagsEntity;
import it.yolo.entity.LegalRepresentativeInfoEntity;
import it.yolo.entity.UserAcceptancesEntity;
import it.yolo.exception.EntityNotFoundException;
import it.yolo.mapper.MapperCustomer;
import it.yolo.model.ArssoRequest;
import it.yolo.model.CustomerBoundaryRequest;
import it.yolo.model.LegalRepresentativeInfoBoundaryReferenceRequest;
import it.yolo.model.UserAcceptancesBoundaryReferenceRequest;
import it.yolo.model.communicationManager.dto.CommunicationManagerDtoRequest;
import it.yolo.model.communicationManager.dto.TemplatePlaceholder;
import it.yolo.records.CustomerRecordRequest;
import it.yolo.records.CustomersPaginated;
import it.yolo.repository.CardRepository;
import it.yolo.repository.CustomerRepository;
import it.yolo.repository.FlagsRepository;
import it.yolo.repository.UserAcceptancesRepository;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.hibernate.exception.ConstraintViolationException;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.json.JsonString;
import javax.persistence.PersistenceException;
import javax.transaction.Transactional;
import javax.ws.rs.core.Response;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@ApplicationScoped
public class CustomerService {

    @Inject
    CustomerRepository customerRepo;


    @Inject
    FlagsRepository flagsRepository ;

    @Inject
    CardRepository cardRepo;

    @Inject
    @RestClient
    ArssoClient arssoClient;

    @Inject
    @RestClient
    UtilityClient utilityClient;

    @Inject
    @RestClient
    AddressClient addressClient;

    ArssoRequest arssoRequest;

    @Inject
    IAMUtility iamUtility;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    UserAcceptancesRepository userAcceptancesRepository;

    @Inject
    UserAcceptancesService  userAcceptancesService;


    @ConfigProperty(name = "default.itemTotal.pagination")
    int itemTotalConf;

    @ConfigProperty(name = "ndg.techincalUser")
    String techincalUser;

    @ConfigProperty(name = "intermediary.users")
    String intermediaryUser;

    @ConfigProperty(name = "technical.users")
    String technicalUser;



    @Inject
    Logger log;

    @Inject
    @RestClient
    CommunicationManagerClient communicationManagerClient;

    @ConfigProperty(name="registration.mail.key")
    String registrationMailKey;

    @ConfigProperty(name="registration.mail.privateAreaUrl")
    String privateAreaUrl;

    @Transactional
    @WithSpan("CustomerService.createCustomer")
    public CustomerEntity createCustomer(
            @SpanAttribute("arg.entity") CustomerEntity entity, List<UserAcceptancesBoundaryReferenceRequest> reqUserAcceptances) throws Exception {
        Log.infof("customer service : create new customer, entity=%s, reqUserAcceptances=%s", entity, reqUserAcceptances);
        log.info("CustomerService.createCustomer() started...");
        String ndg=entity.getNdg()!=null?entity.getNdg():null;
        String email=entity.getPrimary_mail()!=null?entity.getPrimary_mail():null;

        log.info("CustomerService.createCustomer() ricerca per email o ndg " +  ndg + email);
        CustomerEntity customerEntity= this.findByNdgOrEmail(ndg,email);
        log.info("CustomerService.createCustomer() result db " +  ndg + email+ customerEntity);

        //check se abbiamo il nodo registration, quindi siamo in registrazione utente . Se si remove della pass.
        if(entity.getRegistration_info()!=null && entity.getRegistration_info().get("password")!=null){
            Log.info("we are in the user registration (entity's registration_info not null), remove password...");
            ((ObjectNode) entity.getRegistration_info()).remove("password");
        }

        /*
         introdotta questa logica siccome per gli altri tenant la userName cioe il codice fiscale viene settato sia nella ndg,
         sia nel taxCode. Per Itas il funnel può essere anonimo . La richiesta che arriva alla customer sarà una username,ndg,taxCode autogenerati.
         verifico quindi che il codice fiscale sia effettivamente il codice fiscale cosi da autocalcolare il gender.
         */
        if(entity.getGender()==null && entity.getTax_code()!=null && entity.getTax_code().length()<=16){
            Log.info("management of anonymous users for itas : entity's gender is null, tax code is not null, and tax code length is <= 16: calculate the gender for entity...");
            entity.setGender(Integer.valueOf(entity.getTax_code().charAt(9)+""+entity.getTax_code().charAt(10)) < 32 ? "Male" : "Female");
        }
        Log.infof("entity's gender is %s", entity.getGender());

        // verifica se arriva la userName, se non arriva per Itas va generata
        String userName= Utility.generateUIID();
        if(entity.getUsername()==null){
            entity.setUsername(userName);
        }
        Log.infof("entity's username is %s", entity.getUsername());

        //se non mi arriva l'ndg diretto come parametro , salvo io in quanto per FCA,TIM arriva all'interno della userName.
        //Itas/Yolo non arriva quindi , se non arriva , calcoliamo prima la userName , dopo aver calcolato quella settiamo la Ndg con la userName calcolata
        entity.setNdg(entity.getUsername().toUpperCase());
        Log.infof("entity's ndg is %s", entity.getNdg());

        if(entity.getCustomer_code()==null){
            entity.setCustomer_code(userName);
        }
        Log.infof("entity's customer code is %s", entity.getCustomer_code());

        if(customerEntity == null) {
            Log.info("customerEntity (returned by findByNdgOrEmail()) is null: entity's username and tax code will be set with the entity's username in UPPERCASE");
           entity.setUsername(entity.getUsername().toUpperCase());
           entity.setTax_code(entity.getTax_code()!=null ? entity.getTax_code().toUpperCase() : null);
            Log.info("customer repo : persist the entity");
            customerRepo.persist(entity);
            if(entity.getUserAcceptances()!=null){
                Log.info("entity's user acceptances are not null : customer service's flagToAcceptancesCreate()...");
                List<UserAcceptancesEntity> userAcceptancesEntities= flagToAcceptancesCreate(entity,reqUserAcceptances);
                entity.setUserAcceptances(userAcceptancesEntities);
            }
        }else {
            log.info("CustomerService.createCustomer() utente già censito con ndg o email " + customerEntity.getNdg() + customerEntity.getPrimary_mail());
            Log.infof("customer service : return customerEntity %s", customerEntity);
            return customerEntity;
            //entity=this.updateCustomer(entity, entity.getTax_code(), true);
        }
        log.info("CustomerService.createCustomer() end...");
        Log.infof("customer service : return entity %s", entity);
        return entity;
    }

    @WithSpan("CustomerService.readCustomer")
    public Optional<CustomerEntity> readCustomer(
            @SpanAttribute("arg.id") long id) {
        Log.infof("customer service : read customer by id=%d", id);
        Log.info("customer repo : find customer by id optional");
        return customerRepo.findByIdOptional(id);
    }

    @WithSpan("CustomerService.readCustomer")
    public CustomerEntity readCustomerByTaxCode(
            @SpanAttribute("arg.tax_code") String taxCode) {
        Log.infof("customer service : read customer by taxCode=%s", taxCode);
        CustomerEntity entity;
        log.info("CustomerService.readCustomerByTaxCode() started...");
        entity = customerRepo.find("tax_code", taxCode).firstResult();
        log.info("CustomerService.readCustomerByTaxCode() end...");

        Log.info("customer repo : find customer by tax_code and then return it...");
        return customerRepo.find("tax_code", taxCode).firstResult();
    }

    @WithSpan("CustomerService.readCustomer")
    public List<Long> readTaxCodeAndEmailById(@SpanAttribute("arg.tax_code") Long id) {
        log.info("readTaxCodeAndEmailById() start, id: "+id);
        List<CustomerEntity> entitiesTaxCode, entitiesEmail;
        Set<Long> ids=new HashSet<>();
        CustomerEntity customer=customerRepo.findById(id);
        entitiesTaxCode=customerRepo.find("tax_code", customer.getTax_code()).list();
        entitiesEmail=customerRepo.find("primary_mail", customer.getPrimary_mail()).list();
        if(entitiesTaxCode!=null){
            entitiesTaxCode.forEach(e->ids.add(e.getId()));
        }
        if (entitiesEmail!=null) {
            entitiesEmail.forEach(e -> ids.add(e.getId()));
        }
        log.info("CustomerService.readTaxCodeAndEmailById() end...");
        log.info("Customer ids found: "+ids.size());
        return ids.stream().toList();
    }

    @WithSpan("CustomerService.readCustomer")
    public List<Long> readTaxCodeById(@SpanAttribute("arg.tax_code") Long id) {
        log.info("readTaxCodeAndEmailById() start, id: "+id);
        List<CustomerEntity> entitiesTaxCode, entitiesEmail;
        Set<Long> ids=new HashSet<>();
        CustomerEntity customer=customerRepo.findById(id);
        entitiesTaxCode=customerRepo.find("tax_code", customer.getTax_code()).list();
        if(entitiesTaxCode!=null){
            entitiesTaxCode.forEach(e->ids.add(e.getId()));
        }
        log.info("CustomerService.readTaxCodeAndEmailById() end...");
        log.info("Customer ids found: "+ids.size());
        return ids.stream().toList();
    }

    @WithSpan("CustomerService.readCustomer")
    public CustomerEntity readCustomerById(
            @SpanAttribute("arg.id") Long id) {
        Log.infof("customer service : read customer by id=%d", id);
        CustomerEntity entity;
        log.info("ProductService.readCustomerById() started...");
        Log.info("customer repo : find customer by id");
        entity = customerRepo.find("id", id).firstResult();
        log.info("CustomerService.readCustomerById() end...");
        Log.info("customer service : return customer...");
        return getCountryCode(entity);
    }


    @Transactional
    @WithSpan("CustomerService.updateCustomerById")
    public CustomerEntity updateCustomerById(
            @SpanAttribute("arg.entity") CustomerEntity entity
    ) throws Exception {
        Log.infof("customer service : update customer by id, entity=%s", entity);

        Log.infof("customer repo : find customer (customerBeforeUpdate) by entity's id=%d", entity.getId());
        CustomerEntity customerBeforeUpdate = customerRepo.find("id", entity.getId()).firstResult();

        if(customerBeforeUpdate == null){
            throw new EntityNotFoundException("Entity with id: " +entity.getId() + " not found");
        }

        Log.infof("customer (customerBeforeUpdate) retrieved from DB is not null : set its fields with values contained in the entity=%s", entity);
        if (entity.getId() != null)
            customerBeforeUpdate.setId(entity.getId());
        if (entity.getBirth_city_id() != null)
            customerBeforeUpdate.setBirth_city_id(entity.getBirth_city_id());
        if (entity.getBirth_city() != null)
            customerBeforeUpdate.setBirth_city(entity.getBirth_city());
        if (entity.getBirth_country() != null)
            customerBeforeUpdate.setBirth_country(entity.getBirth_country());
        if (entity.getBirth_country_id() != null)
            customerBeforeUpdate.setBirth_country_id(entity.getBirth_country_id());
        if (entity.getBirth_state() != null)
            customerBeforeUpdate.setBirth_state(entity.getBirth_state());
        if (entity.getBirth_state_id() != null)
            customerBeforeUpdate.setBirth_state_id(entity.getBirth_state_id());
        if (entity.getDate_of_birth() != null) {
            Utility.checkAdultAge(entity.getDate_of_birth());
            customerBeforeUpdate.setDate_of_birth(entity.getDate_of_birth());
        }
        if (entity.getCity() != null)
            customerBeforeUpdate.setCity(entity.getCity());
        if (entity.getCountry_id() != null)
            customerBeforeUpdate.setCountry_id(entity.getCountry_id());
        if (entity.getCountry() != null)
            customerBeforeUpdate.setCountry(entity.getCountry());
        if (entity.getState() != null)
            customerBeforeUpdate.setState(entity.getState());
        if (entity.getState_id() != null)
            customerBeforeUpdate.setState_id(entity.getState_id());
        if (entity.getState_abbr() != null)
            customerBeforeUpdate.setState_abbr(entity.getState_abbr());
        if (entity.getEducation() != null)
            customerBeforeUpdate.setEducation(entity.getEducation());
        if (entity.getSalary() != null)
            customerBeforeUpdate.setSalary(entity.getSalary());
        if (entity.getProfession() != null)
            customerBeforeUpdate.setProfession(entity.getProfession());
        if (entity.getName() != null)
            customerBeforeUpdate.setName(entity.getName());
        if (entity.getSurname() != null)
            customerBeforeUpdate.setSurname(entity.getSurname());
        if (entity.getStreet() != null)
            customerBeforeUpdate.setStreet(entity.getStreet());
        if (entity.getStreet_number() != null)
            customerBeforeUpdate.setStreet_number(entity.getStreet_number());
        if (entity.getPrimary_mail() != null)
            customerBeforeUpdate.setPrimary_mail(entity.getPrimary_mail());
        if (entity.getSecondary_mail() != null)
            customerBeforeUpdate.setSecondary_mail(entity.getSecondary_mail());
        if (entity.getPrimary_phone() != null)
            customerBeforeUpdate.setPrimary_phone(entity.getPrimary_phone());
        if (entity.getSecondary_phone() != null)
            customerBeforeUpdate.setSecondary_phone(entity.getSecondary_phone());
        if (entity.getZip_code() != null)
            customerBeforeUpdate.setZip_code(entity.getZip_code());
        if (entity.getBirth_state_abbr() != null)
            customerBeforeUpdate.setBirth_state_abbr(entity.getBirth_state_abbr());
        if (entity.getId_card() != null)
            customerBeforeUpdate.setId_card(entity.getId_card());
        if (entity.getCity_id() != null)
            customerBeforeUpdate.setCity_id(entity.getCity_id());
        if (entity.getGender() != null)
            customerBeforeUpdate.setGender(entity.getGender());
        if (entity.getLanguage() != null)
            customerBeforeUpdate.setLanguage(entity.getLanguage());
        if (entity.getLegal_form() != null)
            customerBeforeUpdate.setLegal_form(entity.getLegal_form());
        if (entity.getTax_code() != null)
            customerBeforeUpdate.setTax_code(entity.getTax_code());
        if (entity.getNdg() != null)
            customerBeforeUpdate.setNdg(entity.getNdg());
        Log.info("customer repo : update customerBeforeUpdate into DB...");
        return customerRepo.getEntityManager().merge(customerBeforeUpdate);
    }






    @Transactional
    @WithSpan("CustomerService.updateCustomer")
    public CustomerEntity updateCustomer(
            @SpanAttribute("arg.entity") CustomerEntity entity,
            @SpanAttribute("arg.taxCode") String taxCode,
            @SpanAttribute("arg.updNdg") boolean updNdg
    ) throws Exception {
        Log.infof("customer service : update customer by tax code, taxCode=%s, updNdg=%b, entity=%s", taxCode, updNdg, entity);

        Log.info("customer repo : find customer (customerBeforeUpdate) by tax code");
        CustomerEntity customerBeforeUpdate = customerRepo.find("tax_code", taxCode).firstResult();

        if(customerBeforeUpdate == null){
            throw new EntityNotFoundException("Entity with tax_code: " +entity.getTax_code() + " not found");
        }
        Utility.canUpdateCustomer(customerBeforeUpdate);
        Log.infof("customer (customerBeforeUpdate) retrieved from DB is not null : set its fields with values contained in the entity=%s", entity);
        if (entity.getBirth_city_id() != null)
            customerBeforeUpdate.setBirth_city_id(entity.getBirth_city_id());
        if (entity.getBirth_city() != null)
            customerBeforeUpdate.setBirth_city(entity.getBirth_city());
        if (entity.getBirth_country() != null)
            customerBeforeUpdate.setBirth_country(entity.getBirth_country());
        if (entity.getBirth_country_id() != null)
            customerBeforeUpdate.setBirth_country_id(entity.getBirth_country_id());
        if (entity.getBirth_state() != null)
            customerBeforeUpdate.setBirth_state(entity.getBirth_state());
        if (entity.getBirth_state_id() != null)
            customerBeforeUpdate.setBirth_state_id(entity.getBirth_state_id());
        if (entity.getDate_of_birth() != null) {
            Utility.checkAdultAge(entity.getDate_of_birth());
            customerBeforeUpdate.setDate_of_birth(entity.getDate_of_birth());
        }
        if (entity.getCity() != null)
            customerBeforeUpdate.setCity(entity.getCity());
        if (entity.getCountry_id() != null)
            customerBeforeUpdate.setCountry_id(entity.getCountry_id());
        if (entity.getCountry() != null)
            customerBeforeUpdate.setCountry(entity.getCountry());
        if (entity.getState() != null)
            customerBeforeUpdate.setState(entity.getState());
        if (entity.getState_id() != null)
            customerBeforeUpdate.setState_id(entity.getState_id());
        if (entity.getState_abbr() != null)
            customerBeforeUpdate.setState_abbr(entity.getState_abbr());
        if (entity.getEducation() != null)
            customerBeforeUpdate.setEducation(entity.getEducation());
        if (entity.getSalary() != null)
            customerBeforeUpdate.setSalary(entity.getSalary());
        if (entity.getProfession() != null)
            customerBeforeUpdate.setProfession(entity.getProfession());
        if (entity.getName() != null)
            customerBeforeUpdate.setName(entity.getName());
        if (entity.getSurname() != null)
            customerBeforeUpdate.setSurname(entity.getSurname());
        if (entity.getStreet() != null)
            customerBeforeUpdate.setStreet(entity.getStreet());
        if (entity.getStreet_number() != null)
            customerBeforeUpdate.setStreet_number(entity.getStreet_number());
        if (entity.getPrimary_mail() != null)
            customerBeforeUpdate.setPrimary_mail(entity.getPrimary_mail());
        if (entity.getSecondary_mail() != null)
            customerBeforeUpdate.setSecondary_mail(entity.getSecondary_mail());
        if (entity.getPrimary_phone() != null)
            customerBeforeUpdate.setPrimary_phone(entity.getPrimary_phone());
        if (entity.getSecondary_phone() != null)
            customerBeforeUpdate.setSecondary_phone(entity.getSecondary_phone());
        if (entity.getZip_code() != null)
            customerBeforeUpdate.setZip_code(entity.getZip_code());
        if (entity.getBirth_state_abbr() != null)
            customerBeforeUpdate.setBirth_state_abbr(entity.getBirth_state_abbr());
        if (entity.getId_card() != null)
            customerBeforeUpdate.setId_card(entity.getId_card());
        if (entity.getCity_id() != null)
            customerBeforeUpdate.setCity_id(entity.getCity_id());
        if (entity.getGender() != null)
            customerBeforeUpdate.setGender(entity.getGender());
        if (entity.getLanguage() != null)
            customerBeforeUpdate.setLanguage(entity.getLanguage());
        if (entity.getLegal_form() != null)
            customerBeforeUpdate.setLegal_form(entity.getLegal_form());
        if (entity.getTax_code() != null)
            customerBeforeUpdate.setTax_code(entity.getTax_code());
        if (updNdg && entity.getNdg() != null)
            customerBeforeUpdate.setNdg(entity.getNdg());
        Log.info("customer repo : update customerBeforeUpdate into DB...");
        return customerRepo.getEntityManager().merge(customerBeforeUpdate);
    }

    @Transactional
    @WithSpan("CustomerService.deleteCustomer")
    public boolean deleteCustomer(
            @SpanAttribute("arg.id") long id) {
        Log.infof("customer service : delete customer by id=%d", id);
        Log.info("card repo : delete card by customer id");
        cardRepo.delete("customer.id", id);
        Log.info("customer repo : delete customer by id");
        return customerRepo.deleteById(id);
    }

    @WithSpan("CustomerService.listCustomers")
    public List<CustomerEntity> listCustomers() {
        Log.info("customer service : list customers");

        Log.info("customer repo : list all customers");
        return customerRepo.listAll();
    }

    @WithSpan("CustomerService.customersPagination")
    public CustomersPaginated customersPagination(
            @SpanAttribute("pageIndex") int pageIndex,
            @SpanAttribute("itemTotal") int  itemTotal) {

        log.info("CustomerService.customersPagination() start with : " + "pageIndex " + pageIndex + "itemTotal" + itemTotal);
        //se non possiedo un numero massimo di entity da visualizzare di default lo setto a 50
        itemTotal= itemTotal==0?itemTotalConf:itemTotal;
        //ricerca dei customer by id ordine ascedente
        Log.info("customer repo : find distinct customers order by id ascending...");
        PanacheQuery<CustomerEntity> query =customerRepo.find("select distinct c from CustomerEntity c order by c.id  asc");
        //paginazione
        List<CustomerEntity> listPaginated= query.page(pageIndex,itemTotal).list();
        log.info("CustomerService.customersPagination() start query pagination  " + "pageIndex " + pageIndex + "itemTotal" + itemTotal);
        //calcolo delle pagineTotali
        int numberTotalPage = query.pageCount()-1;
        Long itemTotalInPage = listPaginated.stream().count();
        CustomersPaginated customersPaginated =
                new CustomersPaginated(listPaginated,// list of customer
                                            (int) query.count(), //totalRecord
                                            pageIndex, //NumberCurrentPage
                                            numberTotalPage, //numberTotalPage
                                            itemTotal, //itemTotal
                                            itemTotalInPage.intValue());//itemTotalInPage
        log.info("CustomerService.customersPagination() end " + "customerPaginated " + customersPaginated);
        Log.info("customer service : return the paginated customers...");
        return customersPaginated;
    }




    @Transactional
    public CustomerEntity createCustomerAssimoco(CustomerEntity customerEntity) {
        Log.infof("customer service : create customer assimoco, customerEntity=%s", customerEntity);
        Log.info("customer repo : find customer by customerEntity's customer_code");
        PanacheQuery<CustomerEntity> findFromDb = customerRepo.find("customer_code", customerEntity.getCustomer_code());

        if (findFromDb.count() == 0) {
            Log.info("no entities found... set customerEntity's id to null and persist into DB");
            customerEntity.setId(null);
            customerRepo.persist(customerEntity);
        } else {
            Log.info("result found");
            CustomerEntity findFromDbResult = findFromDb.firstResult();
            Log.info("set customerEntity's id and createdAt fields with values of customer retrieved from DB...");
            customerEntity.setId(findFromDbResult.getId());
            customerEntity.setCreatedAt(findFromDbResult.getCreatedAt());
            Log.info("customer repo : update customerEntity into DB");
            customerEntity = customerRepo.getEntityManager().merge(customerEntity);
        }

        Log.info("customer repo : find customer (find the created or updated customer) by customerEntity's customer_code...");
        return customerRepo.find("customer_code", customerEntity.getCustomer_code()).firstResult();
    }

    @WithSpan("CustomerService.callArssoClient")
    public void callArssoClient(String cf){
        Log.infof("customer service : call arsso client, cf=%s", cf);
        Log.info("create a new arsso request and set its fields (codice fiscale and timestamp) with cf and the current date-time...");
        arssoRequest = new ArssoRequest();
        arssoRequest.setCodiceFiscale(cf);
        arssoRequest.setTimestamp(LocalDateTime.now());
        Log.info("call arsso client passing the arsso request, POST ar-sso/assimoco");
        arssoClient.callArssoClient(arssoRequest);
    }

    @WithSpan("CustomerService.findByNdg")
    @Transactional
    public CustomerEntity findByNdg(
            @SpanAttribute("arg.ndg") String ndg) throws EntityNotFoundException {
        Log.infof("customer service : find by ndg=%s", ndg);
        Log.info("customer repo : find customer by ndg (UPPERCASE)");
        CustomerEntity entity=customerRepo.find("UPPER(ndg)", ndg).firstResult();
        if (entity==null){
            Log.error("entity is null, throwing exception...");
            throw new EntityNotFoundException("Anagrafica non trovata");
        }

        // Filter the userAcceptances, keeping only those with active FlagsEntity (where deleted_at is null).
        entity.getUserAcceptances().removeIf(ua -> ua.getFlag().getDeleted_at() != null);

        // Valorizza il tag esterno di UserAcceptance con il tag presente in FlagEntity
        entity.getUserAcceptances().forEach(ua -> {
            if (ua.getFlag() != null && ua.getFlag().getTag() != null) {
                ua.setTag(ua.getFlag().getTag());
            }
        });
        
        entity.getLegalRepresentativeInfo().size();
        log.info("CustomerService.readCustomerByTaxCode() end...");
        Log.info("customer service : return customer");
        return getCountryCode(entity);
    }


    @WithSpan("CustomerService.findByNdgTechnical")
    public CustomerEntity findByNdgTechnical(
            @SpanAttribute("arg.ndg") String ndg) {
        Log.infof("customer service : find by ndg technical, ndg=%s", ndg);
        checkTokenTI();
        Log.info("customer repo : find customer by ndg (UPPERCASE)");
        CustomerEntity entity = customerRepo.find("UPPER(ndg)", ndg).firstResult();
        if (entity == null) {
            Log.error("entity is null, throwing exception...");
            throw new EntityNotFoundException("Anagrafica non trovata");
        }
        Log.info("customer service : return customer");
        return entity;
    }

    @WithSpan("CustomerService.findByEmailTechnical")
    @Transactional
    public CustomerEntity findByEmailTechnical(
            @SpanAttribute("arg.ndg") String email) {
        Log.infof("customer service : find by email technical, email=%s", email);
        checkTokenTI();
        Log.info("customer repo : find customer by primary_mail");
        CustomerEntity entity = customerRepo.find("primary_mail", email).firstResult();
        if (entity == null) {
            Log.error("entity is null, throwing exception...");
            throw new EntityNotFoundException("Anagrafica non trovata");
        }
        Log.info("customer service : return customer");
        return entity;

    }

    @WithSpan("CustomerService.findByToken")
    public CustomerEntity findByToken() throws EntityNotFoundException {
        Log.info("customer service : find by token");
        String ndg=jsonWebToken.getClaim("username").toString().toUpperCase();
        Log.infof("ndg (username from jwt) is %s", ndg);
        Log.info("customer repo : find customer by ndg (UPPERCASE)");
        CustomerEntity entity=customerRepo.find("UPPER(ndg)", ndg).firstResult();
        if (entity==null){
            Log.error("entity is null, throwing exception...");
            throw new EntityNotFoundException("Anagrafica non trovata");
        }
        log.info("CustomerService.readCustomerByTaxCode() end...");
        Log.info("customer service : return customer");
        return entity;
    }

    @Transactional
    @WithSpan("CustomerService.updateCustomer")
    public CustomerEntity updateCustomerByNdg(
            @SpanAttribute("arg.entity") CustomerRecordRequest request,
            @SpanAttribute("arg.taxCode") String ndg
    ) throws Exception {
        Log.infof("customer service : update customer by ndg, request=%s, ndg=%s", request, ndg);
        Log.info("customer repo : find customer (customerBeforeUpdate) by ndg (UPPERCASE)");
        CustomerEntity customerBeforeUpdate = customerRepo.find("UPPER(ndg)", ndg).firstResult();
        if(customerBeforeUpdate == null){
            Log.error("entity (customerBeforeUpdate) is null, throwing exception...");
            throw new EntityNotFoundException("Entity with tax_code: " +ndg + " not found");
        }
        Log.info("retrieved customerBeforeUpdate is not null");

        Utility.canUpdateCustomer(customerBeforeUpdate);

        //keep track of the customer residence change date
        if (residenceFieldsChanged(customerBeforeUpdate, request)) customerBeforeUpdate.setAddressChangeDate(LocalDateTime.now());

        Log.info("if request object's fields are not null, set the fields of the customerBeforeUpdate with that values, before update into DB...");
        if(request.firstname()!=null){
            customerBeforeUpdate.setName(request.firstname());
        }

        if(request.firstname()!=null){
            customerBeforeUpdate.setName(request.firstname());
        }
        if(request.lastname()!=null){
            customerBeforeUpdate.setSurname(request.lastname());
        }
        if(request.primary_phone()!=null){
            customerBeforeUpdate.setPrimary_phone(request.primary_phone());
        }
        if(request.primary_mail()!=null){
            customerBeforeUpdate.setPrimary_mail(request.primary_mail());
        }
        if(request.zip_code()!=null){
            customerBeforeUpdate.setZip_code(request.zip_code());
        }
        if(request.birth_city()!=null){
            customerBeforeUpdate.setBirth_city(request.birth_city());
        } else if (request.birth_country() != null) {
            customerBeforeUpdate.setBirth_city(request.birth_country());
        }
        if(request.birth_city_id()!=null){
            customerBeforeUpdate.setBirth_city_id(Integer.valueOf(request.birth_city_id()));
        }

        if(request.birth_state()!=null){
            customerBeforeUpdate.setBirth_state(request.birth_state());
        } else if (request.birth_country() != null) {
            customerBeforeUpdate.setBirth_state(request.birth_country());
        }
        if(request.birth_state_id()!=null){
            customerBeforeUpdate.setBirth_state_id(Integer.valueOf(request.birth_state_id()));
        }
        if(request.birth_country_id()!=null){
            customerBeforeUpdate.setBirth_country_id(Integer.valueOf(request.birth_country_id()));
        }
        if(request.city()!=null){
            customerBeforeUpdate.setCity(request.city());
        }
        if(request.street()!=null){
            customerBeforeUpdate.setStreet(request.street());
        }
        if(request.street_number()!=null){
            customerBeforeUpdate.setStreet_number(request.street_number());
        }
        if(request.city_id()!=null){
            customerBeforeUpdate.setCity_id(Integer.valueOf(request.city_id()));
        }
        if(request.state_id()!=null){
            customerBeforeUpdate.setState_id(Long.valueOf(request.state_id()));
        }
        if(request.state()!=null){
            customerBeforeUpdate.setState(request.state());
        }
        if(request.country_id()!=null){
            customerBeforeUpdate.setCountry_id(Long.valueOf(request.country_id()));
        }
        if(request.birth_country()!=null) {
            customerBeforeUpdate.setBirth_country(request.birth_country());
        }
        if(request.country()!=null) {
            customerBeforeUpdate.setCountry(request.country());
        }
        if(request.state_abbr()!=null) {
            customerBeforeUpdate.setState_abbr(request.state_abbr());
        }
        if(request.birth_state_abbr()!=null) {
            customerBeforeUpdate.setBirth_state_abbr(request.birth_state_abbr());
        }
        if (request.taxcode()!= null) {
            customerBeforeUpdate.setTax_code(request.taxcode());
            if (customerBeforeUpdate.getGender() == null) {
                customerBeforeUpdate.setGender(Integer.valueOf(customerBeforeUpdate.getTax_code().charAt(9) + ""+ customerBeforeUpdate.getTax_code().charAt(10)) < 32 ? "Male" : "Female");
            }
        }
        if (customerBeforeUpdate.getGender() == null && customerBeforeUpdate.getTax_code() != null) {
            customerBeforeUpdate.setGender(Integer.valueOf(customerBeforeUpdate.getTax_code().charAt(9) + "" + customerBeforeUpdate.getTax_code().charAt(10)) < 32 ? "Male" : "Female");
        }
        if (request.birth_date()!=null) {
            Utility.checkAdultAge(request.birth_date());
            customerBeforeUpdate.setDate_of_birth(request.birth_date());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            LocalDate dateOfBirth = LocalDate.parse(request.birth_date(), formatter);
            formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
            LocalDateTime dob=dateOfBirth.atStartOfDay();
            customerBeforeUpdate.setDate_of_birth(dob.format(formatter));
        }
        Log.info("customer repo : update customerBeforeUpdate into DB...");
        return customerRepo.getEntityManager().merge(customerBeforeUpdate);
    }



    @WithSpan("CustomerService.readCustomer")
    @Transactional
    @Blocking
    public CustomerEntity readCustomerByNdg(
            @SpanAttribute("arg.ndg") String ndg,
            @SpanAttribute("arg.token") String token) throws Exception {
        Log.infof("customer service : read customer by ndg, ndg=%s, token=%s", ndg, token);
        CustomerEntity entity;
        log.info("CustomerService.readCustomerByNdg() started...");
        try {
            Log.info("customer repo : find customer by ndg (UPPERCASE)");
            entity = customerRepo.find("UPPER(ndg)", ndg).firstResult();
            if (entity == null) {
                Log.info("customer null, create a new customer entity...");
                entity = new CustomerEntity();
                log.info("call address gateway for anagrafica...");
                Log.info("call iam utility : get user info, passing token...");
                Credentials result = iamUtility.getUserInfo(token);
                String tokenRes = result.getExternalInfo();

                Log.infof("tokenRes=%s", tokenRes);
                Log.info("call address gateway : get anagrafica GET /user...");
                Response response = addressClient.getAnagrafica(token, "ftthCheck");
                AddressGatewayResponse addressGatewayResponse = response.readEntity(AddressGatewayResponse.class);
                MapperCustomer mapperCustomer = new MapperCustomer();
                Log.info("call mapper customer");
                entity = mapperCustomer.customerToAddress(addressGatewayResponse);
                Log.info("customer repo : persist new customer into DB...");
                customerRepo.persist(entity);
                log.info("customer salvato ...");
            }
            log.info("CustomerService.readCustomerByNdg() end...");
            Log.info("customer service : return customer");
            return entity;
        } catch (PersistenceException px) {
            //tale constraint serve al fine di gestire le doppe chiamate che partono da fe e non si riesce a capire da dove possano partire
            if (px.getCause() instanceof ConstraintViolationException) {
                if (((ConstraintViolationException) px.getCause()).getConstraintName().equalsIgnoreCase("uc_customers_ndg")) {
                    log.info("ndg duplicato...");
                    return null;
                }
            }
            px.printStackTrace();
            throw new Exception(px.getMessage());
        } catch (Exception ex) {
            Log.error("something went wrong, throwing exception...");
            ex.printStackTrace();
            throw ex;
        }
    }

    @Transactional
    public CustomerEntity updateAreaClientiNdg(String ndg, CustomerBoundaryRequest request) throws Exception {
        Log.infof("customer service : update area clienti ndg, ndg=%s, request=%s", ndg, request);
        Log.info("customer repo : find customer by ndg (UPPERCASE)");
        CustomerEntity result = customerRepo.find("UPPER(ndg)", ndg).firstResult();
        Utility.canUpdateCustomer(result);
        return updateAreaClienti(result, request);
    }

    @Transactional
    public CustomerEntity updateAreaClientiEmail(String email, CustomerBoundaryRequest request) throws Exception {
        Log.infof("customer service : update area clienti email, email=%s, request=%s", email, request);
        Log.info("customer repo : find customer by email (UPPERCASE)");
        checkTokenTI();
        CustomerEntity result;
        if (request.getTax_code() != null && !request.getTax_code().isEmpty()) {
            result = customerRepo.findCustomerEmailTaxCode(email,request.getTax_code());
            Utility.canUpdateCustomer(result);
        } else {
            result = customerRepo.find("primary_mail", email).firstResult();
        }
        return updateAreaClienti(result, request);
    }
    @Transactional
    public CustomerEntity updateAreaClienti(CustomerEntity result, CustomerBoundaryRequest request) throws Exception {
        Log.infof("customer service : update area clienti, result=%s, request=%s", result, request);
        Log.infof("Customer service: checking tax code during update, taxCodeResult=%s, taxCodeRequest=%s", result.getTax_code(), request.getTax_code());
        Log.info("if request object's fields are not null, set the customer entity's fields with the values contained in the request object, before update into DB...");
        if (result.getTax_code() != null && !result.getTax_code().equalsIgnoreCase(request.getTax_code())) {
            Log.error("customer service: update area clienti, tax code does not match the request");
            throw new IllegalArgumentException("Tax code in the request does not match the customer's current tax code.");
        }
        if (request.getBirth_city_id() != null)
            result.setBirth_city_id(request.getBirth_city_id());
        if (request.getBirth_city() != null)
            result.setBirth_city(request.getBirth_city());
        if (request.getBirth_country() != null)
            result.setBirth_country(request.getBirth_country());
        if (request.getBirth_country_id() != null)
            result.setBirth_country_id(request.getBirth_country_id());
        if (request.getBirth_state() != null)
            result.setBirth_state(request.getBirth_state());
        if (request.getBirth_state_id() != null)
            result.setBirth_state_id(request.getBirth_state_id());
        if (request.getDate_of_birth() != null) {
            Utility.checkAdultAge(request.getDate_of_birth());
            result.setDate_of_birth(request.getDate_of_birth());
        }
        if (request.getCity() != null)
            result.setCity(request.getCity());
        if (request.getCountry_id() != null)
            result.setCountry_id(request.getCountry_id());
        if (request.getCountry() != null)
            result.setCountry(request.getCountry());
        if (request.getState() != null)
            result.setState(request.getState());
        if (request.getState_id() != null)
            result.setState_id(request.getState_id());
        if (request.getState_abbr() != null)
            result.setState_abbr(request.getState_abbr());
        if (request.getEducation() != null)
            result.setEducation(request.getEducation());
        if (request.getSalary() != null)
            result.setSalary(request.getSalary());
        if (request.getProfession() != null)
            result.setProfession(request.getProfession());
        if (request.getName() != null)
            result.setName(request.getName());
        if (request.getSurname() != null)
            result.setSurname(request.getSurname());
        if (request.getStreet() != null)
            result.setStreet(request.getStreet());
        if (request.getStreet_number() != null)
            result.setStreet_number(request.getStreet_number());
        if (request.getPrimary_mail() != null)
            result.setPrimary_mail(request.getPrimary_mail());
        if (request.getSecondary_mail() != null)
            result.setSecondary_mail(request.getSecondary_mail());
        if (request.getPrimary_phone() != null)
            result.setPrimary_phone(request.getPrimary_phone());
        if (request.getSecondary_phone() != null)
            result.setSecondary_phone(request.getSecondary_phone());
        if (request.getZip_code() != null)
            result.setZip_code(request.getZip_code());
        if (request.getBirth_state_abbr() != null)
            result.setBirth_state_abbr(request.getBirth_state_abbr());
        if (request.getId_card() != null)
            result.setId_card(request.getId_card());
        if (request.getCity_id() != null)
            result.setCity_id(request.getCity_id());
        if (request.getGender() != null)
            result.setGender(request.getGender());
        if (request.getLanguage() != null)
            result.setLanguage(request.getLanguage());
        if (request.getLegal_form() != null)
            result.setLegal_form(request.getLegal_form());
        if (request.getTax_code() != null)
            result.setTax_code(request.getTax_code());
        if (request.getVat_code() != null)
            result.setVatcode(request.getVat_code());
        if (request.getCompany() != null)
            result.setCompany(request.getCompany());
        if(request.getUserAcceptances()!=null){
            List<UserAcceptancesEntity> userAcceptancesEntities= flagToAcceptances(result,request.getUserAcceptances());
            result.setUserAcceptances(userAcceptancesEntities);
        }
        if(request.getSdiCode() !=null)
            result.setSdiCode(request.getSdiCode());
        if(request.getCertifiedMail() !=null)
            result.setCertifiedMail(request.getCertifiedMail());
        if(request.getLegalRepresentativeInfo()!=null){
            Set<LegalRepresentativeInfoEntity> legalRepresentativeInfoEntities = customerLegalRepresentativeInfo(result, request.getLegalRepresentativeInfo());
            result.setLegalRepresentativeInfo(legalRepresentativeInfoEntities);
        }
        if(request.getIndividualFirm() !=null)
            result.setIndividualFirm(request.getIndividualFirm());
        if(request.getVatTaxDifference() !=null)
            result.setVatTaxDifference(request.getVatTaxDifference());
        Log.info("customer repo : update customer entity into DB...");
        CustomerEntity resultUpdate= customerRepo.getEntityManager().merge(result);
        Log.info("customer service : return the result update");
        return resultUpdate;
    }


    private List<UserAcceptancesEntity> flagToAcceptances(CustomerEntity entity,List<UserAcceptancesBoundaryReferenceRequest> reqUserAcceptances){
        Log.infof("customer service : flag to acceptances, entity=%s, reqUserAcceptances=%s", entity, reqUserAcceptances);

        Log.info("for each user acceptance boundary reference request, retrieve from DB or create a user acceptance entity, update it and add it to a list of user acceptance entities...");
        List<UserAcceptancesEntity> result= new ArrayList<>();
        for (UserAcceptancesBoundaryReferenceRequest req:reqUserAcceptances) {
            FlagsEntity flagsEntity= flagsRepository.find("tag = ?1 and deleted_at is null", req.getTag()).firstResult();
      Map<String, Object> params = new HashMap<>();
            params.put("user_id", entity.getId());
            params.put("flag_id", flagsEntity.getId());
            UserAcceptancesEntity userAcceptancesEntity= userAcceptancesRepository.find("user_id = :user_id and flag_id = :flag_id", params).firstResult();
            if(userAcceptancesEntity == null) {
               userAcceptancesEntity = new UserAcceptancesEntity();
            }
            userAcceptancesEntity.setFlag(flagsEntity);
            userAcceptancesEntity.setUser(entity);
            userAcceptancesEntity.setValue(req.getValue());
            result.add(userAcceptancesEntity);
        }
        Log.info("customer service : return the list of user acceptances entities");
        return result;

    }

    private Set<LegalRepresentativeInfoEntity> customerLegalRepresentativeInfo(CustomerEntity entity, List<LegalRepresentativeInfoBoundaryReferenceRequest> reqLegalRepresentativeInfo){
        Set<LegalRepresentativeInfoEntity> result = new HashSet<>();
        Log.info("it.yolo.service#CustomerService: start create legalRepresentativeInfo");
        for(LegalRepresentativeInfoBoundaryReferenceRequest req:reqLegalRepresentativeInfo){
            LegalRepresentativeInfoEntity legalRepresentativeInfoEntity = new LegalRepresentativeInfoEntity();
            legalRepresentativeInfoEntity.setCustomer(entity);
            legalRepresentativeInfoEntity.setLastname(req.getLastname());
            legalRepresentativeInfoEntity.setFirstname(req.getFirstname());
            legalRepresentativeInfoEntity.setTaxcode(req.getTaxcode());
            legalRepresentativeInfoEntity.setMail(req.getMail());
            legalRepresentativeInfoEntity.setCountry(req.getCountry());
            legalRepresentativeInfoEntity.setCountry_id(req.getCountry_id());
            legalRepresentativeInfoEntity.setCity(req.getCity());
            legalRepresentativeInfoEntity.setCity_id(req.getCity_id());
            legalRepresentativeInfoEntity.setState(req.getState());
            legalRepresentativeInfoEntity.setState_id(req.getState_id());
            legalRepresentativeInfoEntity.setState_abbr(req.getState_abbr());
            legalRepresentativeInfoEntity.setZipcode(req.getZipcode());
            legalRepresentativeInfoEntity.setStreet(req.getStreet());
            result.add(legalRepresentativeInfoEntity);
        }
        Log.info("it.yolo.service#CustomerService: success creating legalRepresentativeInfo");
        return result;
    }

    private List<UserAcceptancesEntity> flagToAcceptancesCreate(CustomerEntity entity,List<UserAcceptancesBoundaryReferenceRequest> reqUserAcceptances){
        Log.infof("customer service : flag to acceptances for create customer, customer entity=%s, list reqUserAcceptances=%s", entity, reqUserAcceptances);

        Log.info("for each user acceptance boundary reference request, create a new user acceptance entity and set its fields with user_id, flag_id and user acceptance boolean value, and add it to a list of user acceptance entities...");
        List<UserAcceptancesEntity> result= new ArrayList<>();
        for (UserAcceptancesBoundaryReferenceRequest req:reqUserAcceptances) {

            FlagsEntity flagsEntity= flagsRepository.find("tag = ?1 and deleted_at is null", req.getTag()).firstResult();
            if(flagsEntity!=null){
                Map<String, Object> params = new HashMap<>();
                params.put("user_id", entity.getId());
                params.put("flag_id", flagsEntity.getId());
                UserAcceptancesEntity userAcceptancesEntity = new UserAcceptancesEntity();
                userAcceptancesEntity.setFlag(flagsEntity);
                userAcceptancesEntity.setUser(entity);
                userAcceptancesEntity.setValue(req.getValue());
                result.add(userAcceptancesEntity);
            }

        }
        Log.info("call user acceptances service : create entities...");
        userAcceptancesService.createEntities(result);
        Log.info("customer service : return the list of user acceptance entities");
        return result;

    }

    public CustomerEntity findByNdgOrEmail(String ndg , String email){
        Log.infof("customer service : find by ndg or email, ndg=%s, email=%s", ndg, email);
        CustomerEntity resultFind;
        if(ndg!=null){
            Log.info("customer repo : find customer by ndg (UPPERCASE)");
            resultFind= customerRepo.find("UPPER(ndg)",ndg.toUpperCase()).firstResult();
            Log.info("customer service : return customer entity (by ndg)...");
            return resultFind;
        }else {
            Log.info("customer repo : find customer by primary_mail (UPPERCASE)");
            resultFind= customerRepo.find("UPPER(primary_mail)",email.toUpperCase()).firstResult();
            Log.info("customer service : return customer entity (by primary_mail)...");
            return resultFind;
        }
    }

    private void checkTokenTI(){
        //check token appartenente a technical-users o intermediary-users
        Log.info("customer service : called check token TI method for checking token of a technical user / intermediary user...");
        List<JsonString> groupsJson=jsonWebToken.getClaim("cognito:groups");
        List<String> groups=new ArrayList<>();
        for (JsonString group : groupsJson) {
            groups.add(group.getString());
        }
        if(!(groups.contains(technicalUser) || groups.contains(intermediaryUser))) {
            Log.error("cognito:groups does not contain technical or intermediary user, throwing an unauthorized exception...");
            throw new UnauthorizedException();
        }
    }

    private boolean residenceFieldsChanged(CustomerEntity existingCustomer, CustomerRecordRequest request) {
        return !Objects.equals(existingCustomer.getCity(), request.city()) ||
                !Objects.equals(existingCustomer.getStreet(), request.street()) ||
                !Objects.equals(existingCustomer.getState(), request.state()) ||
                !Objects.equals(existingCustomer.getCountry(), request.country());
    }


//     check for country and city
    private CustomerEntity getCountryCode(CustomerEntity customer){
        try {
            JsonNode country=utilityClient.getCountryByName(customer.getCountry()).readEntity(JsonNode.class);
            if(country!=null && !country.isNull() && !country.isEmpty()) {
                customer.setCountryCode(country.get("iso").asText());
            }
        } catch (Exception e) {}

        try {
            JsonNode city=utilityClient.getCityById(Long.valueOf(customer.getCity_id())).readEntity(JsonNode.class);
            if(city!=null && !city.isNull() && !city.isEmpty()) {
                customer.setCityCode(city.get("code").asText());
            }
        } catch (Exception e) {}

        return customer;
    }

    @WithSpan("CustomerService.checkUserExists")
    public Response checkUserExists(String ndg) {
        return customerRepo.find("ndg", ndg).firstResultOptional().isPresent() ?
                Response.ok().build() : Response.noContent().build();
    }

    public void confirmSignupRegistrationEmail(String token, String username){
        CustomerEntity entity;
        Log.infov("it.yolo.service.CustomerService#confirmSignupRegistrationEmail: Start to find user by ndg {0}", username);
        entity = findByNdg(username);
        Log.infov("it.yolo.service.CustomerService#confirmSignupRegistrationEmail: User find", entity.getUsername());
        Log.infov("it.yolo.service.CustomerService#confirmSignupRegistrationEmail: Start send mail to {0}", entity.getPrimary_mail());
        this.SendMailRegistration(entity, token);
        Log.infov("it.yolo.service.CustomerService#confirmSignupRegistrationEmail: Mail sended to {0}", entity.getPrimary_mail());
    }

    public void SendMailRegistration(CustomerEntity entity, String token) {
        try {
            CommunicationManagerDtoRequest communicationManagerDtoRequest = new CommunicationManagerDtoRequest();
            communicationManagerDtoRequest.getMessage().setKey(registrationMailKey);
            communicationManagerDtoRequest.getOptions().setMessaggetype("html");
            communicationManagerDtoRequest.getOptions().setToMail(entity.getPrimary_mail());
            TemplatePlaceholder emailPlaceholder = new TemplatePlaceholder();
            TemplatePlaceholder privateAreaPlaceholder = new TemplatePlaceholder();
            privateAreaPlaceholder.setKey("privateArea");
            privateAreaPlaceholder.setValue(privateAreaUrl);
            emailPlaceholder.setKey("email");
            emailPlaceholder.setValue(entity.getPrimary_mail());
            communicationManagerDtoRequest.getOptions().getTemplatePlaceholder().add(emailPlaceholder);
            communicationManagerDtoRequest.getOptions().getTemplatePlaceholder().add(privateAreaPlaceholder);
            communicationManagerDtoRequest.getOptions().setUrgent(false);
            communicationManagerClient.sendEmail(token, communicationManagerDtoRequest);
            Log.infov("it.yolo.service.CustomerService#confirmSignupRegistrationEmail: Send mail successfully to {0}", entity.getPrimary_mail());
        } catch (Exception e) {
            Log.errorv("it.yolo.service#sendEmailPreregistration: Error send email :", e.getMessage());
        }
    }
}
