package it.yolo.service.question;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Service for processing question templates with dynamic placeholder replacement.
 * 
 * This service replaces placeholders in question content with actual values from OrderEntity.
 * Placeholder format: {{campo.sottocampo}} (e.g., {{orderItem.insuredAge}}, {{insuredItem.value}})
 * 
 * Supported data sources:
 * - orderItem: fields from OrderItemEntity
 * - insuredItem: fields from the insured_item JsonNode
 * - order: fields from OrderEntity
 */
@ApplicationScoped
@Slf4j
public class QuestionTemplatingService {

    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\{\\{([^}]+)\\}\\}");

    /**
     * Process question content by replacing placeholders with actual values from order data.
     *
     * @param content The question content containing placeholders
     * @param orderEntity The order entity containing the data
     * @return The processed content with placeholders replaced
     */
    public String processTemplate(String content, OrderEntity orderEntity) {
        if (content == null || content.trim().isEmpty()) {
            return content;
        }

        if (orderEntity == null) {
            log.warn("OrderEntity is null, returning original content");
            return content;
        }

        log.debug("Processing template: {}", content);

        String processedContent = content;
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(content);

        while (matcher.find()) {
            String placeholder = matcher.group(0); // Full placeholder including {{}}
            String fieldPath = matcher.group(1);   // Field path without {{}}
            
            log.debug("Processing placeholder: {} with field path: {}", placeholder, fieldPath);
            
            String value = extractValue(fieldPath, orderEntity);
            if (value != null) {
                processedContent = processedContent.replace(placeholder, value);
                log.debug("Replaced placeholder {} with value: {}", placeholder, value);
            } else {
                log.debug("No value found for placeholder: {}, keeping original", placeholder);
            }
        }

        log.debug("Template processing completed. Original: {}, Processed: {}", content, processedContent);
        return processedContent;
    }

    /**
     * Extract value from order entity based on field path.
     *
     * @param fieldPath The field path (e.g., "orderItem.price", "insuredItem.company.scoreESG")
     * @param orderEntity The order entity
     * @return The extracted value as string, or null if not found
     */
    private String extractValue(String fieldPath, OrderEntity orderEntity) {
        if (fieldPath == null || fieldPath.trim().isEmpty()) {
            return null;
        }

        String[] pathParts = fieldPath.split("\\.", 2);
        String rootField = pathParts[0];
        String remainingPath = pathParts.length > 1 ? pathParts[1] : null;

        log.debug("Extracting value for root field: {} with remaining path: {}", rootField, remainingPath);

        switch (rootField) {
            case "orderItem":
                return extractFromOrderItem(remainingPath, orderEntity);
            case "insuredItem":
                return extractFromInsuredItem(remainingPath, orderEntity);
            case "order":
                return extractFromOrder(remainingPath, orderEntity);
            case "chosenWarranties":
                return extractFromChosenWarranties(remainingPath, orderEntity);
            default:
                log.debug("Unknown root field: {}", rootField);
                return null;
        }
    }

    /**
     * Extract value from OrderItemEntity.
     *
     * @param fieldPath The field path within order item
     * @param orderEntity The order entity
     * @return The extracted value as string, or null if not found
     */
    private String extractFromOrderItem(String fieldPath, OrderEntity orderEntity) {
        if (orderEntity.getOrderItem() == null || orderEntity.getOrderItem().isEmpty()) {
            log.debug("No order items available");
            return null;
        }

        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);
        
        if (fieldPath == null) {
            return null;
        }

        log.debug("Extracting from order item field: {}", fieldPath);

        // Handle direct fields of OrderItemEntity
        switch (fieldPath) {
            case "price":
                return orderItem.getPrice();
            case "annualPrice":
                return orderItem.getAnnualPrice();
            case "productId":
                return String.valueOf(orderItem.getProduct_id());
            case "policyNumber":
                return orderItem.getPolicy_number();
            case "masterPolicyNumber":
                return orderItem.getMaster_policy_number();
            case "externalId":
                return orderItem.getExternal_id();
            case "state":
                return orderItem.getState();
            case "quantity":
                return orderItem.getQuantity() != null ? orderItem.getQuantity().toString() : null;
            case "packetId":
                return orderItem.getPacketId() != null ? orderItem.getPacketId().toString() : null;
            default:
                log.debug("Unknown order item field: {}", fieldPath);
                return null;
        }
    }

    /**
     * Extract value from insured_item JsonNode.
     *
     * @param fieldPath The field path within insured item
     * @param orderEntity The order entity
     * @return The extracted value as string, or null if not found
     */
    private String extractFromInsuredItem(String fieldPath, OrderEntity orderEntity) {
        if (orderEntity.getOrderItem() == null || orderEntity.getOrderItem().isEmpty()) {
            log.debug("No order items available");
            return null;
        }

        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);
        JsonNode insuredItem = orderItem.getInsured_item();
        
        if (insuredItem == null || fieldPath == null) {
            log.debug("Insured item is null or field path is null");
            return null;
        }

        log.debug("Extracting from insured item field: {}", fieldPath);

        // Convert field path to JsonPointer format
        String jsonPointer = "/" + fieldPath.replace(".", "/");
        JsonNode valueNode = insuredItem.at(jsonPointer);

        if (!valueNode.isMissingNode()) {
            String value = valueNode.asText();
            log.debug("Found value {} for insured item field {}", value, fieldPath);
            return value;
        } else {
            log.debug("Field {} not found in insured item", fieldPath);
            return null;
        }
    }

    /**
     * Extract value from OrderEntity.
     *
     * @param fieldPath The field path within order
     * @param orderEntity The order entity
     * @return The extracted value as string, or null if not found
     */
    private String extractFromOrder(String fieldPath, OrderEntity orderEntity) {
        if (fieldPath == null) {
            return null;
        }

        log.debug("Extracting from order field: {}", fieldPath);

        // Handle direct fields of OrderEntity
        switch (fieldPath) {
            case "id":
                return orderEntity.getId() != null ? orderEntity.getId().toString() : null;
            case "orderCode":
                return orderEntity.getOrderCode();
            case "policyCode":
                return orderEntity.getPolicyCode();
            case "packetId":
                return orderEntity.getPacketId() != null ? orderEntity.getPacketId().toString() : null;
            case "productId":
                return orderEntity.getProductId() != null ? orderEntity.getProductId().toString() : null;
            case "brokerId":
                return String.valueOf(orderEntity.getBrokerId());
            case "companyId":
                return String.valueOf(orderEntity.getCompanyId());
            case "customerId":
                return orderEntity.getCustomerId() != null ? orderEntity.getCustomerId().toString() : null;
            case "insurancePremium":
                return orderEntity.getInsurancePremium() != null ? orderEntity.getInsurancePremium().toString() : null;
            case "createdBy":
                return orderEntity.getCreatedBy();
            case "updatedBy":
                return orderEntity.getUpdatedBy();
            case "productType":
                return orderEntity.getProductType();
            default:
                log.debug("Unknown order field: {}", fieldPath);
                return null;
        }
    }

    /**
     * Extract value from chosenWarranties JsonNode with support for array processing.
     *
     * @param fieldPath The field path within chosenWarranties (e.g., "warranties", "warranties[mandatory=true]")
     * @param orderEntity The order entity
     * @return The extracted value as formatted string, or null if not found
     */
    private String extractFromChosenWarranties(String fieldPath, OrderEntity orderEntity) {
        if (fieldPath == null) {
            log.debug("Field path is null for chosenWarranties");
            return null;
        }

        JsonNode chosenWarranties = orderEntity.getChosenWarranties();
        if (chosenWarranties == null || chosenWarranties.isNull()) {
            log.debug("chosenWarranties is null");
            return null;
        }

        log.debug("Extracting from chosenWarranties field: {}", fieldPath);

        // Check if this is an array access with filter
        if (fieldPath.contains("[") && fieldPath.contains("]")) {
            return processArrayWithFilter(fieldPath, chosenWarranties);
        } else {
            // Simple field access
            return processSimpleField(fieldPath, chosenWarranties);
        }
    }

    /**
     * Process simple field access in chosenWarranties.
     *
     * @param fieldPath The field path
     * @param chosenWarranties The chosenWarranties JsonNode
     * @return The extracted value as string, or null if not found
     */
    private String processSimpleField(String fieldPath, JsonNode chosenWarranties) {
        // Convert field path to JsonPointer format
        String jsonPointer = "/" + fieldPath.replace(".", "/");
        JsonNode valueNode = chosenWarranties.at(jsonPointer);

        if (valueNode.isMissingNode()) {
            log.debug("Field {} not found in chosenWarranties", fieldPath);
            return null;
        }

        // If it's an array, process it as array
        if (valueNode.isArray()) {
            return formatArrayAsString(valueNode, null, null);
        } else {
            // Return as simple value
            return valueNode.asText();
        }
    }

    /**
     * Process array access with filter (e.g., "warranties[mandatory=true]").
     *
     * @param fieldPath The field path with filter
     * @param chosenWarranties The chosenWarranties JsonNode
     * @return The formatted array as string, or null if not found
     */
    private String processArrayWithFilter(String fieldPath, JsonNode chosenWarranties) {
        // Parse the field path to extract array name and filter
        int bracketStart = fieldPath.indexOf('[');
        int bracketEnd = fieldPath.indexOf(']');

        if (bracketStart == -1 || bracketEnd == -1 || bracketEnd <= bracketStart) {
            log.debug("Invalid array filter syntax in field path: {}", fieldPath);
            return null;
        }

        String arrayPath = fieldPath.substring(0, bracketStart);
        String filterExpression = fieldPath.substring(bracketStart + 1, bracketEnd);

        log.debug("Array path: {}, Filter expression: {}", arrayPath, filterExpression);

        // Get the array
        String jsonPointer = "/" + arrayPath.replace(".", "/");
        JsonNode arrayNode = chosenWarranties.at(jsonPointer);

        if (arrayNode.isMissingNode() || !arrayNode.isArray()) {
            log.debug("Array {} not found in chosenWarranties", arrayPath);
            return null;
        }

        // Parse filter expression
        String filterProperty = null;
        String filterValue = null;

        if ("*".equals(filterExpression)) {
            // Wildcard - no filter
            filterProperty = null;
            filterValue = null;
        } else {
            // Must be in format "property=value"
            String[] filterParts = filterExpression.split("=", 2);
            if (filterParts.length == 2) {
                filterProperty = filterParts[0].trim();
                filterValue = filterParts[1].trim();

                // Validate that both property and value are not empty
                if (filterProperty.isEmpty() || filterValue.isEmpty()) {
                    log.debug("Invalid filter syntax: empty property or value in {}", filterExpression);
                    return null;
                }
            } else {
                log.debug("Invalid filter syntax: missing '=' in {}", filterExpression);
                return null;
            }
        }

        return formatArrayAsString(arrayNode, filterProperty, filterValue);
    }

    /**
     * Format an array of objects as a human-readable string.
     * Extracts the 'name' property from each object and formats as "item1, item2 e item3".
     *
     * @param arrayNode The array JsonNode
     * @param filterProperty The property to filter by (null for no filter)
     * @param filterValue The value to filter by (null for no filter)
     * @return The formatted string, or null if array is empty
     */
    private String formatArrayAsString(JsonNode arrayNode, String filterProperty, String filterValue) {
        List<String> names = new ArrayList<>();

        for (JsonNode item : arrayNode) {
            // Apply filter if specified
            if (filterProperty != null && filterValue != null) {
                JsonNode propertyNode = item.get(filterProperty);
                if (propertyNode == null) {
                    continue; // Skip items without the filter property
                }

                String itemValue = propertyNode.asText();
                if (!matchesFilterValue(itemValue, filterValue)) {
                    continue; // Skip items that don't match the filter
                }
            }

            // Extract the name property
            JsonNode nameNode = item.get("name");
            if (nameNode != null && !nameNode.isNull()) {
                names.add(nameNode.asText());
            } else {
                log.debug("Item in array missing 'name' property: {}", item);
            }
        }

        if (names.isEmpty()) {
            log.debug("No items found matching filter criteria");
            return null;
        }

        return formatListAsItalianString(names);
    }

    /**
     * Check if an item value matches the filter value.
     * Handles boolean, string, and numeric comparisons.
     *
     * @param itemValue The value from the item
     * @param filterValue The filter value to match against
     * @return true if the values match, false otherwise
     */
    private boolean matchesFilterValue(String itemValue, String filterValue) {
        if (itemValue == null || filterValue == null) {
            return false;
        }

        // Direct string comparison
        if (itemValue.equals(filterValue)) {
            return true;
        }

        // Boolean comparison (case insensitive)
        if ("true".equalsIgnoreCase(itemValue) && "true".equalsIgnoreCase(filterValue)) {
            return true;
        }
        if ("false".equalsIgnoreCase(itemValue) && "false".equalsIgnoreCase(filterValue)) {
            return true;
        }

        // Numeric comparison
        try {
            double itemNumeric = Double.parseDouble(itemValue);
            double filterNumeric = Double.parseDouble(filterValue);
            return itemNumeric == filterNumeric;
        } catch (NumberFormatException e) {
            // Not numeric, fall back to string comparison
            return itemValue.equalsIgnoreCase(filterValue);
        }
    }

    /**
     * Format a list of strings as Italian-style enumeration.
     * Examples: "item1", "item1 e item2", "item1, item2 e item3"
     *
     * @param items The list of items to format
     * @return The formatted string
     */
    private String formatListAsItalianString(List<String> items) {
        if (items.isEmpty()) {
            return "";
        }

        if (items.size() == 1) {
            return items.get(0);
        }

        if (items.size() == 2) {
            return items.get(0) + " e " + items.get(1);
        }

        // For 3 or more items: "item1, item2, item3 e item4"
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < items.size() - 1; i++) {
            if (i > 0) {
                sb.append(", ");
            }
            sb.append(items.get(i));
        }
        sb.append(" e ").append(items.get(items.size() - 1));

        return sb.toString();
    }
}
