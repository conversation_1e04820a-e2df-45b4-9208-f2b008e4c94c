package it.yolo.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.communicationManager.dto.CommunicationManagerDtoRequest;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.arag.AragTutelaLegaleEmission;
import it.yolo.client.communicationManager.template.cnps.BmgMed;
import it.yolo.client.communicationManager.template.cnps.CnpsProtecion;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;
import it.yolo.client.communicationManager.template.europAssistance.*;
import it.yolo.client.communicationManager.template.generali.TimSport;
import it.yolo.client.communicationManager.template.helvetia.HelvetiaCyber;
import it.yolo.client.communicationManager.template.helvetia.HelvetiaPet;
import it.yolo.client.communicationManager.template.helvetia.HelvetiaTravel;
import it.yolo.client.communicationManager.template.itas.ItasGive;
import it.yolo.client.communicationManager.template.itas.ItasSnowNew;
import it.yolo.client.communicationManager.template.metLife.TimBillProtection;
import it.yolo.client.communicationManager.template.itas.ItasActive;
import it.yolo.client.communicationManager.template.itas.ItasSnow;
import it.yolo.client.communicationManager.template.metLife.TimBillProtector;
import it.yolo.client.communicationManager.template.netInsurance.NetCyber;
import it.yolo.client.communicationManager.template.netInsurance.NetHomeflix;
import it.yolo.client.communicationManager.template.netInsurance.NetMultirisk;
import it.yolo.client.communicationManager.template.netInsurance.YoloForPet;
import it.yolo.client.communicationManager.template.europAssistance.RcScooterBike;
import it.yolo.client.communicationManager.template.verti.VertiMotor;
import it.yolo.client.communicationManager.template.yolo.CasaSelectra;
import it.yolo.client.customer.CustomerClient;
import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.document.DocumentClient;
import it.yolo.client.document.dto.DownloadDocumentRequest;
import it.yolo.client.document.dto.DownloadDocumentResponse;
import it.yolo.client.documentManager.DocumentManagerClient;
import it.yolo.client.order.OrderClient;
import it.yolo.client.order.dto.request.DataDtoRequest;
import it.yolo.client.order.dto.request.OrderItemRequest;
import it.yolo.client.order.dto.request.OrderRequestDto;
import it.yolo.client.order.dto.response.OrderCodesListResponse;
import it.yolo.client.order.dto.response.OrderItem;
import it.yolo.client.order.dto.response.OrderResponseDto;
import it.yolo.client.order.dto.response.ProductOrderResponse;
import it.yolo.client.orderManager.OrderManagerClient;
import it.yolo.client.payment.PaymentManagerClient;
import it.yolo.client.payment.StripeClient;
import it.yolo.client.payment.dto.PaymentManagerListRequest;
import it.yolo.client.pg.response.PgResponseDto;
import it.yolo.client.policy.PolicyClient;
import it.yolo.client.policy.dto.response.DataPolicyResponse;
import it.yolo.client.policy.dto.response.PolicyResponseDto;
import it.yolo.client.pricing.PricingClient;
import it.yolo.client.product.ProductClient;
import it.yolo.client.yin.YinClientV2;
import it.yolo.common.Base64Utils;
import it.yolo.costants.Costant;
import it.yolo.emission.dto.request.*;
import it.yolo.emission.dto.response.CertificateResponseDto;
import it.yolo.emission.dto.response.EmissionResponseDto;
import it.yolo.exception.*;
import it.yolo.model.request.EmissionManagerRequest;
import it.yolo.model.request.EmissionManagerRequestDto;
import it.yolo.model.request.UpdateWarrantiesRequest;
import it.yolo.service.client.InvokePG;
import it.yolo.service.client.V2.InvokePolicyV2;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;
import javax.annotation.PostConstruct;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.json.JsonString;
import javax.ws.rs.core.Response;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequestScoped
public class ServiceEmissionManager {

    private static final String PAYMENT_STATE="Confirmed";

    private static final String STATE_COMPLETE="COMPLETE";
    private static final String COMPLETE_STATE="Completed";
    private static final String INSURANCE_DOCUMENT_CATEGORY ="policy";

    @ConfigProperty(name = "tenant.name")
    String tenantName;

    @Inject
    @RestClient
    OrderClient orderClient;

    @Inject
    @RestClient
    StripeClient stripeClient;

    @Inject
    @RestClient
    CustomerClient customerClient;

    @Inject
    @RestClient
    PricingClient pricingClient;

    @Inject
    @RestClient
    YinClientV2 yinClient;

    @Inject
    @RestClient
    OrderManagerClient orderManagerClient;

    @Inject
    @RestClient
    PaymentManagerClient paymentManagerClient;

    @Inject
    @RestClient
    DocumentManagerClient documentManagerClient;

    @Inject
    ServiceEmission serviceEmission;

    @Inject
    ServicePolicy servicePolicy;

    @Inject
    ServiceProduct serviceProduct;

    @Inject
    ServiceCustomer serviceCustomer;

    @Inject
    InvokePolicyV2 invokePolicy;
    @Inject
    InvokePG invokePG;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    ServiceCommunicationManager serviceCommunicationManager;

    @Inject
    NetHomeflix netHomeflix;

    @Inject
    NetCyber netCyber;

    @Inject
    YoloForPet yoloForPet;

    @Inject 
    VertiMotor vertiMotor;

    @Inject
    ItasActive itasActive;

    @Inject
    ItasSnow itasSnow;

    @Inject
    ItasSnowNew itasSnowNew;

    @Inject
    TimProtezioneCasa timProtezioneCasa;

    @Inject
    NetMultirisk netMultirisk;

    @Inject
    RcScooterBike rcScooterBike;

    @Inject
    TimBillProtection timBillProtection;

    @Inject
    TimBillProtector timBillProtector;

    @Inject
    CnpsProtecion cnpsProtecion;

    @Inject
    CasaSelectra casaSelectra;

    @Inject
    BmgMed bmgMed;

    @Inject
    ItasGive itasGive;

    @Inject
    TimProtezioneViaggiAnnualeE timProtezioneViaggiAnnualeEurope;

    @Inject
    TimProtezioneViaggiAnnualeW timProtezioneViaggiAnnualeWorld;

    @Inject
    TimProtezioneViaggiBreviE timProtezioneViaggiBreviEurope;

    @Inject
    TimProtezioneViaggiBreviW timProtezioneViaggiBreviWorld;

    @Inject
    TimProtezioneViaggiRoaming timProtezioneViaggiRoaming;

    @Inject
    TimSport timSport;

    @Inject
    HelvetiaCyber helvetiaCyber;

    @Inject
    HelvetiaPet helvetiaPet;

    @Inject
    HelvetiaTravel helvetiaTravel;

    @Inject
    AragTutelaLegaleEmission aragTutelaLegaleEmission;

    @Inject
    Logger logger;

    @Inject
    ServiceCustomer customerService;

    @Inject
    @RestClient
    DocumentClient documentClient;

    @Inject
    @RestClient
    PolicyClient policyClient;

    @Inject
    @RestClient
    ProductClient productClient;

    @ConfigProperty(name = "technical.users")
    List<String> technicalUser;

    private final Map<String, Map<String, Template>> mapProductToTemplate = new HashMap<>();

    @PostConstruct
    void initMap() {
        Map<String, Template> mapNetInsurance = new HashMap<>();
        Map<String, Template> mapItas = new HashMap<>();
        Map<String, Template> mapEuropAssistance = new HashMap<>();
        Map<String, Template> mapMetLife= new HashMap<>();
        Map<String, Template> mapCnps= new HashMap<>();
        Map<String, Template> mapVerti= new HashMap<>();
        Map<String, Template> mapGenerali= new HashMap<>();
        Map<String, Template> mapHelvetia= new HashMap<>();
        Map<String, Template> mapCasaSelectra= new HashMap<>();
        // Aggiungiamo la nuova mappa per ARAG
        Map<String, Template> mapArag = new HashMap<>();

        mapNetInsurance.put("net-homeflix", netHomeflix);
        mapNetInsurance.put("yolo-for-pet", yoloForPet);
        mapNetInsurance.put("net-multirisk-commerce", netMultirisk);
        mapNetInsurance.put("net-multirisk-craft", netMultirisk);
        mapNetInsurance.put("net-cyber", netCyber);
        mapItas.put("itas-active", itasActive);
        mapItas.put("itas-snow", itasSnow);
        mapItas.put("itas-give", itasGive);
        mapHelvetia.put("helvetia-cyber", helvetiaCyber);
        mapHelvetia.put("helvetia-pet", helvetiaPet);
        mapHelvetia.put("helvetia-travel", helvetiaTravel);
        mapItas.put("itas-snow-new", itasSnowNew);
        mapEuropAssistance.put("tim-protezione-viaggi-roaming", timProtezioneViaggiRoaming);
        mapEuropAssistance.put("tim-protezione-viaggi-europe", timProtezioneViaggiBreviEurope);
        mapEuropAssistance.put("tim-protezione-viaggi-world", timProtezioneViaggiBreviWorld);
        mapEuropAssistance.put("tim-protezione-viaggi-europe-y", timProtezioneViaggiAnnualeEurope);
        mapEuropAssistance.put("tim-protezione-viaggi-world-y", timProtezioneViaggiAnnualeWorld);
        mapEuropAssistance.put("tim-protezione-casa", timProtezioneCasa);
        mapEuropAssistance.put("rc-scooter-bike", rcScooterBike);
        mapMetLife.put("tim-bill-protection", timBillProtection);
        mapMetLife.put("tim-bill-protector", timBillProtector);
        mapCnps.put("cnps-protecion", cnpsProtecion);
        mapCnps.put("bmg-med", bmgMed);
        mapVerti.put("verti-motor", vertiMotor);
        mapGenerali.put("tim-sport", timSport);
        mapCasaSelectra.put("yolo-casa-selectra", casaSelectra);
        // Aggiungiamo il prodotto ARAG
        mapArag.put("yolo-tutela-legale-famiglia", aragTutelaLegaleEmission);
        mapArag.put("yolo-tutela-legale-professionisti", aragTutelaLegaleEmission);
        mapArag.put("yolo-tutela-legale-pmi", aragTutelaLegaleEmission);

        mapProductToTemplate.put("Generali", mapGenerali);
        mapProductToTemplate.put("Helvetia", mapHelvetia);
        mapProductToTemplate.put("Net Insurance", mapNetInsurance);
        mapProductToTemplate.put("ITAS", mapItas);
        mapProductToTemplate.put("Europ Assistance", mapEuropAssistance);
        mapProductToTemplate.put("Met Life", mapMetLife);
        mapProductToTemplate.put("CNPS", mapCnps);
        mapProductToTemplate.put("Verti", mapVerti);
        mapProductToTemplate.put("Allianz", mapCasaSelectra);
        mapProductToTemplate.put("ARAG", mapArag);
    }

    public JsonNode generateCertificate (String token, String code) throws Exception {
        CustomerResponseDto customerResponseDto = null;
        Response order = orderClient.findByOrderCode(token,code);
        OrderResponseDto orderResponseDto = order.readEntity(OrderResponseDto.class);

        if(orderResponseDto.getResponse().getCustomerId()!=null){
            customerResponseDto = serviceCustomer.findById(token, Long.valueOf(orderResponseDto.getResponse().getCustomerId()));
        }
        Response policyResponse = policyClient.getOrderId(token,orderResponseDto.getResponse().getId());
        PolicyResponseDto policyResponseDto = policyResponse.readEntity(PolicyResponseDto.class);
        Response product = productClient.findById(token,null,Long.valueOf(orderResponseDto.getResponse().getProductId()));
        JsonNode productResponseDto = product.readEntity(JsonNode.class);
        CertificateRequestDto certificateRequestDto = new CertificateRequestDto();
        certificateRequestDto.setCustomer(customerResponseDto);
        certificateRequestDto.setOrder(orderResponseDto);
        certificateRequestDto.setPolicy(policyResponseDto);
        certificateRequestDto.setProduct(productResponseDto);
        Response response;
        ObjectNode responseNode;
        try {
            response= documentClient.generateCertficate(certificateRequestDto);
            responseNode = response.readEntity(ObjectNode.class);
        }catch (Exception e){
            throw new DocumentException(e.getMessage());
        }

        return  responseNode;
    }
    public OrderResponseDto emission(EmissionManagerRequest request, String token, String code) throws Exception {
        EmissionManagerRequestDto requestDto=request.getData();
        OrderResponseDto orderResponseDtoPayment=null;
        OrderResponseDto orderResponseDtoComplete=null;
        JsonNode responsePg=null;
        EmissionRequestDto emissionRequestDto=null;
        EmissionResponseDto emissionResponseDto=null;
        CertificateResponseDto certificateResponseDto=null;
        JsonNode product=null;
        Boolean isTch=checkTokenTI();
        // Update order with payment token
        try {
            OrderRequestDto orderRequestDto = new OrderRequestDto();
            DataDtoRequest dataDtoRequestOrder = new DataDtoRequest();
            dataDtoRequestOrder.setOrderCode(code);
            dataDtoRequestOrder.setPaymentToken(requestDto.getPaymentToken());
            dataDtoRequestOrder.setPaymentType(requestDto.getPaymentType());
            dataDtoRequestOrder.setPaymentTransactionId(requestDto.getPaymentTransactionId());
            dataDtoRequestOrder.setDiscount(requestDto.getDiscount());
            dataDtoRequestOrder.setAnagState(PAYMENT_STATE);
            orderRequestDto.setDataDtoRequest(dataDtoRequestOrder);
            logger.info("updating order: "+code);
            if(isTch){
                orderClient.uncheckedUpdate(token, orderRequestDto, code).readEntity(OrderResponseDto.class);
                orderResponseDtoPayment=orderClient.getOrderUnchecked(token, code).readEntity(OrderResponseDto.class);
            }else {
                orderClient.update(token, orderRequestDto, code).readEntity(OrderResponseDto.class);
                orderResponseDtoPayment=orderClient.getOrder(token, code).readEntity(OrderResponseDto.class);
            }
            logger.info("updated order: "+code);
        }catch (Exception e){
            //code 001
            logger.error("EmissionFlowException while creating order request TO", e);
            throw new Exception("EmissionFlowException while creating order request TO", e);
        }
        // END Update order with payment token
        // Create Request, call emission impl
        try {
            emissionRequestDto = new EmissionRequestDto();
            Integer customerId=orderResponseDtoPayment.getResponse().getCustomerId();
            Long productId=Long.valueOf(orderResponseDtoPayment.getResponse().getProductId());
            Long orderId=Long.valueOf(orderResponseDtoPayment.getResponse().getId());
            if(orderResponseDtoPayment.getResponse().getParentOrder()!=null){
                String parentCode=orderResponseDtoPayment.getResponse().getParentOrder();
                String firstParent=parentCode;
                String lastParent=parentCode;
                if(parentCode.contains(";")){
                    String[] codes=parentCode.split(";");
                    firstParent=codes[0];
                    lastParent=codes[codes.length-1];
                }
                OrderResponseDto firstParentOrder=orderClient.findByOrderCode(token,
                        firstParent).readEntity(OrderResponseDto.class);
                emissionRequestDto.setParentOrderId(firstParentOrder.getResponse().getId());
                if(!lastParent.equalsIgnoreCase(firstParent)){
                    OrderResponseDto lastParentOrder=orderClient.findByOrderCode(token,
                            lastParent).readEntity(OrderResponseDto.class);
                    emissionRequestDto.setLastParentOrderId(lastParentOrder.getResponse().getId());
                }
            }
            logger.info("getting customer: "+customerId);
            CustomerResponseDto customerResponseDto=customerClient.findById(token,
                    Long.valueOf(customerId)).readEntity(CustomerResponseDto.class);
            logger.info("got customer: "+customerId);
            logger.info("getting product: "+productId);
            product=serviceProduct.findById(productId, orderResponseDtoPayment.getResponse().getLanguage());
            if(product.get("data").get("configuration").hasNonNull("properties") &&
                    product.get("data").get("configuration").get("properties").hasNonNull("existingCheck")){
                JsonNode check=orderClient.checkExistingPolicy(code, token).readEntity(JsonNode.class);
                String msg=check.get("data").asText();
                if(msg.equalsIgnoreCase(Costant.ERROR_EXISTING_POLICY)){
                    throw new Exception(msg);
                }
            }
            logger.info("got product: "+productId);
            emissionRequestDto.setCustomer(customerResponseDto);
            logger.info("getting order: "+orderId);
            if(isTch){
                emissionRequestDto.setOrder(orderClient.findByIdUnchecked(token, orderId).readEntity(OrderResponseDto.class));
            }else {
                emissionRequestDto.setOrder(orderClient.findById(token, orderId).readEntity(OrderResponseDto.class));
            }
            logger.info("got order: "+orderId);
            emissionRequestDto.setProduct(product);
            emissionRequestDto.setPayment_frequency(orderResponseDtoPayment.getResponse().getOrderItem().get(0)
                            .getInstance()!=null && orderResponseDtoPayment.getResponse().getOrderItem().get(0)
                            .getInstance().hasNonNull("paymentFrequency") ?
                    orderResponseDtoPayment.getResponse().getOrderItem().get(0)
                    .getInstance().get("paymentFrequency").asText() : null);
            emissionRequestDto.setSubscriptionId(requestDto.getSubscriptionId());
            checkPolicyCodeGeneration(product, orderResponseDtoPayment, emissionRequestDto, token, orderResponseDtoPayment);
            if (orderResponseDtoPayment.getResponse().getProduct() == null) {
                ProductOrderResponse productOrderResponse = new ProductOrderResponse();
                productOrderResponse.setDataProduct(orderResponseDtoPayment.getResponse().getPacket().getDataPacket().getProduct());
                orderResponseDtoPayment.getResponse().setProduct(productOrderResponse);
            }
            if(emissionRequestDto.getProduct().get("data").get("configuration").get("properties").hasNonNull("emissionSteps")){
                JsonNode array = emissionRequestDto.getProduct().get("data").get("configuration").get("properties").withArray("emissionSteps");
                for(JsonNode step: array){
                    switch(step.asText()) {
                        case "emission":
                            emissionResponseDto = getEmissionResponseDto(emissionRequestDto);
                            break;

                        case "certificate":
                            emissionResponseDto = new EmissionResponseDto();
                            PolicyResponseDto tempPolicyResponse = new PolicyResponseDto();
                            tempPolicyResponse.setData(emissionRequestDto.getPolicy());
                            emissionResponseDto.setPolicyResponseDto(tempPolicyResponse);
                            certificateResponseDto = generateCertificate(emissionRequestDto, emissionResponseDto, product);
                            break;
                    }
                }
            } else {
                emissionResponseDto = getEmissionResponseDto(emissionRequestDto);
                responsePg = emissionResponseDto.getPgResponse();
                certificateResponseDto = generateCertificate(emissionRequestDto, emissionResponseDto, product);
            }
        } catch (EmissionFlowException e){
            logger.error("EmissionFlowException while creating emission request TO", e);
            throw new Exception("EmissionFlowException while creating emission request TO", e);
        }
        catch (PGException e) {
            //code 002
            logger.error("PGException while creating emission request TO", e);
            throw new Exception("PGException while creating emission request TO", e);
        }catch (PolicyException e){
            //code 003
            logger.error("PolicyException while creating emission request TO", e);
            throw new Exception("PGException while creating emission request TO", e);
        } catch (GenerateMacException e) {
            logger.error("GenerateMacException while creating emission request TO", e);
            throw new Exception("GenerateMacException while creating emission request TO", e);
        }
        // END Create Request, call emission impl
        PolicyResponseDto insurance=null;
        try{
            logger.info("updating policy "+emissionResponseDto.getPolicyResponseDto().getData().getId());
            if(certificateResponseDto!=null && certificateResponseDto.getFile()!=null) {
                insurance=servicePolicy.updateAfterEmission(emissionResponseDto,
                            certificateResponseDto.getNomeFile(), certificateResponseDto.getLink(), certificateResponseDto.getType(),
                            orderResponseDtoPayment.getResponse().getProduct().getDataProduct().getCode(),
                            orderResponseDtoPayment.getResponse().getOrderItem().get(0).getStartDate());

                logger.info("policy updated: " + emissionResponseDto.getPolicyResponseDto().getData().getId());
            }
        }catch (Exception e){
            //code 005
            logger.error("While updating policy", e);
            throw new Exception("Exception While updating policy", e);
        }
        try{
            OrderRequestDto orderRequestDto = new OrderRequestDto();
            DataDtoRequest dataDtoRequestOrder = new DataDtoRequest();
            dataDtoRequestOrder.setOrderCode(code);
            dataDtoRequestOrder.setAnagState(COMPLETE_STATE);
            dataDtoRequestOrder.setResPg(responsePg);
            orderRequestDto.setDataDtoRequest(dataDtoRequestOrder);
            logger.info("updating order: "+ code);
            while(orderResponseDtoComplete == null || !orderResponseDtoComplete.getResponse().getOrderItem().get(0).getState().equalsIgnoreCase(STATE_COMPLETE)){
                if(isTch){
                    orderResponseDtoComplete = orderClient.uncheckedUpdate(token, orderRequestDto, code).readEntity(OrderResponseDto.class);
                }else {
                    orderResponseDtoComplete = orderClient.update(token, orderRequestDto, code).readEntity(OrderResponseDto.class);
                }}
            if(orderResponseDtoPayment.getResponse().getIntermediaryOrder()!= null
                    && orderResponseDtoPayment.getResponse().getIntermediaryOrder()){
                yinClient.alignOder(orderResponseDtoPayment.getResponse().getOrderCode());
            }
            logger.info("order updated: "+ code+". Emission flow ok");
        }catch (Exception e){
            //code 007
            logger.error("While updating order", e);
            throw new Exception("Exception While updating order", e);
        }
        CommunicationManagerDtoRequest mailRequest;
        String cert = orderResponseDtoPayment.getResponse().getProduct().getDataProduct().getConfiguration().getCertificate();
        try{
            if("external_generation_async".equalsIgnoreCase(cert) || "none".equalsIgnoreCase(cert)) {
                    logger.info("sending email without certificate...");
                    mailRequest=this.certificationToCommunicationRequestWithoutCertificate(emissionResponseDto, emissionRequestDto);
                    serviceCommunicationManager.sendEmail(mailRequest);
                    logger.info("email without certificate sended");
            } else {
                logger.info("sending email with certificate...");
                mailRequest=this.certificationToCommunicationRequest(certificateResponseDto, emissionResponseDto, emissionRequestDto);
                serviceCommunicationManager.sendEmail(mailRequest);
                logger.info("email with certificate sended");
            }
        } catch (Exception e){
            //code 006
            logger.error("While sending email", e);
            throw new Exception("Exception While sending email", e);
        }
        if(product.get("data").get("configuration").get("properties").has("attachDocumentation") &&
                product.get("data").get("configuration").get("properties").get("attachDocumentation").has("emission")) {
            try {
                documentManagerClient.addDocument(token, insurance.getData().getPolicyCode(), getDocuments(mailRequest, insurance, product));
            }catch (Exception e){
                logger.error("While adding documents", e);
                throw new Exception("Exception While adding documents", e);
            }
        }
        return orderResponseDtoComplete;
    }

    private CertificateResponseDto generateCertificate(EmissionRequestDto emissionRequestDto, EmissionResponseDto emissionResponseDto, JsonNode product) throws Exception {
        CertificateResponseDto certificateResponseDto;
        try{
            CertificateRequestDto certificateRequestDto = emissionToCertificate(emissionRequestDto, emissionResponseDto, product);
            logger.info("generating certificate...");
            certificateResponseDto = serviceEmission.generateCerticatePolicy(certificateRequestDto, emissionResponseDto);
            logger.info("certificate generation ok");
            if (certificateResponseDto!=null && emissionResponseDto.getPolicyResponseDto().getData() != null
                    && emissionRequestDto.getOrder().getResponse().getProduct() != null) {
                certificateResponseDto.setNomeFile(emissionResponseDto.getPolicyResponseDto().getData().getPolicyCode()+ ".pdf");
            }
        } catch (Exception e){
            //code 004
            logger.error("While generating certificate", e);
            throw new Exception("Exception While generating certificate", e);
        }
        return certificateResponseDto;
    }

    private EmissionResponseDto getEmissionResponseDto(EmissionRequestDto emissionRequestDto) throws GenerateMacException {
        EmissionResponseDto emissionResponseDto;
        logger.info("emission policy...");
        emissionResponseDto = serviceEmission.emissionPolicy(emissionRequestDto);
        logger.info("emission policy ok: "+emissionResponseDto.getPolicyResponseDto().getData().getPolicyCode());
        return emissionResponseDto;
    }

    public JsonNode updateWarranties(UpdateWarrantiesRequest request, String token){
        OrderResponseDto parentOrder=orderClient.findByOrderCode(token, request.getOrderCode()).readEntity(OrderResponseDto.class);
        ObjectNode reqQuote= JsonNodeFactory.instance.objectNode();
        reqQuote.put("productId", Long.valueOf(parentOrder.getResponse().getProductId()));//da req
        reqQuote.put("customerId", parentOrder.getResponse().getCustomerId());
        reqQuote.put("orderId", parentOrder.getResponse().getId());
//        PolicyResponseDto parentPolicy=invokePolicy.readPolicyByOrderId(token, parentOrder.getResponse().getId());
        OrderRequestDto orderRequest=new OrderRequestDto();
        orderRequest.getDataDtoRequest().getOrderItem().add(new OrderItemRequest());
        orderRequest.getDataDtoRequest().getOrderItem().get(0).setInstance(JsonNodeFactory.instance.objectNode());
        ((ObjectNode)orderRequest.getDataDtoRequest().getOrderItem().get(0).getInstance()).set("chosenWarranties",
                request.getInstance().get("chosenWarranties"));
        orderRequest.getDataDtoRequest().setOrderCode(request.getOrderCode());
        OrderResponseDto orderDuplicated=orderClient.duplicatedOrder(token, orderRequest).readEntity(OrderResponseDto.class);
        reqQuote.put("productId", Long.valueOf(orderDuplicated.getResponse().getProductId()));
        reqQuote.put("customerId", orderDuplicated.getResponse().getCustomerId());
        reqQuote.put("orderId", orderDuplicated.getResponse().getId());
        return pricingClient.quote("Bearer " + jsonWebToken.getRawToken(),
                JsonNodeFactory.instance.objectNode().set("data", reqQuote)).readEntity(JsonNode.class);
    }

    public Map<String, JsonNode> getPayments(String token) {
        Long customerId=customerService.findByNdg(token, jsonWebToken.getClaim("username")).getData().getId().longValue();
        OrderCodesListResponse codes=orderClient.getByCustomerId(token, customerId).readEntity(OrderCodesListResponse.class);
        ArrayNode payments=paymentManagerClient.getPayments(token, new PaymentManagerListRequest(codes.getCodes())).readEntity(ArrayNode.class);
        Map<String, JsonNode> pending=new HashMap<>();
        ObjectMapper mapper=new ObjectMapper();
        payments.forEach(p->{
            String orderCode=p.get("orderCode").asText();
            ArrayNode trx = p.get("listTransaction") != null && p.get("listTransaction").isArray()
                    ? (ArrayNode) p.get("listTransaction")
                    : mapper.createArrayNode();
            trx.forEach(t->{
                JsonNode subscription = null;
                try {
                    subscription = stripeClient.findSubcriptionByOrder(token, orderCode)
                            .readEntity(JsonNode.class);
                } catch (Exception e) {
                    return;
                }
                if (subscription == null || !subscription.has("paymentSourceName") ||
                        !subscription.get("paymentSourceName").asText("").contains("Sepa")) {
                    return;
                }
                if(t.get("paymentType").asText().equalsIgnoreCase("pending")){
                    ObjectNode value=mapper.createObjectNode();
                    OrderResponseDto order= orderClient.findByOrderCode(token,  orderCode).readEntity(OrderResponseDto.class);
                        value.set("transactions", trx);
                        OrderItem orderItem=order.getResponse().getOrderItem().get(0);
                        orderItem.setCreatedAt(order.getResponse().getCreatedAt());
                        value.putPOJO("orderItem", orderItem);
                        pending.put(orderCode, value);
                }
            });
        });
        if(pending.isEmpty()){
            return new HashMap<>();
        }
        return pending;
    }

    public JsonNode proxyEmission(String token, String orderCode, EmissionManagerRequest request) throws Exception {
        OrderResponseDto order=orderClient.findByOrderCodeUnchecked(token, orderCode).readEntity(OrderResponseDto.class);
        if(StringUtils.isNotBlank(request.getData().getPaymentType()) &&
                request.getData().getPaymentType().equalsIgnoreCase(Costant.SDD_PAYMENT_METHOD)) {
            LocalDate start = LocalDate.parse(order.getResponse().getOrderItem().get(0).getStartDate().split("T")[0]);
            if (start.isBefore(LocalDate.now())) {
                order=orderClient.updateStartDate(token, orderCode).readEntity(OrderResponseDto.class);
            }
        }
        JsonNode product=serviceProduct.findById(Long.valueOf(order.getResponse().getProductId()), null);
        String emission=product.get("data").get("configuration").get("properties").has("emission") ?
                product.get("data").get("configuration").get("properties").get("emission").asText() : "";
        if("legacy".equalsIgnoreCase(emission)) {
            JsonNode instance = order.getResponse().getOrderItem().get(0).getInstance();
            return orderManagerClient.confirm(token, orderCode, instance.get("orderManager")).readEntity(JsonNode.class);
        } else {
            return new ObjectMapper().valueToTree(this.emission(request, token, orderCode));
        }
    }

    private CertificateRequestDto emissionToCertificate(EmissionRequestDto emissionRequestDto, EmissionResponseDto emissionResponseDto, JsonNode product) {
        CertificateRequestDto certificateRequestDto = new CertificateRequestDto();
        certificateRequestDto.setProduct(product);
        certificateRequestDto.setPolicy(emissionResponseDto.getPolicyResponseDto());
        certificateRequestDto.setOrder(emissionRequestDto.getOrder());
        certificateRequestDto.setCustomer(emissionRequestDto.getCustomer());
        certificateRequestDto.setCertificate(emissionResponseDto.getCertificate());
        if(emissionResponseDto.getEmission() != null) {
            certificateRequestDto.getOrder().getResponse().getOrderItem().forEach(orderItem -> {
                orderItem.setEmission(emissionResponseDto.getEmission());});
        }
        return certificateRequestDto;
    }

    private CommunicationManagerDtoRequest certificationToCommunicationRequest(CertificateResponseDto certificate, EmissionResponseDto emissionResponseDto, EmissionRequestDto emissionRequestDto) {
        CommunicationManagerDtoRequest communicationManagerDtoRequest = new CommunicationManagerDtoRequest();
        TemplateRequest templateRequest = new TemplateRequest();
        templateRequest.setCertificateResponseDto(certificate);
        templateRequest.setEmissionRequestDto(emissionRequestDto);
        templateRequest.setEmissionResponseDto(emissionResponseDto);
        TemplateResponseDto templateResponseDto = mapProductToTemplate.get(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getInsuranceCompany()).get(
                emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getCode()).generate(templateRequest);
        communicationManagerDtoRequest.setMessage(templateResponseDto.getMessage());
        communicationManagerDtoRequest.setOptions(templateResponseDto.getOptions());
        communicationManagerDtoRequest.setAttachments(templateResponseDto.getAttachment());
        return communicationManagerDtoRequest;
    }

    private CommunicationManagerDtoRequest certificationToCommunicationRequestWithoutCertificate(EmissionResponseDto emissionResponseDto, EmissionRequestDto emissionRequestDto) {
        CommunicationManagerDtoRequest communicationManagerDtoRequest = new CommunicationManagerDtoRequest();

        TemplateRequest templateRequest = new TemplateRequest();
        //templateRequest.setCertificateResponseDto(certificateResponseDto);
        templateRequest.setEmissionRequestDto(emissionRequestDto);
        templateRequest.setEmissionResponseDto(emissionResponseDto);
        TemplateResponseDto templateResponseDto = mapProductToTemplate.get(emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getInsuranceCompany()).get(
                emissionRequestDto.getOrder().getResponse().getProduct().getDataProduct().getCode()).generate(templateRequest);
        communicationManagerDtoRequest.setMessage(templateResponseDto.getMessage());
        communicationManagerDtoRequest.setOptions(templateResponseDto.getOptions());
        communicationManagerDtoRequest.getOptions().setToMail(emissionRequestDto.getCustomer().getData().getPrimaryMail());
        //communicationManagerDtoRequest.getOptions().setToMail(""); // for tests
        //communicationManagerDtoRequest.setAttachments(null);
        return communicationManagerDtoRequest;
    }

    private void checkPolicyCodeGeneration(JsonNode product, OrderResponseDto order, EmissionRequestDto emissionRequestDto,
                                           String token, OrderResponseDto orderResponseDto){
        if(product.get("data").get("configuration").get("emissionPrefix")!=null &&
                !product.get("data").get("configuration").get("emissionPrefix").asText().equalsIgnoreCase("null") &&
                !StringUtils.isEmpty(product.get("data").get("configuration").get("emissionPrefix").asText()) &&
                StringUtils.isNotBlank(product.get("data").get("configuration").get("emissionPrefix").asText())){
            logger.info("Generating policy code for order "+ order.getResponse().getOrderCode());
            Integer policyProgressive=servicePolicy.getNextPolicy(product.get("data").get("code").asText(), token);
            String replacement="";
            String year="";
            String prefix=product.get("data").get("configuration").get("emissionPrefix").asText();
            String utmSource=order.getResponse().getUtmSource()!=null ? order.getResponse().getUtmSource() : "";
            if(StringUtils.isNotEmpty(utmSource) && product.get("data").get("configuration").get("properties").has("emission")
                    && product.get("data").get("configuration").get("properties").get("emission").has("utmReplacement")){
                replacement=product.get("data").get("configuration").get("properties").get("emission").get("utmReplacement").asText();
            }
            if(product.get("data").get("configuration").get("properties").has("emission") && product.get("data")
                    .get("configuration").get("properties").get("emission").has("yearPrefix") &&
                    product.get("data").get("configuration").get("properties").get("emission").get("yearPrefix").asBoolean()){
                year= String.valueOf(LocalDateTime.parse(order.getResponse().getOrderItem().get(0).getStartDate()).getYear()).substring(2)+
                        product.get("data").get("configuration").get("properties").get("emission").get("yearSeparator").asText();
            }
            String policyCode=prefix.replace(replacement, utmSource) + year + policyProgressive;
            logger.info("Generated policy code "+ policyCode +" for order "+ order.getResponse().getOrderCode());
            DataPolicyResponse policy=new DataPolicyResponse();
            policy.setPolicyCode(policyCode);
            emissionRequestDto.setPolicy(policy);
        } else if(orderResponseDto.getResponse().getPacket().getDataPacket()!=null &&
                orderResponseDto.getResponse().getPacket().getDataPacket().getConfiguration().has("emissionPrefix") &&
                orderResponseDto.getResponse().getPacket().getDataPacket().getConfiguration().get("emissionPrefix")!=null){
            logger.info("Generating policy code for order "+ order.getResponse().getOrderCode());
            Integer policyProgressive=servicePolicy.getNextPolicy(orderResponseDto.getResponse().getPacket().getDataPacket().getName(), token);
            String prefix=orderResponseDto.getResponse().getPacket().getDataPacket().getConfiguration().get("emissionPrefix").asText();
            String policyCode=prefix + policyProgressive;
            logger.info("Generated policy code "+ policyCode +" for order "+ order.getResponse().getOrderCode());
            DataPolicyResponse policy=new DataPolicyResponse();
            policy.setPolicyCode(policyCode);
            emissionRequestDto.setPolicy(policy);
        }
    }

    public PgResponseDto sendCertificate(String token, String policyCode){
        EmissionRequestV2 emissionRequest = this.policyToEmissionRequestDto(policyCode, token);
        PgResponseDto pgResponse = invokePG.callback(emissionRequest);
        return pgResponse;
    }

    public PgResponseDto sendReceipt(String token, String policyCode){
        EmissionRequestV2 emissionRequest = this.policyToEmissionRequestDto(policyCode, token);
        PgResponseDto pgResponse = invokePG.receipt(emissionRequest);

        return pgResponse;
    }

    private EmissionRequestV2 policyToEmissionRequestDto(String policyCode, String token) {
        PolicyResponseDto policy;
        try {
            policy = invokePolicy.readPolicyByCode(token, policyCode);
        }catch (Exception e) {
            throw new PolicyException(e.getMessage());
        }
        CustomerResponseDto customer;
        try {
            customer = serviceCustomer.findById(token, policy.getData().getCustomer().getId());
        } catch (Exception e) {
            logger.error("while fetching customer", e);
            throw new CustomerEx(e.getMessage());
        }
        OrderResponseDto order;
        try {
            order = orderClient.findByIdUnchecked(token, policy.getData().getOrderId()).readEntity(OrderResponseDto.class);
        } catch (Exception e) {
            logger.error("while fetching order", e);
            throw new OrderException(e.getMessage());
        }
        if(!order.getResponse().getOrderItem().get(0).getStartDate().contains(".")) {
            order.getResponse().getOrderItem().get(0).setStartDate(order.getResponse().getOrderItem()
                    .get(0).getStartDate().concat(".000"));
        }
        JsonNode product=serviceProduct.findById(policy.getData().getProduct().getId(), null);
        DownloadDocumentRequest documentRequest = new DownloadDocumentRequest();
        documentRequest.setLink(policy.getData().getCertificateLink());
        DownloadDocumentResponse document = documentClient.download(documentRequest).readEntity(DownloadDocumentResponse.class);
        policy.getData().setCertificate(document.getFile());
        EmissionRequestV2 emissionRequest = new EmissionRequestV2();
        EmissionRequestDtoV2 emissionRequestDto = new EmissionRequestDtoV2();
       /* ((ObjectNode)product.get("data")).remove("images");
        for(JsonNode packet : product.get("data").get("packets")) {
            for (JsonNode warranty : packet.withArray("warranties")) {
                ((ObjectNode) warranty.get("anagWarranty")).remove("images");
            }
        }
        if(order.getResponse().getOrderItem().get(0).getInstance().has("ceilings")) {
            for(JsonNode ceiling: order.getResponse().getOrderItem().get(0).getInstance().withArray("ceilings")){
                ((ObjectNode)ceiling.get("anagWarranty")).remove("images");
            }
        }
        if(policy.getData().getCertificate() != null && !policy.getData().getCertificate().isEmpty()){
            policy.getData().setCertificate("");
        }
        if (order.getResponse().getOrderItem().get(0).getEmission().has("certificate")){
            ((ObjectNode)order.getResponse().getOrderItem().get(0).getEmission()).remove("certificate");
        }
        if (order.getResponse().getOrderItem().get(0).getEmission().has("providerResponse")){
            ((ObjectNode)order.getResponse().getOrderItem().get(0).getEmission()).remove("providerResponse");
        }*/

        String startDate=policy.getData().getStartDate().contains(".") ? policy.getData().getStartDate() :
                policy.getData().getStartDate()+".000";
        policy.getData().setStartDate(startDate);
        JsonNode orderNode=new ObjectMapper().valueToTree(order);
        ((ObjectNode)orderNode.get("data")).remove("product");
        emissionRequestDto.setOrder(orderNode);
        emissionRequestDto.setPolicy(policy.getData());
        emissionRequestDto.setCustomer(customer);
        emissionRequestDto.setProduct(product);
        emissionRequest.setData(emissionRequestDto);
        emissionRequest.setTenant(tenantName);
        return emissionRequest;
    }

    private Boolean checkTokenTI(){
        //check token appartenente a technical-users o intermediary-users
        List<JsonString> groupsJson=jsonWebToken.getClaim("cognito:groups");
        List<String> groups=new ArrayList<>();
        for (JsonString group : groupsJson) {
            groups.add(group.getString());
        }
        return (technicalUser.containsAll(groups));
    }

    private JsonNode getDocuments(CommunicationManagerDtoRequest mailReq, PolicyResponseDto policy, JsonNode product){
        ObjectNode request=JsonNodeFactory.instance.objectNode();
        String now=LocalDateTime.now().toString();
        String category= INSURANCE_DOCUMENT_CATEGORY;
        ArrayNode documents=JsonNodeFactory.instance.arrayNode();
        JsonNode fields=product.get("data").get("configuration").get("properties").get("attachDocumentation")
                .get("emission");
        mailReq.getAttachments().forEach(attachment->{

            String fieldType=fields.get(attachment.getFilename()).asText(),
                    fileName=attachment.getFilename();
            if(fieldType.equalsIgnoreCase("certificate")) {
                documents.add(getDocument(fileName, policy.getData().getCertificateLink(), now, category));
            } else if (fieldType.equalsIgnoreCase("att344ter")){
                //upload file to s3
                String fileNameS3=fileName.replace(".", "_".concat(policy.getData().getPolicyCode()).concat("."));
                CertificateResponseDto upload=documentClient.uploadDocument(new UploadDtoRequest(Base64Utils
                        .getBase64FromLink(product.get("data").get("attachment3_4").asText()), fileNameS3))
                        .readEntity(CertificateResponseDto.class);
                documents.add(getDocument(fileName, upload.getLink(), now, category));
            } else if (fieldType.equalsIgnoreCase("infoSet")) {
                //upload file to s3
                String fileNameS3=fileName.replace(".", "_".concat(policy.getData().getPolicyCode()).concat("."));
                CertificateResponseDto upload=documentClient.uploadDocument(new UploadDtoRequest(Base64Utils
                        .getBase64FromLink(product.get("data").get("informativeSet").asText()), fileNameS3)).readEntity(CertificateResponseDto.class);
                documents.add(getDocument(fileName, upload.getLink(), now, category));
            }
        });
        request.set("data", documents);
        return request;
    }

    private ObjectNode getDocument(String name, String link, String now, String category){
        ObjectNode doc=JsonNodeFactory.instance.objectNode();
        doc.put("name", name);
        doc.put("link", link);
        doc.put("category", category);
        doc.put("uploadedAt", now);
        return doc;
    }
}
