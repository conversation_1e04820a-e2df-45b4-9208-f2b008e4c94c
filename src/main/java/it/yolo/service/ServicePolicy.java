package it.yolo.service;

import it.yolo.client.policy.PolicyClient;
import it.yolo.client.policy.dto.PolicyDtoRequest;
import it.yolo.client.policy.request.ActionRequest;
import it.yolo.client.policy.response.PolicyResponse;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;

@RequestScoped
public class ServicePolicy {

    @Inject
    JsonWebToken jsonWebToken;

    @RestClient
    @Inject
    PolicyClient policyClient;

    @Inject
    Logger logger;

    public PolicyResponse findById(Long policyId) throws Exception {
        String token = "Bearer " + jsonWebToken.getRawToken();
        try {
            Response response = policyClient.findById(token, policyId);
            PolicyResponse policyResponse = response.readEntity(PolicyResponse.class);
            return policyResponse;
        } catch (Exception ex) {
            throw new Exception("errore durante la chiamata al servizio di policy : " + ex.getMessage());
        }
    }

    public PolicyResponse findByCode(final String policyCode) throws Exception {
        final String token = "Bearer " + jsonWebToken.getRawToken();
        try {
            return policyClient.findByCode(token, policyCode).readEntity(PolicyResponse.class);
        } catch (final Exception ex) {
            throw new Exception("Durante la chiamata al servizio di policy (by code): " + ex.getMessage());
        }
    }

    public PolicyDtoRequest actionPolicy(ActionRequest actionRequest) throws Exception {
        try {
            String token = "Bearer " + jsonWebToken.getRawToken();
            Response res = policyClient.actionForPostVendita(token, actionRequest);
            PolicyDtoRequest policyResponse = res.readEntity(PolicyDtoRequest.class);
            return policyResponse;
        } catch (Exception ex) {
            throw new Exception("errore durante la chiamata al servizio di policy /action: " + ex.getMessage());
        }
    }

    public PolicyResponse saveByUpdatedWarranties(PolicyDtoRequest policyRequest) throws Exception {
        try {
            logger.info("ServicePolicy.saveByUpdatedWarranties start "+ policyRequest);
            String token = "Bearer " + jsonWebToken.getRawToken();
            logger.info("ServicePolicy.saveByUpdatedWarranties chiamata allo iad-policy/saveByUpdatedWarranties");
            Response res = policyClient.saveByUpdatedWarranties(token, policyRequest);
            PolicyResponse policyResponse = res.readEntity(PolicyResponse.class);
            logger.info("ServicePolicy.saveByUpdatedWarranties iad-policy/saveByUpdatedWarranties end with response + "+ policyResponse);
            return policyResponse;
        } catch (Exception ex) {
            throw new Exception("errore durante la chiamata al servizio di policy /action: " + ex.getMessage());
        }
    }


}
