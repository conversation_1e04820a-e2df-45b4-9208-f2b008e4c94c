package it.yolo.exception;

import it.yolo.model.error.ErrorResponse;
import javax.ws.rs.core.Response;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.RestResponse;
import org.jboss.resteasy.reactive.server.ServerExceptionMapper;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Arrays;
/*
public class ExceptionMappers {
    private static final Logger LOGGER = Logger.getLogger(ExceptionMappers.class);

    @ServerExceptionMapper(Exception.class)
    public RestResponse<ErrorResponse> mapException(InvokeProductEx ex) {
        LOGGER.error(ex.getStackTrace().toString(), ex);
        return RestResponse.status(Response.Status.INTERNAL_SERVER_ERROR,
                new ErrorResponse(ex.getMessage(), getStackTrace(ex)));
    }

    public static String getStackTrace(Throwable t) {
        StringWriter sw = new StringWriter();
        t.printStackTrace(new PrintWriter(sw));
        return sw.toString();
    }
}
*/