package it.yolo.utils;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

public final class TaxCodeGenerator {

    private static final Calendar CALENDAR = new GregorianCalendar();

    /**
     * E.g. "<PERSON>", "<PERSON>", '<PERSON>', new Date(1977, 9, 12), "H501"
     *
     * @param name            Name
     * @param surname         Surname
     * @param gender          Gender
     * @param dateOfBirth     Date of birth
     * @param cityCodeOfBirth Code of the "comune" of birth
     * @return Codice Fiscale (Italian tax code)
     */
    public static String calculateFC(
            final String name,
            final String surname,
            final char gender,
            final Date dateOfBirth,
            final String cityCodeOfBirth) {

        String fiscalCode = "";
        final String fcSurname = surname.replace(" ", "").toUpperCase();
        final String fcName = name.replace(" ", "").toUpperCase();
        CALENDAR.setTime(dateOfBirth);

        String consonants = consonants(fcSurname);
        String vowels = vowels(fcSurname);
        int consonantsLength = consonants.length();
        switch (consonantsLength) {
            case 0 -> {
                if (vowels.length() > 2)
                    fiscalCode += vowels.substring(0, 3);
                else if (vowels.length() == 2)
                    fiscalCode += vowels + "x";
                else if (vowels.length() == 1)
                    fiscalCode += vowels + "xx";
                else
                    fiscalCode += "xxx";
            }
            case 1 -> {
                if (vowels.length() == 1)
                    fiscalCode += consonants + vowels + "x";
                else
                    fiscalCode += consonants + vowels.substring(0, 2);
            }
            case 2 -> {
                if (vowels.length() > 0)
                    fiscalCode += consonants + vowels.charAt(0);
                else
                    fiscalCode += consonants + "x";
            }
            default -> fiscalCode += consonants.substring(0, 3);
        }

        // NAME
        consonants = consonants(fcName);
        vowels = vowels(fcName);
        consonantsLength = consonants.length();
        switch (consonantsLength) {
            case 0 -> {
                if (vowels.length() > 2)
                    fiscalCode += vowels.substring(0, 3);
                else if (vowels.length() == 2)
                    fiscalCode += vowels + "x";
                else if (vowels.length() == 1)
                    fiscalCode += vowels + "xx";
                else
                    fiscalCode += "xxx";
            }
            case 1 -> {
                if (vowels.length() == 1)
                    fiscalCode += consonants + vowels + "x";
                else
                    fiscalCode += consonants + vowels.substring(0, 2);
            }
            case 2 -> {
                if (vowels.length() > 0)
                    fiscalCode += consonants + vowels.charAt(0);
                else
                    fiscalCode += consonants + "x";
            }
            case 3 -> fiscalCode += consonants;
            default -> fiscalCode += consonants.charAt(0) + consonants.substring(2, 4);
        }

        /* Year */
        fiscalCode += String.format("%02d", CALENDAR.get(Calendar.YEAR) % 100);
        /* Month */
        switch (CALENDAR.get(Calendar.MONTH) + 1) {
            case 1 -> fiscalCode += "A";
            case 2 -> fiscalCode += "B";
            case 3 -> fiscalCode += "C";
            case 4 -> fiscalCode += "D";
            case 5 -> fiscalCode += "E";
            case 6 -> fiscalCode += "H";
            case 7 -> fiscalCode += "L";
            case 8 -> fiscalCode += "M";
            case 9 -> fiscalCode += "P";
            case 10 -> fiscalCode += "R";
            case 11 -> fiscalCode += "S";
            case 12 -> fiscalCode += "T";
        }
        /* day */
        int day = CALENDAR.get(Calendar.DAY_OF_MONTH);
        if (gender == 'M')
            fiscalCode += day < 10 ? "0" + day : day;
        else {
            day += 40;
            fiscalCode += Integer.toString(day);
        }
        /* birth city */
        fiscalCode += cityCodeOfBirth;

        /* Control char */
        fiscalCode = fiscalCode.toUpperCase();
        int evenSum = 0;
        for (int i = 1; i <= 13; i += 2) {
            switch (fiscalCode.charAt(i)) {
                case '0', 'A' -> evenSum += 0;
                case '1', 'B' -> evenSum += 1;
                case '2', 'C' -> evenSum += 2;
                case '3', 'D' -> evenSum += 3;
                case '4', 'E' -> evenSum += 4;
                case '5', 'F' -> evenSum += 5;
                case '6', 'G' -> evenSum += 6;
                case '7', 'H' -> evenSum += 7;
                case '8', 'I' -> evenSum += 8;
                case '9', 'J' -> evenSum += 9;
                case 'K' -> evenSum += 10;
                case 'L' -> evenSum += 11;
                case 'M' -> evenSum += 12;
                case 'N' -> evenSum += 13;
                case 'O' -> evenSum += 14;
                case 'P' -> evenSum += 15;
                case 'Q' -> evenSum += 16;
                case 'R' -> evenSum += 17;
                case 'S' -> evenSum += 18;
                case 'T' -> evenSum += 19;
                case 'U' -> evenSum += 20;
                case 'V' -> evenSum += 21;
                case 'W' -> evenSum += 22;
                case 'X' -> evenSum += 23;
                case 'Y' -> evenSum += 24;
                case 'Z' -> evenSum += 25;
            }
        }
        int oddSum = 0;
        for (int i = 0; i <= 14; i += 2) {
            switch (fiscalCode.charAt(i)) {
                case '0', 'A' -> oddSum += 1;
                case '1', 'B' -> oddSum += 0;
                case '2', 'C' -> oddSum += 5;
                case '3', 'D' -> oddSum += 7;
                case '4', 'E' -> oddSum += 9;
                case '5', 'F' -> oddSum += 13;
                case '6', 'G' -> oddSum += 15;
                case '7', 'H' -> oddSum += 17;
                case '8', 'I' -> oddSum += 19;
                case '9', 'J' -> oddSum += 21;
                case 'K' -> oddSum += 2;
                case 'L' -> oddSum += 4;
                case 'M' -> oddSum += 18;
                case 'N' -> oddSum += 20;
                case 'O' -> oddSum += 11;
                case 'P' -> oddSum += 3;
                case 'Q' -> oddSum += 6;
                case 'R' -> oddSum += 8;
                case 'S' -> oddSum += 12;
                case 'T' -> oddSum += 14;
                case 'U' -> oddSum += 16;
                case 'V' -> oddSum += 10;
                case 'W' -> oddSum += 22;
                case 'X' -> oddSum += 25;
                case 'Y' -> oddSum += 24;
                case 'Z' -> oddSum += 23;
            }
        }
        final int controlInteger = (evenSum + oddSum) % 26;
        String controlCharacter = "";
        switch (controlInteger) {
            case 0 -> controlCharacter = "A";
            case 1 -> controlCharacter = "B";
            case 2 -> controlCharacter = "C";
            case 3 -> controlCharacter = "D";
            case 4 -> controlCharacter = "E";
            case 5 -> controlCharacter = "F";
            case 6 -> controlCharacter = "G";
            case 7 -> controlCharacter = "H";
            case 8 -> controlCharacter = "I";
            case 9 -> controlCharacter = "J";
            case 10 -> controlCharacter = "K";
            case 11 -> controlCharacter = "L";
            case 12 -> controlCharacter = "M";
            case 13 -> controlCharacter = "N";
            case 14 -> controlCharacter = "O";
            case 15 -> controlCharacter = "P";
            case 16 -> controlCharacter = "Q";
            case 17 -> controlCharacter = "R";
            case 18 -> controlCharacter = "S";
            case 19 -> controlCharacter = "T";
            case 20 -> controlCharacter = "U";
            case 21 -> controlCharacter = "V";
            case 22 -> controlCharacter = "W";
            case 23 -> controlCharacter = "X";
            case 24 -> controlCharacter = "Y";
            case 25 -> controlCharacter = "Z";
        }
        fiscalCode += controlCharacter;
        return fiscalCode.toUpperCase();
    }

    private static String consonants(final String word) {
        final StringBuilder consonants = new StringBuilder();
        for (final char character : word.toLowerCase().toCharArray()) {
            if (character != 'a' && character != 'e' && character != 'i'
                    && character != 'o' && character != 'u' && isAlphabet(character)) {

                consonants.append(character);
            }

        }
        return consonants.toString();
    }

    private static boolean isAlphabet(final char ch) {
        if (ch >= 'a' && ch <= 'z') {
            return true;
        }
        return ch >= 'A' && ch <= 'Z';
    }

    private static String vowels(final String word) {
        final StringBuilder vowels = new StringBuilder();
        for (final char character : word.toLowerCase().toCharArray()) {
            if (character == 'a' || character == 'e' || character == 'i'
                    || character == 'o' || character == 'u') {

                vowels.append(character);
            }
        }
        return vowels.toString();
    }

    private TaxCodeGenerator() {
        // do nothing
    }

    public static String calculateFCWithNameSurname(
            final String name,
            final String surname) {

        String fiscalCode = "";
        final String fcSurname = surname.replace(" ", "").toUpperCase();
        final String fcName = name.replace(" ", "").toUpperCase();

        String consonants;
        consonants = consonants(fcSurname);
        String vowels = vowels(fcSurname);
        int consonantsLength = consonants.length();
        switch (consonantsLength) {
            case 0 -> {
                if (vowels.length() > 2)
                    fiscalCode += vowels.substring(0, 3);
                else if (vowels.length() == 2)
                    fiscalCode += vowels + "x";
                else if (vowels.length() == 1)
                    fiscalCode += vowels + "xx";
                else
                    fiscalCode += "xxx";
            }
            case 1 -> {
                if (vowels.length() == 1)
                    fiscalCode += consonants + vowels + "x";
                else
                    fiscalCode += consonants + vowels.substring(0, 2);
            }
            case 2 -> {
                if (vowels.length() > 0)
                    fiscalCode += consonants + vowels.charAt(0);
                else
                    fiscalCode += consonants + "x";
            }
            default -> fiscalCode += consonants.substring(0, 3);
        }

        // NAME
        consonants = consonants(fcName);
        vowels = vowels(fcName);
        consonantsLength = consonants.length();
        switch (consonantsLength) {
            case 0 -> {
                if (vowels.length() > 2)
                    fiscalCode += vowels.substring(0, 3);
                else if (vowels.length() == 2)
                    fiscalCode += vowels + "x";
                else if (vowels.length() == 1)
                    fiscalCode += vowels + "xx";
                else
                    fiscalCode += "xxx";
            }
            case 1 -> {
                if (vowels.length() == 1)
                    fiscalCode += consonants + vowels + "x";
                else
                    fiscalCode += consonants + vowels.substring(0, 2);
            }
            case 2 -> {
                if (vowels.length() > 0)
                    fiscalCode += consonants + vowels.charAt(0);
                else
                    fiscalCode += consonants + "x";
            }
            case 3 -> fiscalCode += consonants;
            default -> fiscalCode += consonants.charAt(0) + consonants.substring(2, 4);
        }

        return fiscalCode.toUpperCase();
    }

}
