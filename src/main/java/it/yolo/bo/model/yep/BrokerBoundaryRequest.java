package it.yolo.bo.model.yep;

import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import javax.validation.constraints.NotBlank;

@Schema(description = "Broker data")
@Getter
@Setter
public class BrokerBoundaryRequest {

    @NotBlank
    @Schema(description = "Name")
    private String name;

    @NotBlank
    @Schema(description = "Identity code")
    private String identitycode;

    @Schema(description = "Description")
    private String description;

}
