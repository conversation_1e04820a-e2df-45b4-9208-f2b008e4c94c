package it.yolo.bo.rest.iad;


import it.yolo.bo.domain.iad.WarrantyEntity;
import it.yolo.bo.exception.EntityNotFoundException;
import it.yolo.bo.mapper.WarrantyMapper;
import it.yolo.bo.model.ErrorResponse;
import it.yolo.bo.model.iad.BaseRequestBoundary;
import it.yolo.bo.model.iad.BaseResponseBoundary;
import it.yolo.bo.model.iad.WarrantyBoundaryRequest;
import it.yolo.bo.model.iad.WarrantyBoundaryResponse;
import it.yolo.bo.rest.BaseResource;
import it.yolo.bo.service.iad.WarrantyService;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.jboss.resteasy.reactive.ResponseStatus;
import org.jboss.resteasy.reactive.RestPath;
import org.jboss.resteasy.reactive.RestResponse;

import javax.inject.Inject;
import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Path("/iad/v1/warranties")
public class WarrantyResource extends BaseResource {

    @Inject
    WarrantyService service;

    @POST
    @ResponseStatus(RestResponse.StatusCode.CREATED)
    @Operation(summary = "Creates a new warranty returning the created entity")
    @APIResponse(responseCode = "201",
            description = "Entity created",
            content = @Content(mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = ResponseSchema.class)))
    @APIResponse(description = "Error",
            content = @Content(mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<WarrantyBoundaryResponse> create(
            @Valid final BaseRequestBoundary<WarrantyBoundaryRequest> req,
            @HeaderParam("Authorization") final String token) throws Exception {
        try {
            final WarrantyEntity entity = service.create(
                    WarrantyMapper.INSTANCE.requestToEntity(req.getData()));
            return new BaseResponseBoundary<>(
                    WarrantyMapper.INSTANCE.entityToResponse(entity)
            );
        } catch (Exception e) {
            throw new Exception("Error status 500");
        }
    }


    @GET
    @Path("{id}")
    @Operation(summary = "Reads a warranty entity")
    @APIResponse(responseCode = "200", description = "Entity read")
    @APIResponse(description = "Error",
            content = @Content(mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<WarrantyBoundaryResponse> read(
            @Parameter(description = "Warranty ID") @RestPath("id") final long id,
            @HeaderParam("Authorization") final String token) throws Exception {
        try {
            final WarrantyEntity entity = service.read(id).orElseThrow(() ->
                    new EntityNotFoundException(String.format(
                            "Entity warranty by id=%d not found", id)));
            return new BaseResponseBoundary<>(
                    WarrantyMapper.INSTANCE.entityToResponse(entity)
            );
        }  catch (Exception e) {
            throw new Exception("Error status 500");
        }
    }


    @PUT
    @Path("{id}")
    @Operation(summary = "Updates a warranty returning the resulting entity")
    @APIResponse(responseCode = "200", description = "Entity updated")
    @APIResponse(description = "Error",
            content = @Content(mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<WarrantyBoundaryResponse> update(
            @Parameter(description = "Warranty ID") @RestPath("id") final long id,
            @Valid final BaseRequestBoundary<WarrantyBoundaryRequest> req,
            @HeaderParam("Authorization") final String token) throws Exception {
        try {
            final WarrantyEntity entity = service.update(
                    id,
                    WarrantyMapper.INSTANCE.requestToEntity(req.getData()));
            return new BaseResponseBoundary<>(
                    WarrantyMapper.INSTANCE.entityToResponse(entity)
            );
        } catch (Exception e) {
            // Gestione delle eccezioni qui

            throw new Exception("Error status 500");
        }
    }


//    @DELETE
//    @Path("{id}")
//    @ResponseStatus(RestResponse.StatusCode.NO_CONTENT)
//    @Operation(summary = "Deletes a warranty")
//    @APIResponse(responseCode = "204",
//            description = "Entity deleted")
//    @APIResponse(description = "Error",
//            content = @Content(mediaType = MediaType.APPLICATION_JSON,
//                    schema = @Schema(implementation = ErrorResponse.class)))
//    public void delete(
//            @Parameter(description = "Warranty ID")
//            @RestPath("id") final long id,
//            @HeaderParam("Authorization") final String token) {
//
//        service.delete(id);
//    }

    @GET
    @Operation(summary = "Lists warranty entities")
    @APIResponse(responseCode = "200", description = "Entities read")
    @APIResponse(description = "Error",
            content = @Content(mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = ErrorResponse.class)))
    public BaseResponseBoundary<List<WarrantyBoundaryResponse>> list(
            @QueryParam("page") final Integer page,
            @QueryParam("perPage") final Integer perPage,
            @QueryParam("order") final String order,
            @QueryParam("sort") final String sort,
            @QueryParam("filter") final String filter) throws Exception {
        try {
            return new BaseResponseBoundary<>(
                    WarrantyMapper.INSTANCE.entitiesToResponses(
                            service.list(perPage, page, order, sort, getFilters(filter))));
        } catch (Exception e) {

            throw new Exception("Error status 500");
        }
    }


    static class ResponseSchema extends BaseResponseBoundary<WarrantyBoundaryResponse> {

        public ResponseSchema(final WarrantyBoundaryResponse data) {
            super(data);
        }
    }

}
