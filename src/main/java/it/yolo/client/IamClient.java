package it.yolo.client;

import it.yolo.model.iam.CredentialsDTO;
import it.yolo.model.iam.LoginRequestDto;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;


@RegisterRestClient(configKey = "authorization-token")
@Path("/v1/iam/")
public interface IamClient {

    @Path("login")
    @POST
    Response getToken(LoginRequestDto loginRequestDto);

    @Path("userInfo")
    @GET
    CredentialsDTO getUserInfo(@HeaderParam("Authorization") String authToken);
}
