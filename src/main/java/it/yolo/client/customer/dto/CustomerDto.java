package it.yolo.client.customer.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerDto {

    @JsonProperty("data")
    private Data data;
    @JsonProperty("data")
    public Data getData() {
        return data;
    }
    @JsonProperty("data")
    public void setData(Data data) {
        this.data = data;
    }
}
