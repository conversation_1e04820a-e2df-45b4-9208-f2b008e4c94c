package it.yolo.client;


import it.yolo.client.response.client.packet.PacketResponse;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;

@Path("/v2/packets")
@RegisterRestClient(configKey="product")
public interface PacketClient {
    @GET
    @Path("{id}")
    PacketResponse findById(@PathParam("id") Long id, @RestHeader("Authorization") String token);

}
