
package it.yolo.client.response.client.product;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.JsonNode;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "id",
    "content",
    "position",
    "externalCode",
    "answers",
    "rule"
})
public class Question {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("content")
    private String content;
    @JsonProperty("position")
    private Integer position;
    @JsonProperty("externalCode")
    private Object externalCode;
    @JsonProperty("answers")
    private List<JsonNode> answers = null;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();
    @JsonProperty("packet_id")
    private Integer packetId;
    @JsonProperty("rule")
    private JsonNode rule;

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("content")
    public String getContent() {
        return content;
    }

    @JsonProperty("content")
    public void setContent(String content) {
        this.content = content;
    }

    @JsonProperty("position")
    public Integer getPosition() {
        return position;
    }

    @JsonProperty("position")
    public void setPosition(Integer position) {
        this.position = position;
    }

    @JsonProperty("externalCode")
    public Object getExternalCode() {
        return externalCode;
    }

    @JsonProperty("externalCode")
    public void setExternalCode(Object externalCode) {
        this.externalCode = externalCode;
    }

    @JsonProperty("answers")
    public List<JsonNode> getAnswers() {
        return answers;
    }

    @JsonProperty("answers")
    public void setAnswers(List<JsonNode> answers) {
        this.answers = answers;
    }

    @JsonProperty("packet_id")
    public Integer getPacketId() {
        return packetId;
    }

    @JsonProperty("packet_id")
    public void setPacketId(Integer packetId) {
        this.packetId = packetId;
    }

    @JsonProperty("rule")
    public JsonNode getRule() {
        return rule;
    }

    @JsonProperty("rule")
    public void setRule(JsonNode rule) {
        this.rule = rule;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
