
package it.yolo.client.adp.customer.dto;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "id",
    "email",
    "firstname",
    "lastname",
    "profession",
    "education",
    "salary",
    "channel",
    "created_by_id",
    "utm_source",
    "business",
    "user_acceptances",
    "locked_anagraphic",
    "data",
    "confirmed",
    "signed_in_once",
    "address",
    "empty_address",
    "is_same_address",
    "payment_methods"
})
@Generated("jsonschema2pojo")
public class AdpCustomerDto {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("email")
    private String email;
    @JsonProperty("firstname")
    private String firstname;
    @JsonProperty("lastname")
    private String lastname;
    @JsonProperty("profession")
    private String profession;
    @JsonProperty("education")
    private String education;
    @JsonProperty("salary")
    private String salary;
    @JsonProperty("channel")
    private Object channel;
    @JsonProperty("created_by_id")
    private Object createdById;
    @JsonProperty("utm_source")
    private String utmSource;
    @JsonProperty("business")
    private Boolean business;
    @JsonProperty("user_acceptances")
    private List<UserAcceptance> userAcceptances;
    @JsonProperty("locked_anagraphic")
    private Boolean lockedAnagraphic;
    @JsonProperty("data")
    private Data data;
    @JsonProperty("confirmed")
    private Boolean confirmed;
    @JsonProperty("signed_in_once")
    private Boolean signedInOnce;
    @JsonProperty("address")
    private Address address;
    @JsonProperty("empty_address")
    private Boolean emptyAddress;
    @JsonProperty("is_same_address")
    private Object isSameAddress;
    @JsonProperty("payment_methods")
    private List<PaymentMethod> paymentMethods;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new LinkedHashMap<String, Object>();

    @JsonProperty("id")
    public Integer getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(Integer id) {
        this.id = id;
    }

    @JsonProperty("email")
    public String getEmail() {
        return email;
    }

    @JsonProperty("email")
    public void setEmail(String email) {
        this.email = email;
    }

    @JsonProperty("firstname")
    public String getFirstname() {
        return firstname;
    }

    @JsonProperty("firstname")
    public void setFirstname(String firstname) {
        this.firstname = firstname;
    }

    @JsonProperty("lastname")
    public String getLastname() {
        return lastname;
    }

    @JsonProperty("lastname")
    public void setLastname(String lastname) {
        this.lastname = lastname;
    }

    @JsonProperty("profession")
    public String getProfession() {
        return profession;
    }

    @JsonProperty("profession")
    public void setProfession(String profession) {
        this.profession = profession;
    }

    @JsonProperty("education")
    public String getEducation() {
        return education;
    }

    @JsonProperty("education")
    public void setEducation(String education) {
        this.education = education;
    }

    @JsonProperty("salary")
    public String getSalary() {
        return salary;
    }

    @JsonProperty("salary")
    public void setSalary(String salary) {
        this.salary = salary;
    }

    @JsonProperty("channel")
    public Object getChannel() {
        return channel;
    }

    @JsonProperty("channel")
    public void setChannel(Object channel) {
        this.channel = channel;
    }

    @JsonProperty("created_by_id")
    public Object getCreatedById() {
        return createdById;
    }

    @JsonProperty("created_by_id")
    public void setCreatedById(Object createdById) {
        this.createdById = createdById;
    }

    @JsonProperty("utm_source")
    public String getUtmSource() {
        return utmSource;
    }

    @JsonProperty("utm_source")
    public void setUtmSource(String utmSource) {
        this.utmSource = utmSource;
    }

    @JsonProperty("business")
    public Boolean getBusiness() {
        return business;
    }

    @JsonProperty("business")
    public void setBusiness(Boolean business) {
        this.business = business;
    }

    @JsonProperty("user_acceptances")
    public List<UserAcceptance> getUserAcceptances() {
        return userAcceptances;
    }

    @JsonProperty("user_acceptances")
    public void setUserAcceptances(List<UserAcceptance> userAcceptances) {
        this.userAcceptances = userAcceptances;
    }

    @JsonProperty("locked_anagraphic")
    public Boolean getLockedAnagraphic() {
        return lockedAnagraphic;
    }

    @JsonProperty("locked_anagraphic")
    public void setLockedAnagraphic(Boolean lockedAnagraphic) {
        this.lockedAnagraphic = lockedAnagraphic;
    }

    @JsonProperty("data")
    public Data getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(Data data) {
        this.data = data;
    }

    @JsonProperty("confirmed")
    public Boolean getConfirmed() {
        return confirmed;
    }

    @JsonProperty("confirmed")
    public void setConfirmed(Boolean confirmed) {
        this.confirmed = confirmed;
    }

    @JsonProperty("signed_in_once")
    public Boolean getSignedInOnce() {
        return signedInOnce;
    }

    @JsonProperty("signed_in_once")
    public void setSignedInOnce(Boolean signedInOnce) {
        this.signedInOnce = signedInOnce;
    }

    @JsonProperty("address")
    public Address getAddress() {
        return address;
    }

    @JsonProperty("address")
    public void setAddress(Address address) {
        this.address = address;
    }

    @JsonProperty("empty_address")
    public Boolean getEmptyAddress() {
        return emptyAddress;
    }

    @JsonProperty("empty_address")
    public void setEmptyAddress(Boolean emptyAddress) {
        this.emptyAddress = emptyAddress;
    }

    @JsonProperty("is_same_address")
    public Object getIsSameAddress() {
        return isSameAddress;
    }

    @JsonProperty("is_same_address")
    public void setIsSameAddress(Object isSameAddress) {
        this.isSameAddress = isSameAddress;
    }

    @JsonProperty("payment_methods")
    public List<PaymentMethod> getPaymentMethods() {
        return paymentMethods;
    }

    @JsonProperty("payment_methods")
    public void setPaymentMethods(List<PaymentMethod> paymentMethods) {
        this.paymentMethods = paymentMethods;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
