package it.yolo.client.comunicationManager.templeate.withdraw.tim.customer;

import it.yolo.client.comunicationManager.dto.TemplatePlaceholder;
import it.yolo.client.comunicationManager.templeate.Templeate;
import it.yolo.client.comunicationManager.templeate.dto.TemplateDto;
import it.yolo.client.comunicationManager.templeate.dto.TempleateRequest;
import it.yolo.client.comunicationManager.templeate.dto.TempleateResponse;

import java.util.ArrayList;
import java.util.List;

public class TimSkiCompanyWithdrawalTemplate implements Templeate<TempleateResponse, TempleateRequest> {
    public TempleateResponse generate(TempleateRequest request) {
        TemplateDto responseDto=new TemplateDto();
        responseDto.getOptions().setToMail("<EMAIL>");
        responseDto.getOptions().setLanguage("ita");
        responseDto.getOptions().setMessaggetype("html");
        List<TemplatePlaceholder> placeholders = new ArrayList<>();
        placeholders.add(new TemplatePlaceholder("numeroOrdine", request.getOrderCode()));
        placeholders.add(new TemplatePlaceholder("numeroPolizza", request.getPolicyCode()));
        placeholders.add(new TemplatePlaceholder("numeroPolizzaEsterno", request.getPolicyCode()));
        placeholders.add(new TemplatePlaceholder("messaggio", request.getMessage()));
        responseDto.getOptions().setTemplatePlaceholder(placeholders);
        responseDto.getMessage().setKey("TIM_FOR_SKY_SCI&SNOWBOARD_RICHIESTA_RECESSO");
        TempleateResponse response = new TempleateResponse();
        response.setTemplateDto(responseDto);
        return response;
    }
}