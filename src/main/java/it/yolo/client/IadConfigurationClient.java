package it.yolo.client;

import java.util.List;

import javax.ws.rs.GET;
import javax.ws.rs.Path;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import it.yolo.model.error.ErrorCodeAndValueResponseDto;

@Path("/v1/cms")
@RegisterRestClient(configKey = "iad-configuration")
public interface IadConfigurationClient {

    @GET
    @Path("/error-messages")
    List<ErrorCodeAndValueResponseDto> getErrorMessages(@RestHeader("Authorization") String token);
}
