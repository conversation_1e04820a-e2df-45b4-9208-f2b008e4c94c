package it.yolo.client;

import com.fasterxml.jackson.databind.JsonNode;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@RegisterRestClient(configKey = "iad-configuration")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public interface IadConfigurationClient {

    @Path("/v1/cms/error-messages")
    @GET
    JsonNode getErrors(@HeaderParam("Authorization") String authToken);
}
