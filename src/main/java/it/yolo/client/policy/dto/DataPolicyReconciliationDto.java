package it.yolo.client.policy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;


@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataPolicyReconciliationDto {
    @JsonProperty("ids")
    private List<Long> ids;
    @JsonProperty("customerId")
    private String customerId;
    @JsonProperty("ids")
    public List<Long> getIds() {
        return ids;
    }
    @JsonProperty("ids")
    public void setIds(List<Long> ids) {
        this.ids = ids;
    }
    @JsonProperty("customerId")
    public String getCustomerId() {
        return customerId;
    }
    @JsonProperty("customerId")
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
}
