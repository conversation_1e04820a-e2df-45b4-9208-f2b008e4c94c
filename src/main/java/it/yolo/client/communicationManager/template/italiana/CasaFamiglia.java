package it.yolo.client.communicationManager.template.italiana;

import it.yolo.client.communicationManager.dto.Attachments;
import it.yolo.client.communicationManager.dto.TemplatePlaceholder;
import it.yolo.client.communicationManager.dto.TemplateResponseDto;
import it.yolo.client.communicationManager.template.Template;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;
import it.yolo.client.customer.dto.DataCustomerResponseDto;
import it.yolo.client.order.dto.response.DataOrderResponseDto;

import javax.enterprise.context.RequestScoped;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@RequestScoped
public class CasaFamiglia implements Template {
    private static final Logger LOGGER = Logger.getLogger(CasaFamiglia.class.getName());
    
    private static final String CONTENT_TYPE_PDF = "application/pdf";
    private static final String SET_INFORMATIVO_FILE_NAME = "SET_INFORMATIVO_ITALIANA.pdf";
    private static final String CERTIFICATO_FILE_NAME = "CERTIFICATO";
    private static final String MESSAGE_KEY = "EMISSIONE_POLIZZA";
    private static final String HTML_TYPE = "html";

    @Override
    public TemplateResponseDto generate(TemplateRequest templateRequest) {
        TemplateResponseDto responseDto = new TemplateResponseDto();
        
        // Configurazione delle opzioni base
        configureBaseOptions(responseDto, templateRequest);
        
        // Aggiunta dei placeholder nel template
        addTemplatePlaceholders(responseDto, templateRequest);
        
        // Preparazione degli allegati
        List<Attachments> attachmentsList = new ArrayList<>();
        
        // Aggiunta del certificato come allegato
        //addCertificateAttachment(attachmentsList, templateRequest);
        
        // Aggiunta del set informativo come allegato
        addInformativeSetAttachment(attachmentsList, templateRequest);
        
        // Configurazione finale
        responseDto.setAttachment(attachmentsList);
        responseDto.getMessage().setKey(MESSAGE_KEY);
        
        return responseDto;
    }
    
    private void configureBaseOptions(TemplateResponseDto responseDto, TemplateRequest templateRequest) {
        DataCustomerResponseDto customerData = templateRequest.getEmissionRequestDto().getCustomer().getData();
        DataOrderResponseDto orderResponse = templateRequest.getEmissionRequestDto().getOrder().getResponse();

        responseDto.getOptions().setToMail(customerData.getPrimaryMail());
        responseDto.getOptions().setMessaggetype(HTML_TYPE);
        responseDto.getOptions().setLanguage(orderResponse.getLanguage() != null ? orderResponse.getLanguage() : "it_IT");
        responseDto.getOptions().setName(customerData.getName());
        responseDto.getOptions().setSurname(customerData.getSurname());
    }
    
    private void addTemplatePlaceholders(TemplateResponseDto responseDto, TemplateRequest templateRequest) {
        // Aggiunta condizionale del frontend URL
        if (templateRequest.getEmissionRequestDto().getProduct().get("data").get("configuration").get("properties").has("feUrl")) {
            responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder(
                    "frontEndUrl", 
                    templateRequest.getEmissionRequestDto().getProduct().get("data").get("configuration").get("properties").get("feUrl").asText()
            ));
        }
        
        // Aggiunta degli altri placeholder
        responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder(
                "productName", 
                templateRequest.getEmissionRequestDto().getProduct().get("data").get("description").asText()));
        
        responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder(
                "policyNumber", 
                templateRequest.getEmissionRequestDto().getPolicy().getPolicyCode()));
        
        responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder(
                "startDate", 
                templateRequest.getEmissionRequestDto().getPolicy().getStartDate()));
        
        responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder(
                "endDate", 
                templateRequest.getEmissionRequestDto().getPolicy().getEndDate()));
        
        responseDto.getOptions().getTemplatePlaceholder().add(new TemplatePlaceholder(
                "orderNumber", 
                templateRequest.getEmissionRequestDto().getOrder().getResponse().getOrderCode()));
    }
    
    private void addCertificateAttachment(List<Attachments> attachmentsList, TemplateRequest templateRequest) {
        String certificateType = templateRequest.getCertificateResponseDto().getType();
        String contentType = certificateType.contains("pdf") ? CONTENT_TYPE_PDF : certificateType;
        
        attachmentsList.add(new Attachments(
                CERTIFICATO_FILE_NAME + "." + certificateType,
                contentType, 
                templateRequest.getCertificateResponseDto().getFile()));
    }
    
    private void addInformativeSetAttachment(List<Attachments> attachmentsList, TemplateRequest templateRequest) {
        String informativeSetUrl = templateRequest.getEmissionRequestDto().getProduct().get("data").get("informativeSet").asText();
        String base64Content = downloadAndEncodeToBase64(informativeSetUrl);
        
        if (base64Content != null) {
            attachmentsList.add(new Attachments(SET_INFORMATIVO_FILE_NAME, CONTENT_TYPE_PDF, base64Content));
        }
    }
    
    private String downloadAndEncodeToBase64(String urlString) {
        try {
            URL url = new URL(urlString);
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            
            try (InputStream in = new BufferedInputStream(url.openStream())) {
                in.transferTo(Base64.getEncoder().wrap(out));
                return out.toString();
            }
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "Errore durante il download o la codifica del file", e);
            return null;
        }
    }
}
