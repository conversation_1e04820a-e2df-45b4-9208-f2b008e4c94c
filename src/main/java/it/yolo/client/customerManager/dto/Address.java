package it.yolo.client.customerManager.dto;

import com.fasterxml.jackson.annotation.*;

import javax.annotation.Generated;
import java.util.LinkedHashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "address1",
        "birth_country",
        "birth_state",
        "birth_city",
        "birth_date",
        "city",
        "country",
        "firstname",
        "lastname",
        "phone",
        "sex",
        "state",
        "taxcode",
        "zipcode"
})
@Generated("jsonschema2pojo")
public class Address {

    @JsonProperty("address1")
    private String address1;
    @JsonProperty("birth_country")
    private BirthCountry birthCountry;
    @JsonProperty("birth_state")
    private BirthState birthState;
    @JsonProperty("birth_city")
    private BirthCity birthCity;
    @JsonProperty("birth_date")
    private String birthDate;
    @JsonProperty("city")
    private String city;
    @JsonProperty("country")
    private Country country;
    @JsonProperty("firstname")
    private String firstname;
    @JsonProperty("lastname")
    private String lastname;
    @JsonProperty("phone")
    private String phone;
    @JsonProperty("sex")
    private String sex;
    @JsonProperty("state")
    private State state;
    @JsonProperty("taxcode")
    private String taxcode;
    @JsonProperty("zipcode")
    private String zipcode;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new LinkedHashMap<String, Object>();

    @JsonProperty("address1")
    public String getAddress1() {
        return address1;
    }

    @JsonProperty("address1")
    public void setAddress1(String address1) {
        this.address1 = address1;
    }

    @JsonProperty("birth_country")
    public BirthCountry getBirthCountry() {
        return birthCountry;
    }

    @JsonProperty("birth_country")
    public void setBirthCountry(BirthCountry birthCountry) {
        this.birthCountry = birthCountry;
    }

    @JsonProperty("birth_state")
    public BirthState getBirthState() {
        return birthState;
    }

    @JsonProperty("birth_state")
    public void setBirthState(BirthState birthState) {
        this.birthState = birthState;
    }

    @JsonProperty("birth_city")
    public BirthCity getBirthCity() {
        return birthCity;
    }

    @JsonProperty("birth_city")
    public void setBirthCity(BirthCity birthCity) {
        this.birthCity = birthCity;
    }

    @JsonProperty("birth_date")
    public String getBirthDate() {
        return birthDate;
    }

    @JsonProperty("birth_date")
    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    @JsonProperty("city")
    public String getCity() {
        return city;
    }

    @JsonProperty("city")
    public void setCity(String city) {
        this.city = city;
    }

    @JsonProperty("country")
    public Country getCountry() {
        return country;
    }

    @JsonProperty("country")
    public void setCountry(Country country) {
        this.country = country;
    }

    @JsonProperty("firstname")
    public String getFirstname() {
        return firstname;
    }

    @JsonProperty("firstname")
    public void setFirstname(String firstname) {
        this.firstname = firstname;
    }

    @JsonProperty("lastname")
    public String getLastname() {
        return lastname;
    }

    @JsonProperty("lastname")
    public void setLastname(String lastname) {
        this.lastname = lastname;
    }

    @JsonProperty("phone")
    public String getPhone() {
        return phone;
    }

    @JsonProperty("phone")
    public void setPhone(String phone) {
        this.phone = phone;
    }

    @JsonProperty("sex")
    public String getSex() {
        return sex;
    }

    @JsonProperty("sex")
    public void setSex(String sex) {
        this.sex = sex;
    }

    @JsonProperty("state")
    public State getState() {
        return state;
    }

    @JsonProperty("state")
    public void setState(State state) {
        this.state = state;
    }

    @JsonProperty("taxcode")
    public String getTaxcode() {
        return taxcode;
    }

    @JsonProperty("taxcode")
    public void setTaxcode(String taxcode) {
        this.taxcode = taxcode;
    }

    @JsonProperty("zipcode")
    public String getZipcode() {
        return zipcode;
    }

    @JsonProperty("zipcode")
    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}