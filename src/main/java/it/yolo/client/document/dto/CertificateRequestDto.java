package it.yolo.client.document.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.response.client.product.ProductResponse;
import it.yolo.model.BaseResponseBoundary;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CertificateRequestDto {
    @JsonProperty("order")
    private OrderResponseDto order;

    @JsonProperty("policy")
    private PolicyResponseDto policy;

    @JsonProperty("customer")
    private CustomerResponseDto customer;
    @JsonProperty("product")
    private ProductResponse product;
    @JsonProperty("tenant")
    private String tenant;

    @JsonProperty("product")
    public ProductResponse getProduct() {
        return product;
    }

    @JsonProperty("product")
    public void setProduct(ProductResponse product) {
        this.product = product;
    }

    @JsonProperty("order")
    public OrderResponseDto getOrder() {
        return order;
    }
    @JsonProperty("order")
    public void setOrder(OrderResponseDto order) {
        this.order = order;
    }

    @JsonProperty("customer")
    public CustomerResponseDto getCustomer() {
        return customer;
    }

    @JsonProperty("customer")
    public void setCustomer(CustomerResponseDto customer) {
        this.customer = customer;
    }

    @JsonProperty("certificate")
    public String getTenant() {
        return tenant;
    }

    @JsonProperty("certificate")
    public void setTenant(String tenant) {
        this.tenant = tenant;
    }

    @JsonProperty("policy")
    public PolicyResponseDto getPolicy() {
        return policy;
    }

    @JsonProperty("policy")
    public void setPolicy(PolicyResponseDto policy) {
        this.policy = policy;
    }
}
