package it.yolo.repository;

import io.quarkus.hibernate.orm.panache.PanacheRepository;
import it.yolo.entity.WarrantyPremium;

import javax.enterprise.context.ApplicationScoped;
import java.math.BigDecimal;
import java.util.List;

@ApplicationScoped
public class WarrantyPremiumRepository implements PanacheRepository<WarrantyPremium> {

    /**
     * Find warranty premiums by warranty IDs, benefit amount and duration in months
     * 
     * @param warrantyIds List of warranty IDs to search for
     * @param benefitAmount The benefit amount
     * @param durationMonths Duration in months
     * @return List of matching warranty premiums
     */
    public List<WarrantyPremium> findByWarrantyIdsAndBenefitAmountAndDurationMonths(
            List<Integer> warrantyIds, 
            BigDecimal benefitAmount, 
            Integer durationMonths) {
        
        return find("warrantyId in ?1 and benefitAmount = ?2 and durationMonths = ?3", 
                   warrantyIds, benefitAmount, durationMonths).list();
    }

    /**
     * Find warranty premium by single warranty ID, benefit amount and duration in months
     * 
     * @param warrantyId The warranty ID
     * @param benefitAmount The benefit amount
     * @param durationMonths Duration in months
     * @return Optional warranty premium
     */
    public WarrantyPremium findByWarrantyIdAndBenefitAmountAndDurationMonths(
            Integer warrantyId, 
            BigDecimal benefitAmount, 
            Integer durationMonths) {
        
        return find("warrantyId = ?1 and benefitAmount = ?2 and durationMonths = ?3", 
                   warrantyId, benefitAmount, durationMonths).firstResult();
    }

    /**
     * Check if premiums exist for all warranty IDs with given benefit amount and duration
     * 
     * @param warrantyIds List of warranty IDs to check
     * @param benefitAmount The benefit amount
     * @param durationMonths Duration in months
     * @return true if all warranty IDs have corresponding premiums
     */
    public boolean existsForAllWarrantyIds(
            List<Integer> warrantyIds, 
            BigDecimal benefitAmount, 
            Integer durationMonths) {
        
        long count = count("warrantyId in ?1 and benefitAmount = ?2 and durationMonths = ?3", 
                          warrantyIds, benefitAmount, durationMonths);
        return count == warrantyIds.size();
    }

    /**
     * Find missing warranty IDs that don't have premiums for given benefit amount and duration
     * 
     * @param warrantyIds List of warranty IDs to check
     * @param benefitAmount The benefit amount
     * @param durationMonths Duration in months
     * @return List of warranty IDs that don't have premiums
     */
    public List<Integer> findMissingWarrantyIds(
            List<Integer> warrantyIds, 
            BigDecimal benefitAmount, 
            Integer durationMonths) {
        
        List<WarrantyPremium> existingPremiums = findByWarrantyIdsAndBenefitAmountAndDurationMonths(
            warrantyIds, benefitAmount, durationMonths);
        
        List<Integer> existingWarrantyIds = existingPremiums.stream()
            .map(WarrantyPremium::getWarrantyId)
            .toList();
        
        return warrantyIds.stream()
            .filter(id -> !existingWarrantyIds.contains(id))
            .toList();
    }
}
