package it.yolo.common;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import it.yolo.client.response.client.packet.PacketData;
import it.yolo.entity.OrderItemEntity;
import it.yolo.model.CalculateCeilingsResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Utility class for calculating ceilings in product packets.
 * This class is specifically designed for use in packet filtering operations
 * and contains dedicated methods for calculating ceilings for product packets.
 * It shares a common implementation approach with Utility class.
 */
@Slf4j
public class CeilingsUtility {

    final static ObjectMapper MAPPER = new ObjectMapper();

    private CeilingsUtility() {
        // Private constructor to prevent instantiation
    }

    /**
     * Calculates ceilings for a packet based on order items and insured item data.
     *
     * @param orderItems List of order items
     * @param insuredItem JsonNode containing insured item data
     * @param packetData The packet data to calculate ceilings for
     * @return CalculateCeilingsResponse containing updated status and warranties with calculated ceilings
     */
    public static CalculateCeilingsResponse calculatePacketCeilings(List<OrderItemEntity> orderItems, JsonNode insuredItem, PacketData packetData) {
        ArrayNode warrantiesNode = JsonNodeFactory.instance.arrayNode();
        AtomicBoolean updated = new AtomicBoolean(false);

        // Cache configuration access
        JsonNode configuration = packetData.getConfiguration();
        JsonNode product = packetData.getProduct();
        JsonNode warranties = packetData.getWarranties();

        // Extract premium limits using cached references
        Double maxInsurancePremium = configuration.has("maxInsurancePremium") ?
                configuration.get("maxInsurancePremium").asDouble() : null;
        Double minInsurancePremium = configuration.has("minInsurancePremium") ?
                configuration.get("minInsurancePremium").asDouble() : null;

        // Pre-calculate available values once
        List<String> availableValues = new ArrayList<>();
        boolean shouldSetAvailableValues = shouldCalculateAvailableValues(product);

        if (shouldSetAvailableValues) {
            Utility.setAvailableValues(insuredItem, availableValues, configuration);
        }

        // Pre-convert to JsonNode to avoid multiple conversions
        ArrayNode jsonAvailableValues = shouldSetAvailableValues ? MAPPER.valueToTree(availableValues) : null;

        // Reuse the optimized processing function
        Utility.processWarranties(warranties, insuredItem, orderItems, warrantiesNode, updated,
                maxInsurancePremium, minInsurancePremium, jsonAvailableValues);

        // Reuse the optimized cleanup function
        Utility.cleanupWarrantyNodes(warrantiesNode);

        return new CalculateCeilingsResponse(updated.get(), warrantiesNode);
    }

    private static boolean shouldCalculateAvailableValues(JsonNode product) {
        return product != null &&
                product.has("configuration") &&
                product.get("configuration").has("properties") &&
                product.get("configuration").get("properties").has("availableValues") &&
                product.get("configuration").get("properties").get("availableValues").asBoolean();
    }
}
