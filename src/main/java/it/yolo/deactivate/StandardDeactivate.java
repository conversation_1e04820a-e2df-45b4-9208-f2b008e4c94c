package it.yolo.deactivate;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import it.yolo.claim.dto.CustomerCareDto;
import it.yolo.client.adpPg.AdpPgClient;
import it.yolo.client.comunicationManager.dto.*;
import it.yolo.client.comunicationManager.templeate.Templeate;
import it.yolo.client.comunicationManager.templeate.deactivate.fca.GeHomeTempleate;
import it.yolo.client.comunicationManager.templeate.deactivate.fca.NetPetGoldTempleate;
import it.yolo.client.comunicationManager.templeate.deactivate.fca.NetPetSilverTempleate;
import it.yolo.client.comunicationManager.templeate.deactivate.tim.customer.CustomersTimPetTempleate;
import it.yolo.client.comunicationManager.templeate.dto.TempleateRequest;
import it.yolo.client.comunicationManager.templeate.dto.TempleateResponse;
import it.yolo.client.policy.dto.PolicyDtoRequest;
import it.yolo.client.policy.request.ActionRequest;
import it.yolo.client.providerGateway.PgClient;
import it.yolo.mapper.ComunicationManagerMapper;
import it.yolo.service.*;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.annotation.PostConstruct;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.StreamSupport;

@RequestScoped
public class StandardDeactivate implements Deactivate<Object, CustomerCareDto> {

    @ConfigProperty(name = "pg.username")
    String username;

    @ConfigProperty(name = "pg.password")
    String password;

    @RestClient
    @Inject
    AdpPgClient adpPgClient;

    @Inject
    ServicePolicy servicePolicy;


    @Inject
    ServiceComunicationManager serviceComunicationManager;

    @Inject
    JsonWebToken jsonWebToken;

    @RestClient
    @Inject
    PgClient pgClient;

    Map<String, Templeate<TempleateResponse,TempleateRequest>> templeateMap;

    Map<String, InvokePaymentProvider> paymentProvidersMap;

    @Inject
    NetPetGoldTempleate netPetGoldDeactivate;
    @Inject
    NetPetSilverTempleate netPetSilverTempleate;

    @Inject
    GeHomeTempleate geHomeTempleate;

    @Inject
    CustomersTimPetTempleate customersTimPetTempleate;

    @Inject
    InvokeBraintree invokeBraintree;

    @Inject
    InvokeGup invokeGup;

    private static final String CANCELLATION = "cancellation";



    @PostConstruct
    void init() {
        templeateMap= new HashMap<>();
        paymentProvidersMap= new HashMap<>();
        templeateMap.put("net-pet-silver",netPetSilverTempleate);
        templeateMap.put("net-pet-gold",netPetGoldDeactivate);
        templeateMap.put("ge-home",geHomeTempleate);
        templeateMap.put("customers-tim-pet",customersTimPetTempleate);
        paymentProvidersMap.put("braintree", invokeBraintree);
        paymentProvidersMap.put("gup", invokeGup);
    }


    @Override
    public Object deactivation(CustomerCareDto customerCareDto) throws Exception {
        Response res;
        String subscriptionId = null;
        String productCode=customerCareDto.getProductDto().getData().getCode();
        if (StringUtils.isNotBlank(customerCareDto.getPolicyDto().getData().getSubscriptionId())) {
            subscriptionId = customerCareDto.getPolicyDto().getData().getSubscriptionId();
        }
        String policyCode=customerCareDto.getPolicyDto().getData().getPolicyCode();
        String token = "Bearer " + jsonWebToken.getRawToken();
        String deactivationUserMessageKey= StreamSupport.stream(customerCareDto.getProductDto().getData().getConfiguration()
                        .getProperties().get("properties").spliterator(),false).
                filter(prop->prop.get("name").asText().equalsIgnoreCase("DeactivationUserMessageKey")).
                map(prop->prop.get("value").asText()).findAny().get();
        customerCareDto.getPolicyDto().getData().getProduct().setCode(productCode);


        String deactivationProvider= StreamSupport.stream(customerCareDto.getProductDto().getData().getConfiguration()
                        .getProperties().get("properties").spliterator(),false).
                filter(prop->prop.get("name").asText().equalsIgnoreCase("DeactivationProvider")).
                map(prop->prop.get("value").asText()).findFirst().orElse(null);
        customerCareDto.getPolicyDto().getData().getProduct().setCode(productCode);

        if(StringUtils.isNotBlank(deactivationProvider) && deactivationProvider.equalsIgnoreCase("null")){
            deactivationProvider = null;
        }

        customerCareDto.getPolicyDto().getData().setUsername(username);
        customerCareDto.getPolicyDto().getData().setPassword(password);

        if(customerCareDto.getOrderResponse().getData().getOrderItem().get(0).getEmission().has("parameters") &&
                customerCareDto.getOrderResponse().getData().getOrderItem().get(0).getEmission().get("parameters").has("insurance_id")){
            customerCareDto.getPolicyDto().getData().setInsuranceId(customerCareDto.getOrderResponse().getData().getOrderItem().get(0)
                    .getEmission().get("parameters").get("insurance_id").asText());
        }
        try{
            Response responseAdp= adpPgClient.toPg(customerCareDto.getPolicyDto());
            JsonNode pgRequest= responseAdp.readEntity(JsonNode.class);
            res = pgClient.deactivate(pgRequest);
        }catch (Exception ex){
            throw new Exception("errore : " + ex.getMessage());
        }


        CommunicationManagerDtoRequest comunicationReq= customerCareToReq(customerCareDto,deactivationUserMessageKey);
        CommunicationManagerDtoResponse userMailResponse=serviceComunicationManager.sendEmail(comunicationReq);

        /*
        TemplateDto templateDto= generateTempleate(productCode,customerCareDto.getCustomerDto().getData().getPrimaryMail(),
                customerCareDto.getCustomerDto().getData().getName(), String.valueOf(customerCareDto.getPolicyDto().getData().getEndDate()));
        serviceComunicationManager.sendEmail(templateDto,token);
        */

        try{
         if(deactivationProvider!=null)   {
             InvokePaymentProvider invokePaymentProvider=paymentProvidersMap.get(deactivationProvider);
             Response responseUnsubscribe = invokePaymentProvider.unsubscribe(token, subscriptionId);
         }
        }catch (Exception ex){
            throw new Exception("errore durante la chiamata unsubscribe : " + ex.getMessage() + "id : " + subscriptionId);
        }

        try{
         PolicyDtoRequest responseAction= servicePolicy.actionPolicy(new ActionRequest(CANCELLATION, policyCode, customerCareDto.getPolicyDto().getData().getDeactivationReason()));
        }catch (Exception ex){
            throw new Exception("errore durante la chiamata al action policy : " + ex.getMessage() + "id : " + policyCode);
        }
        return res.readEntity(JsonNode.class);
    }

    public static CommunicationManagerDtoRequest customerCareToReq(CustomerCareDto req, String value){
        CommunicationManagerDtoRequest communicationManagerRequest = new CommunicationManagerDtoRequest();
        Options options= new ComunicationManagerMapper()
                .CustomerCareDtoToOption(req);
        if(req.getProductDto().getData().getConfiguration().getProperties().hasNonNull("deactivation") &&
                req.getProductDto().getData().getConfiguration().getProperties().get("deactivation").hasNonNull("cc")){
            ArrayNode ccArray=req.getProductDto().getData().getConfiguration().getProperties().get("deactivation").withArray("cc");
            List<String> cc=new ArrayList<>();
            ccArray.forEach(m-> cc.add(m.asText()));
            options.setCc(cc);
        }
        List<TemplatePlaceholder> placeholders=new ComunicationManagerMapper()
                .dtoToPlaceholders(req,value);

        options.setTemplatePlaceholder(placeholders);
        Message message= new Message();
        message.setKey(value);
        communicationManagerRequest.setOptions(options);
        communicationManagerRequest.setMessage(message);

        return  communicationManagerRequest;
    }
}
