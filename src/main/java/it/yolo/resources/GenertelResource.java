package it.yolo.resources;

import java.util.List;

import javax.inject.Inject;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import io.quarkus.logging.Log;
import org.jboss.resteasy.reactive.RestResponse;

import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.entity.GenertelBrandsEntity;
import it.yolo.entity.GenertelDisplacementsEntity;
import it.yolo.entity.GenertelPowerSuppliesEntity;
import it.yolo.service.UtilityService;

@Path("genertel")
public class GenertelResource {
    @Inject
    UtilityService utilityService;

    @GET
    @Path("/brands")
    @Produces(MediaType.APPLICATION_JSON)
    @WithSpan("GenertelResource.brands")
    public RestResponse<List<GenertelBrandsEntity>> brands() {
        Log.info("iad-utility: called genertel brands GET method");
        Log.info("call utility service, find all genertel brands");
        final List<GenertelBrandsEntity> genertelBrands = utilityService.findAllGenertalBrands();
        Log.info("return genertel brands in the response");
        return RestResponse.ResponseBuilder.ok(genertelBrands).build();
    }

    @GET
    @Path("/displacements")
    @Produces(MediaType.APPLICATION_JSON)
    @WithSpan("GenertelResource.displacements")
    public RestResponse<List<GenertelDisplacementsEntity>> displacements() {
        Log.info("iad-utility: called genertel displacements GET method");
        Log.info("call utility service, find all genertel displacements");
        final List<GenertelDisplacementsEntity> genertelDisplacements = utilityService.findAllGenertelDisplacements();
        Log.info("return genertel displacements in the response");
        return RestResponse.ResponseBuilder.ok(genertelDisplacements).build();
    }

    @GET
    @Path("/power-supplies")
    @Produces(MediaType.APPLICATION_JSON)
    @WithSpan("GenertelResource.powerSupplies")
    public RestResponse<List<GenertelPowerSuppliesEntity>> powerSupplies() {
        Log.info("iad-utility: called genertel power supplies GET method");
        Log.info("call utility service, find all genertel power supplies");
        final List<GenertelPowerSuppliesEntity> genertelPowerSupplies = utilityService.findAllGenertelPowerSupply();
        Log.info("return genertel power supplies in the response");
        return RestResponse.ResponseBuilder.ok(genertelPowerSupplies).build();
    }
}
