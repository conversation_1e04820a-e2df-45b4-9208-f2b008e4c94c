package it.yolo.resources;

import com.fasterxml.jackson.databind.JsonNode;
import io.quarkus.security.Authenticated;
import it.yolo.http.HttpStatus;
import it.yolo.http.ResponseCode;
import it.yolo.model.error.ErrorResponse;
import it.yolo.model.request.CustomerCareRequest;
import it.yolo.model.response.CustomerCareResponse;
import it.yolo.service.ServiceCustomerCare;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.ResponseStatus;
import org.jboss.resteasy.reactive.RestResponse;

import javax.inject.Inject;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.core.MediaType;

@Path("/v1")
public class CustomerCareResources {

     @Inject
     ServiceCustomerCare serviceCustomerCare;

     @Inject
     Logger logger;

    @PUT
    @Path("policy/updated-warranties")
    @Authenticated
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "update warranties on domain policy")
    @APIResponse(responseCode = ResponseCode.OK, description = "update ok ", content = @Content(mediaType = MediaType.APPLICATION_JSON))
    @APIResponse(description = "Error", content = @Content(mediaType = MediaType.APPLICATION_JSON, schema = @Schema(implementation = ErrorResponse.class)))
    public RestResponse<CustomerCareResponse> updateWarranties(
            @HeaderParam("Authorization") String token,
            CustomerCareRequest req) throws Exception {
        CustomerCareResponse res;
        logger.info("CustomerCareResources.updateWarranties() start with request: "+ req);
        res= serviceCustomerCare.updateWarranties(req);
        logger.info("CustomerCareResources.updateWarranties() end with response: "+ res);
        return RestResponse.ResponseBuilder.ok(res).build();
    }
}
