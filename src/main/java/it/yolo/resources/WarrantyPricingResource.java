package it.yolo.resources;

import com.fasterxml.jackson.databind.JsonNode;
import io.quarkus.security.Authenticated;
import it.yolo.entity.WarrantyPremium;
import it.yolo.exception.IamException;
import it.yolo.model.*;
import it.yolo.service.IamService;
import it.yolo.service.OrderService;
import it.yolo.service.WarrantyPricingService;
import org.eclipse.microprofile.metrics.annotation.Counted;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.parameters.RequestBody;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.math.BigDecimal;
import java.util.List;

@Path("v2")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class WarrantyPricingResource {

    private static final Logger LOG = LoggerFactory.getLogger(WarrantyPricingResource.class);

    private final WarrantyPricingService warrantyPricingService;
    private final OrderService orderService;
    private final IamService technicalTokenService;

    @Inject
    public WarrantyPricingResource(WarrantyPricingService warrantyPricingService, OrderService orderService, IamService technicalTokenService) {
        this.warrantyPricingService = warrantyPricingService;
        this.orderService = orderService;
        this.technicalTokenService = technicalTokenService;
    }

    @POST
    @Path("/quote")
    @Authenticated
    @Counted(name = "orderQuoteRequests", description = "Numero di richieste all'endpoint order-quote")
    @Timed(name = "orderQuoteTimer", description = "Tempo di esecuzione dell'endpoint order-quote")
    @Operation(summary = "Calcola la quotazione di un ordine di garanzie", description = "Restituisce la quotazione annuale e mensile per le garanzie richieste.")
    @RequestBody(
        required = true,
        content = @Content(schema = @Schema(implementation = BaseData.class))
    )
    @APIResponses({
        @APIResponse(responseCode = "200", description = "Quotazione aggiornata con successo"),
        @APIResponse(responseCode = "400", description = "Richiesta non valida"),
        @APIResponse(responseCode = "404", description = "Ordine o prezzo non trovato"),
        @APIResponse(responseCode = "500", description = "Errore interno del server")
    })
    public Response getQuoteFromOrder(@Valid BaseData<OrderQuoteRequest> request) throws IamException {
        LOG.info("[order-quote] Ricevuta richiesta: {}", request);
        String technicalToken = technicalTokenService.getToken();
        OrderQuoteRequest quoteRequest = request.getData();
        String orderCode = quoteRequest.orderCode();
        LOG.debug("[order-quote] Recupero ordine con codice: {}", orderCode);
        JsonNode orderJson = orderService.getOrder(orderCode, technicalToken);

        // Estrai TUTTE le garanzie da filterWarranties per la quotazione
        List<WarrantyDetail> allWarrantyDetails = orderService.extractAllWarrantyDetailsFromOrder(orderJson);
        if (allWarrantyDetails.isEmpty()) {
            LOG.warn("[order-quote] Nessuna garanzia trovata in filterWarranties per ordine {}", orderCode);
            throw new IllegalArgumentException("No warranty details found in filterWarranties");
        }

        // Estrai gli ID delle garanzie selezionate da chosenWarranties per il calcolo del totale
        List<Integer> chosenWarrantyIds = orderService.extractChosenWarrantyIdsFromOrder(orderJson);
        LOG.debug("[order-quote] Garanzie selezionate: {}, Tutte le garanzie: {}",
                 chosenWarrantyIds.size(), allWarrantyDetails.size());
        int durationYears = orderService.extractPackageDurationFromOrder(orderJson);
        LOG.debug("[order-quote] Durata estratta: {} anni", durationYears);

        // Calcola i premi per TUTTE le garanzie (da filterWarranties)
        List<WarrantyPremium> annualPremiums = warrantyPricingService.getPremiums(allWarrantyDetails, durationYears);
        List<WarrantyPremium> monthlyPremiums = warrantyPricingService.getMonthlyPremiums(allWarrantyDetails);

        // Calcola i totali SOLO per le garanzie selezionate (da chosenWarranties)
        BigDecimal totalAnnualPremium = warrantyPricingService.calculateTotalPremiumForSelected(annualPremiums, chosenWarrantyIds);
        BigDecimal totalMonthlyPremium = warrantyPricingService.calculateTotalPremiumForSelected(monthlyPremiums, chosenWarrantyIds);
        String premiumCurrency = warrantyPricingService.getPremiumCurrency(annualPremiums);
        String benefitCurrency = warrantyPricingService.getBenefitCurrency(annualPremiums);
        LOG.info("[order-quote] Calcolo completato per ordine {}. Garanzie quotate: {}, Garanzie selezionate: {}, Totale annuale: {}, Totale mensile: {}",
                 orderCode, allWarrantyDetails.size(), chosenWarrantyIds.size(), totalAnnualPremium, totalMonthlyPremium);
        Response updatedResponse = orderService.updateOrderWithQuotation(
            orderCode,
            annualPremiums,
            totalAnnualPremium,
            premiumCurrency,
            benefitCurrency,
            monthlyPremiums,
            totalMonthlyPremium,
            durationYears,
            technicalToken,
            chosenWarrantyIds
        );
        String entity = updatedResponse.readEntity(String.class);

        return Response.status(updatedResponse.getStatus())
                .entity(entity)
                .type(MediaType.APPLICATION_JSON)
                .build();
    }
}
