package it.yolo.entity;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "warranty_premiums", schema = "pricing")
public class WarrantyPremium {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "BIGINT")
    private Long id;

    @ManyToOne(fetch =  FetchType.LAZY)
    @JoinColumn(name = "warranty_id", nullable = false, updatable = false, columnDefinition = "BIGINT")
    private AnagWarranty warranty;

    @Column(name = "benefit_amount", nullable = false, length = 50)
    private String benefitAmount;

    @Column(name = "duration_months", nullable = false)
    private Integer durationMonths;

    @Column(name = "total_premium", nullable = false, precision = 15, scale = 2)
    private BigDecimal totalPremium;

    @Column(name = "benefit_currency", nullable = false, length = 3)
    private String benefitCurrency = "PLN";

    @Column(name = "premium_currency", nullable = false, length = 3)
    private String premiumCurrency = "PLN";

    @Column(name = "valid_from", nullable = false)
    private LocalDate validFrom;

    @Column(name = "valid_to")
    private LocalDate validTo;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Constructors
    public WarrantyPremium() {
        this.createdAt = LocalDateTime.now();
        this.validFrom = LocalDate.now();
    }

    public WarrantyPremium(String benefitAmount, Integer durationMonths, BigDecimal totalPremium) {
        this();
        this.benefitAmount = benefitAmount;
        this.durationMonths = durationMonths;
        this.totalPremium = totalPremium;
    }

    public WarrantyPremium(String benefitAmount, Integer durationMonths, BigDecimal totalPremium,
                          String benefitCurrency, String premiumCurrency) {
        this(benefitAmount, durationMonths, totalPremium);
        this.benefitCurrency = benefitCurrency;
        this.premiumCurrency = premiumCurrency;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWarrantyId() {
        return warranty.getId();
    }

    public void setWarrantyId(Long warrantyId) {
        this.warranty.setId(warrantyId);
    }

    public String getBenefitAmount() {
        return benefitAmount;
    }

    public void setBenefitAmount(String benefitAmount) {
        this.benefitAmount = benefitAmount;
    }

    public Integer getDurationMonths() {
        return durationMonths;
    }

    public void setDurationMonths(Integer durationMonths) {
        this.durationMonths = durationMonths;
    }

    public BigDecimal getTotalPremium() {
        return totalPremium;
    }

    public void setTotalPremium(BigDecimal totalPremium) {
        this.totalPremium = totalPremium;
    }

    public String getBenefitCurrency() {
        return benefitCurrency;
    }

    public void setBenefitCurrency(String benefitCurrency) {
        this.benefitCurrency = benefitCurrency;
    }

    public String getPremiumCurrency() {
        return premiumCurrency;
    }

    public void setPremiumCurrency(String premiumCurrency) {
        this.premiumCurrency = premiumCurrency;
    }

    public LocalDate getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(LocalDate validFrom) {
        this.validFrom = validFrom;
    }

    public LocalDate getValidTo() {
        return validTo;
    }

    public void setValidTo(LocalDate validTo) {
        this.validTo = validTo;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public void setWarranty(AnagWarranty warranty) {
        this.warranty = warranty;
    }

    public String getInternalCode() {
        return warranty != null ? warranty.getInternalCode() : null;
    }

    @Override
    public String toString() {
        return "WarrantyPremium{" +
                "id=" + id +
                ", warrantyId=" + warranty.getId() +
                ", benefitAmount=" + benefitAmount +
                ", durationMonths=" + durationMonths +
                ", totalPremium=" + totalPremium +
                ", benefitCurrency='" + benefitCurrency + '\'' +
                ", premiumCurrency='" + premiumCurrency + '\'' +
                ", validFrom=" + validFrom +
                ", validTo=" + validTo +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
