package it.yolo.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;

@Entity
@Table(schema = "utility", name = "genertel_displacements")
public class GenertelDisplacementsEntity {
    @Id
    @Getter
    @Setter
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Getter
    @Setter
    @Column(name = "cc_displacement")
    private String cc_displacement;

    @Getter
    @Setter
    @Column(name = "cv_displacement")
    private String cv_displacement;

    @Getter
    @Setter
    @Column(name = "quote_value")
    private String quote_value;
}
