package it.yolo.entity;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.databind.JsonNode;
import io.quarkiverse.hibernate.types.json.JsonTypes;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "customers", schema = "customer")
@NamedQuery(name = "Customer.updateCustomer",
        query = "update CustomerEntity c set c.tax_code = :tax_code where c.id = :id")
@NamedQuery(name = "Customer.listCustomers",
        query = "select c from CustomerEntity c where " +
                "(:taxId is null or c.tax_code = :tax_code)")
public class CustomerEntity {

    @Id
    @Getter
    @Setter
    @Column(columnDefinition = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Getter
    @Setter
    @Column(name = "customer_code", length = 50)
    private String customer_code;

    @Getter
    @Setter
    @Type(type = JsonTypes.JSON_BIN)
    @Column( name ="external_code")
    private JsonNode external_code;

    @Getter
    @Setter
    @Column(name = "username", length = 50)
    private String username;

    @Getter
    @Setter
    @Column(name = "name", length = 50)
    private String name;

    @Getter
    @Setter
    @Column(name = "surname", length = 50)
    private String surname;

    @Getter
    @Setter
    @Column(name = "date_of_birth", length = 50)
    private String date_of_birth;

    @Getter
    @Setter
    @Column(name = "birth_city", length = 50)
    private String birth_city;

    @Getter
    @Setter
    @Column(name = "birth_country", length = 50)
    private String birth_country;

    @Getter
    @Setter
    @Column(name = "tax_code", length = 50,nullable = false)
    private String tax_code;

    @Getter
    @Setter
    @Column(name = "gender", length = 50)
    private String gender;

    @Getter
    @Setter
    @Column(name = "street", length = 50)
    private String street;

    @Getter
    @Setter
    @Column(name = "city", length = 50)
    private String city;

    @Getter
    @Setter
    @Column(name = "country", length = 50)
    private String country;

    @Getter
    @Setter
    @Column(name = "zip_code", length = 50)
    private String zip_code;

    @Getter
    @Setter
    @Column(name = "primary_mail", length = 50)
    private String primary_mail;

    @Getter
    @Setter
    @Column(name = "secondary_mail", length = 50)
    private String secondary_mail;

    @Getter
    @Setter
    @Column(name = "primary_phone", length = 50)
    private String primary_phone;

    @Getter
    @Setter
    @Column(name = "secondary_phone", length = 50)
    private String secondary_phone;

    @Getter
    @Setter
    @Column(name = "language", length = 50)
    private String language;

    @Getter
    @Setter
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Getter
    @Setter
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Getter
    @Setter
    @Column(name = "street_number", length = 50)
    private String street_number;

    @Getter
    @Setter
    @OneToMany(mappedBy = "user", cascade = CascadeType.MERGE,
            fetch = FetchType.EAGER)
      @JsonManagedReference
    private List<UserAcceptancesEntity> userAcceptances;

    @Getter
    @Setter
    @Column(name="education")
    private String education;

    @Getter
    @Setter
    @Column(name="salary")
    private String salary;

    @Getter
    @Setter
    @Column(name="profession")
    private String profession;

    @Getter
    @Setter
    @Column(name="birth_state")
    private String birth_state;

    @Getter
    @Setter
    @Column(name="state_id")
    private Long state_id;

    @Getter
    @Setter
    @Column(name="country_id")
    private Long country_id;

    @Getter
    @Setter
    @Column(name="ndg")
    private String ndg;

    @Getter
    @Setter
    @Column(name="vatcode")
    private String vatcode;

    @Getter
    @Setter
    @Column(name="company")
    private String company;

    @Getter
    @Setter
    @Column(name="legal_form")
    private Boolean legal_form;

    @Getter
    @Setter
    @Column(name="city_id")
    private Integer city_id;

    @Getter
    @Setter
    @Column(name="birth_state_id")
    private Integer birth_state_id;

    @Getter
    @Setter
    @Column(name="birth_city_id")
    private Integer birth_city_id;

    @Getter
    @Setter
    @Column(name="birth_country_id")
    private Integer birth_country_id;

    @Getter
    @Setter
    @Column(name = "state")
    private String state;

    @Getter
    @Setter
    @Column(name = "state_abbr")
    private String state_abbr;

    @Getter
    @Setter
    @Column(name = "birth_state_abbr")
    private String birth_state_abbr;

    @Getter
    @Setter
    @Column(name = "id_card")
    private String id_card;

    @Getter
    @Setter
    @Column(name = "retired_code")
    private String retired_code;


    @Getter
    @Setter
    @Column(name = "registration_info")
    @Type(type = JsonTypes.JSON_BIN)
    private JsonNode registration_info;

    @Getter
    @Setter
    @Column(name = "ndg_provider")
    private String ndg_provider;

    @Getter
    @Setter
    @Column(name = "utm_source")
    private String utm_source;

    @Getter
    @Setter
    @Transient
    private String countryCode;

    @Getter
    @Setter
    @Transient
    private String cityCode;

    @Getter
    @Setter
    @Column(name = "address_change_date")
    private LocalDateTime addressChangeDate;

    @Getter
    @Setter
    @Column(name="individual_firm")
    private Boolean individualFirm;

    @Getter
    @Setter
    @Column(name="vat_tax_difference")
    private Boolean vatTaxDifference;

    @Getter
    @Setter
    @Column(name = "sdi_code")
    private String sdiCode;

    @Getter
    @Setter
    @Column(name = "certified_mail")
    private String certifiedMail;

    @Getter
    @Setter
    @OneToMany(mappedBy = "customer", cascade = CascadeType.MERGE, fetch = FetchType.EAGER)
    @JsonManagedReference
    private Set<LegalRepresentativeInfoEntity> legalRepresentativeInfo;
}
