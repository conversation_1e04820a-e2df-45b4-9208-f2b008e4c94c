package it.yolo.entity;

import io.quarkus.hibernate.orm.PersistenceUnitExtension;
import io.quarkus.hibernate.orm.runtime.tenant.TenantResolver;
import io.vertx.ext.web.RoutingContext;
import org.eclipse.microprofile.jwt.JsonWebToken;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

@RequestScoped
@PersistenceUnitExtension
public class YoloTenantResolver implements TenantResolver {

    public static final String DEFAULT_TENANT_ID = "iad";

    public static final String PROP_TENANT_ID = "it.yolo.tenant";

    @Inject
    JsonWebToken jwt;

    @Inject
    RoutingContext context;

    @Override
    public String getDefaultTenantId() {
        return YoloTenantResolver.DEFAULT_TENANT_ID;
    }

    @Override
    public String resolveTenantId() {
        String tenant = jwt.getSubject();
        if (tenant == null) {
            tenant = getDefaultTenantId();
        }

        context.put(YoloTenantResolver.PROP_TENANT_ID, tenant);
        return tenant;
    }
}
