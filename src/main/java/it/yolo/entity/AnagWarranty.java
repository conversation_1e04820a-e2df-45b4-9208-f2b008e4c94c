package it.yolo.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "anag_warranties", schema = "product")
public class AnagWarranty {
    @Id
    @Column(name="id", columnDefinition = "BIGINT")
    private Integer id;

    @Column(name="internal_code", length = 255)
    private String internalCode;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getInternalCode() {
        return internalCode;
    }

    public void setInternalCode(String internalCode) {
        this.internalCode = internalCode;
    }
}
