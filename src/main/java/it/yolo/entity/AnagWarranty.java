package it.yolo.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "anag_warranties", schema = "product")
public class AnagWarranty {
    @Id
    @Column(name="id", columnDefinition = "BIGINT")
    private Long id;

    @Column(name="internal_code", length = 255)
    private String internalCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getInternalCode() {
        return internalCode;
    }

    public void setInternalCode(String internalCode) {
        this.internalCode = internalCode;
    }
}
