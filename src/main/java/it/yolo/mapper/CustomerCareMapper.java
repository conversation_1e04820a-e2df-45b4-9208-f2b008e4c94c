package it.yolo.mapper;

import it.yolo.client.dto.iad.response.OrderItem;
import it.yolo.client.order.request.DataOrderRequestDto;
import it.yolo.client.order.request.OrderRequestDto;
import it.yolo.client.order.response.OrderDtoResponse;
import it.yolo.client.policy.dto.DataPolicyRequest;
import it.yolo.client.policy.dto.PolicyDtoRequest;
import it.yolo.model.request.CustomerCareRequest;

import java.util.ArrayList;
import java.util.List;

public class CustomerCareMapper {



    public OrderRequestDto requestToOrderRequestDto(CustomerCareRequest request){
        List<OrderItem> orderItems = new ArrayList<>();

        OrderRequestDto requestDto = new OrderRequestDto();
        //set data
        DataOrderRequestDto dataRequest= new DataOrderRequestDto();
        dataRequest.setOrderCode(request.getOrderCode());
        dataRequest.setProductId(request.getProductId());
        dataRequest.setPacketId(request.getPacketId());
        //create a orderItem and set a list
        OrderItem orderItem= new OrderItem();
        orderItem.setProductId(request.getProductId().intValue());
        orderItem.setPacketId(request.getPacketId().intValue());
        orderItem.setInstance(request.getInstance());
        orderItems.add(orderItem);
        // set list in orderItem
        dataRequest.setOrderItem(orderItems);
        requestDto.setData(dataRequest);
        return requestDto;

    }

    public PolicyDtoRequest policyRequestToCustomerCareReq(CustomerCareRequest request){
        PolicyDtoRequest policyDtoRequest= new PolicyDtoRequest();
        DataPolicyRequest dataPolicyRequest= new DataPolicyRequest();
        dataPolicyRequest.setPolicyCode(request.getPolicyCode());
        policyDtoRequest.setDataPolicyRequest(dataPolicyRequest);
        return policyDtoRequest;
    }
    
    public OrderRequestDto orderToDuplicateRequest(OrderDtoResponse originalOrder) {
        OrderRequestDto requestDto = new OrderRequestDto();
        DataOrderRequestDto dataRequest = new DataOrderRequestDto();
        
        // Genera un nuovo codice ordine
        dataRequest.setOrderCode(generateNewOrderCode());
        dataRequest.setProductId(Long.valueOf(originalOrder.getData().getProductId()));
        dataRequest.setPacketId(Long.valueOf(originalOrder.getData().getPacketId()));
        
        // Copia gli orderItems dall'ordine originale
        List<OrderItem> orderItems = new ArrayList<>();
        if (originalOrder.getData().getOrderItem() != null) {
            for (OrderItem originalItem : originalOrder.getData().getOrderItem()) {
                OrderItem newItem = new OrderItem();
                // Copia i campi necessari dall'orderItem originale
                newItem.setProductId(originalItem.getProductId());
                newItem.setPacketId(originalItem.getPacketId());
                newItem.setInstance(originalItem.getInstance());
                newItem.setPrice(originalItem.getPrice());
                newItem.setStartDate(originalItem.getStartDate());
                newItem.setExpirationDate(originalItem.getExpirationDate());
                newItem.setInsuredItem(originalItem.getInsuredItem());
                orderItems.add(newItem);
            }
        }
        
        dataRequest.setOrderItem(orderItems);
        requestDto.setData(dataRequest);
        return requestDto;
    }

    private String generateNewOrderCode() {
        // Implementa la logica per generare un nuovo codice ordine
        return "DUP_ORDER_" + System.currentTimeMillis();
    }
}
