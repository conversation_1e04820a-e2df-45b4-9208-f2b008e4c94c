package it.yolo.mapper;

import it.yolo.claim.dto.CustomerCareDto;
import it.yolo.client.motionCloud.claim.MotionClaimRequest;
import it.yolo.client.motionCloud.dto.InsuredDto;
import it.yolo.client.motionCloud.dto.MotionClaimDto;
import it.yolo.client.motionCloud.dto.ResidentDto;
import it.yolo.client.policy.request.ClaimRequest;
import org.apache.commons.lang3.StringUtils;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

public class ClaimMapper {

    private static String INTERNAL_CLAIM="INTERNAL";


    public ClaimRequest dtoTorequest(CustomerCareDto req){
        ClaimRequest claimRequest= new ClaimRequest();
        claimRequest.setPolicyId(req.getPolicyDto().getData().getId().intValue());
        if(StringUtils.isNotBlank(req.getDate())) {
            if(req.getDate().contains("/")){
                String[] date=req.getDate().replace("/", "-").split("-");
                req.setDate(date[2]+"-"+date[1]+"-"+date[0]);
            }
            LocalDate localDate = LocalDate.parse(req.getDate());
            claimRequest.setDate(localDate);
        }
        claimRequest.setMessage(req.getMessage());
        claimRequest.setData(req.getRequest());
        claimRequest.setClaimNumber(req.getPolicyDto().getData().getPolicyCode());
        claimRequest.setClaimType((req.getAdditionalProperties().get("claim_type") !=null) ?
                req.getAdditionalProperties().get("claim_type").toString() : INTERNAL_CLAIM);
        return  claimRequest;
    }

    public MotionClaimRequest dtoToMotionReq(CustomerCareDto dto){

        MotionClaimRequest request= new MotionClaimRequest();
        MotionClaimDto claimDto= new MotionClaimDto();


        List<Object> policyType=StreamSupport.stream(dto.getProductDto().getData().getConfiguration()
                        .getProperties().get("properties").spliterator(),false).
                filter(prop->prop.get("name").asText().equalsIgnoreCase("policyType")).
                map(prop->prop.get("value")).collect(Collectors.toList());

        String claimTypeCompany=StreamSupport.stream(dto.getProductDto().getData().getConfiguration()
                        .getProperties().get("properties").spliterator(),false).
                filter(prop->prop.get("name").asText().equalsIgnoreCase("claimTypeCompany")).
                map(prop->prop.get("value").asText()).findAny().get();

        claimDto.setClaim_type(claimTypeCompany);
        claimDto.setPolicy_types(policyType);
        InsuredDto insuredDto = new InsuredDto();
        insuredDto.setPolicy_number(dto.getPolicyDto().getData().getPolicyCode());
        insuredDto.setName(dto.getCustomerDto().getData().getName()+ " " +dto.getCustomerDto().getData().getSurname());
        insuredDto.setFiscal_code(dto.getCustomerDto().getData().getTaxCode());
        insuredDto.setEmail(dto.getCustomerDto().getData().getPrimaryMail());
        insuredDto.setPhone(dto.getCustomerDto().getData().getPrimaryPhone());
        insuredDto.setBirth_place(dto.getCustomerDto().getData().getBirth_state());
        insuredDto.setBirth_day(dto.getCustomerDto().getData().getDateOfBirth());
        ResidentDto residentDto= new ResidentDto();
        residentDto.setCity(dto.getCustomerDto().getData().getBirth_state());
        residentDto.setStreet(dto.getCustomerDto().getData().getStreet());
        residentDto.setSquare(dto.getCustomerDto().getData().getStreet());
        residentDto.setProvince(dto.getCustomerDto().getData().getBirth_state());
        residentDto.setPostcode(dto.getCustomerDto().getData().getZipCode());
        insuredDto.setResidentDto(residentDto);
        claimDto.setInsuredDto(insuredDto);
        request.setClaim(claimDto);
        return request;
    }


}
