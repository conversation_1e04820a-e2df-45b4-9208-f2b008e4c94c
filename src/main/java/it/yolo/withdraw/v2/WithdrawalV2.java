package it.yolo.withdraw.v2;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import it.yolo.claim.dto.CustomerCareDto;
import it.yolo.client.comunicationManager.dto.*;
import it.yolo.client.customer.CustomerClient;
import it.yolo.client.customer.dto.CustomerResponseDto;
import it.yolo.client.document.DocumentClient;
import it.yolo.client.documentManager.DocumentManagerClient;
import it.yolo.client.documentManager.DocumentManagerUtility;
import it.yolo.client.order.response.OrderDtoResponse;
import it.yolo.client.order.v3.OrderClient;
import it.yolo.client.policy.PolicyClient;
import it.yolo.client.policy.request.ActionRequest;
import it.yolo.client.policy.response.DataPolicyResponse;
import it.yolo.client.policy.response.PolicyResponse;
import it.yolo.client.product.ProductClientV3;
import it.yolo.client.product.response.ProductResponse;
import it.yolo.client.providerGateway.v2.PgClient;
import it.yolo.exception.CustomerEx;
import it.yolo.exception.MapperEx;
import it.yolo.exception.OrderException;
import it.yolo.exception.PolicyException;
import it.yolo.mapper.ComunicationManagerMapper;
import it.yolo.service.*;
import it.yolo.withdraw.Withdrawal;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.net.URLConnection;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.StreamSupport;

@RequestScoped
public class WithdrawalV2 implements Withdrawal {

    private static final Logger LOGGER = Logger.getLogger(WithdrawalV2.class);

    @Inject
    @RestClient
    PolicyClient policyClient;

    @Inject
    @RestClient
    CustomerClient customerClient;

    @Inject
    @RestClient
    OrderClient orderClient;

    @Inject
    @RestClient
    ProductClientV3 productClient;

    @Inject
    @RestClient
    DocumentClient documentClient;

    @Inject
    @RestClient
    DocumentManagerClient documentManagerClient;

    @Inject
    @RestClient
    PgClient pgClient;

    @Inject
    ServiceComunicationManager serviceComunicationManager;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    ServiceOrder serviceOrder;

    @Inject
    ServiceProduct serviceProduct;

    @Inject
    ServicePolicy servicePolicy;

    @Inject
    ServiceCustomer serviceCustomer;

    @ConfigProperty(name = "tenant.name")
    private String tenant;

    @Override
    public PolicyResponse withdraw(DataPolicyResponse policy, String token, String message, String productCode, JsonNode policyNode, JsonNode
                                   additionalInfos) throws Exception {
        CustomerCareDto dto = new CustomerCareDto();
        dto.setMessage(message);
        PolicyResponse policyResponse = new PolicyResponse();
        policyResponse.setData(policy);
        dto.setPolicyDto(policyResponse);

        // Utilizziamo l'approccio di ServiceCustomerCareExternal per recuperare i dati
        // Recupero dei dati dell'ordine utilizzando fetchOrderData
        OrderDtoResponse order = fetchOrderData(policyResponse, policy.getId());
        dto.setOrderResponse(order);

        // Recupero dei dati del cliente utilizzando fetchCustomerData
        CustomerResponseDto customer = fetchCustomerData();
        dto.setCustomerDto(customer);

        // Recupero dei dati del prodotto utilizzando fetchProductData
        ProductResponse product = fetchProductData(policyResponse, policy.getId());
        dto.setProductDto(product);

        // Convertire in JsonNode solo quando necessario
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        JsonNode orderNode = mapper.valueToTree(order);
        JsonNode customerNode = mapper.valueToTree(customer);
        JsonNode productNode = mapper.valueToTree(product);

        if (policy.getProduct().getConfiguration().getProperties().get("withdraw").get("callPg").asBoolean()) {
            ObjectNode reqToPg = JsonNodeFactory.instance.objectNode();
            ObjectNode reqToPgData = JsonNodeFactory.instance.objectNode();

            // format orderItem startDate
            ObjectNode orderNodeData = (ObjectNode) orderNode.get("data");
            JsonNode orderItem = orderNodeData.get("orderItem").get(0);
            if (orderItem != null && orderItem.isObject()) {
                String startDate = orderItem.get("start_date").asText();
                ((ObjectNode) orderItem).put("start_date", appendMilliseconds(startDate));
            }

            // Format policy startDate
            String policyStartDate = policyNode.get("data").get("startDate").asText();
            ((ObjectNode) policyNode.get("data")).put("startDate", appendMilliseconds(policyStartDate));
            ((ObjectNode) orderNode.get("data").get("orderItem").get(0)).put("start_date", appendMilliseconds(policyStartDate));
            reqToPgData.set("product", productNode);
            reqToPgData.set("order", orderNode);
            reqToPgData.set("policy", policyNode.get("data"));
            reqToPgData.set("customer", customerNode);
            reqToPgData.set("additionalInfos", additionalInfos);
            reqToPg.set("data", reqToPgData);
            reqToPg.put("tenant", tenant);

            pgClient.withdraw(reqToPg);
        }

        // Chiamata alla policyClient.actionForPostVendita
        Response responsePolicy;
        try {
            responsePolicy = policyClient.actionForPostVendita(token, new ActionRequest
                    (product.getData().getConfiguration().getWithdrawType(), policy.getPolicyCode(), null));
        } catch (Exception e){
            throw new PolicyException();
        }

        String withdrawUserMessageValue= StreamSupport.stream(dto.getProductDto().getData().getConfiguration()
                        .getProperties().get("properties").spliterator(),false).
                filter(prop->prop.has("name") && prop.get("name").asText().equalsIgnoreCase("WithdrawUserMessageKey")).
                map(prop->prop.get("value").asText()).findFirst().orElse(null);

        String withdrawCompanyMessageValue=StreamSupport.stream(dto.getProductDto().getData().getConfiguration()
                        .getProperties().get("properties").spliterator(),false).
                filter(prop->prop.has("name") && prop.get("name").asText().equalsIgnoreCase("WithdrawCompanyMessageKey")).
                map(prop->prop.get("value").asText()).findFirst().orElse(null);

        String companyMail=StreamSupport.stream(dto.getProductDto().getData().getConfiguration()
                        .getProperties().get("properties").spliterator(),false).
                filter(prop->prop.has("name") && prop.get("name").asText().equalsIgnoreCase("companyMail")).
                map(prop->prop.get("value").asText()).findFirst().orElse(null);

        if(StringUtils.isNotEmpty(withdrawUserMessageValue)) {
            CommunicationManagerDtoRequest request = this.customerCareToReq(dto, withdrawUserMessageValue,
                    productNode, orderNode, customerNode, policyNode, additionalInfos);
            serviceComunicationManager.sendEmail(request);
        }
        if(StringUtils.isNotEmpty(withdrawCompanyMessageValue)) {
            CommunicationManagerDtoRequest request = this.customerCareToReq(dto, withdrawCompanyMessageValue,
                    productNode, orderNode, customerNode, policyNode, additionalInfos);
            request.getOptions().setToMail(companyMail);
            serviceComunicationManager.sendEmail(request);
        }
        return responsePolicy.readEntity(PolicyResponse.class);
    }

    /**
     * Retrieves policy data from the policy service.
     *
     * @param token The authorization token
     * @param policyId The ID of the policy to retrieve
     * @return The response containing the policy data
     * @throws RuntimeException If an error occurs during policy data retrieval
     */
    private PolicyResponse fetchPolicyData(String token, Long policyId) {
        LOGGER.debugv("Fetching policy data for ID: {0}", policyId);
        try {
            return servicePolicy.findById(policyId);
        } catch (Exception e) {
            LOGGER.errorv(e, "Failed to retrieve policy data for ID: {0}", policyId);
            throw new RuntimeException("Error retrieving policy data for ID " + policyId + ": " + e.getMessage());
        }
    }

    /**
     * Retrieves product data associated with a policy.
     *
     * @param resPolicy The response containing the policy data
     * @param policyId The policy ID (used only for logging)
     * @return The response containing the product data
     * @throws RuntimeException If an error occurs during product data retrieval
     */
    private ProductResponse fetchProductData(PolicyResponse resPolicy, Long policyId) {
        try {
            Long productId = resPolicy.getData().getProduct().getId();
            LOGGER.debugv("Retrieving product data for ID: {0}", productId);
            ProductResponse resProduct = serviceProduct.findById(productId);
            LOGGER.debug("Successfully retrieved product data");
            return resProduct;
        } catch (Exception e) {
            LOGGER.errorv(e, "Failed to retrieve product data for policy {0}", policyId);
            throw new RuntimeException("Error retrieving product data for policy " + policyId + ": " + e.getMessage());
        }
    }

    /**
     * Retrieves order data associated with a policy.
     *
     * @param resPolicy The response containing the policy data
     * @param policyId The policy ID (used only for logging)
     * @return The response containing the order data
     * @throws RuntimeException If an error occurs during order data retrieval
     */
    private OrderDtoResponse fetchOrderData(PolicyResponse resPolicy, Long policyId) {
        try {
            Long orderId = resPolicy.getData().getOrderId();
            LOGGER.debugv("Retrieving order data for ID: {0}", orderId);
            OrderDtoResponse resOrder = serviceOrder.findById(orderId);
            LOGGER.debug("Successfully retrieved order data");
            return resOrder;
        } catch (Exception e) {
            LOGGER.errorv(e, "Failed to retrieve order data for policy {0}", policyId);
            throw new RuntimeException("Error retrieving order data for policy " + policyId + ": " + e.getMessage());
        }
    }

    /**
     * Retrieves customer data.
     *
     * @return The response containing the customer data
     * @throws RuntimeException If an error occurs during customer data retrieval
     */
    private CustomerResponseDto fetchCustomerData() {
        try {
            LOGGER.debug("Retrieving customer data");
            CustomerResponseDto resCustomer = serviceCustomer.findByNdg();
            LOGGER.debug("Successfully retrieved customer data");
            return resCustomer;
        } catch (Exception e) {
            LOGGER.error("Failed to retrieve customer data", e);
            throw new RuntimeException("Error retrieving customer data: " + e.getMessage());
        }
    }

    private String appendMilliseconds(String date) {
        if (date.contains(".")) {
            return date; // Already in the desired format
        }
        return date + ".000";
    }

    private CommunicationManagerDtoRequest customerCareToReq(CustomerCareDto req, String value,
            JsonNode productNode, JsonNode orderNode, JsonNode customerNode, JsonNode policyNode,
                                                             JsonNode additionalInfos){
        CommunicationManagerDtoRequest communicationManagerRequest = new CommunicationManagerDtoRequest();
        Options options= new ComunicationManagerMapper()
                .CustomerCareDtoToOption(req);
        List<Attachment> attachments=new ArrayList<>();
        JsonNode props=req.getProductDto().getData().getConfiguration().getProperties();
        if(props.hasNonNull("withdraw")) {
            if (props.get("withdraw").hasNonNull("cc")){
                ArrayNode ccArray = props.get("withdraw").withArray("cc");
            List<String> cc = new ArrayList<>();
            ccArray.forEach(m -> {
                cc.add(m.asText());
            });
            options.setCc(cc);
        }
            if (props.get("withdraw").hasNonNull("generateTemplate")){
                ObjectNode reqToDocument = JsonNodeFactory.instance.objectNode();
                ObjectNode reqToDocumentData = JsonNodeFactory.instance.objectNode();
                reqToDocumentData.set("product", productNode);
                reqToDocumentData.set("order", orderNode);
                reqToDocumentData.set("policy", policyNode.get("data"));
                reqToDocumentData.set("customer", customerNode);
                reqToDocumentData.set("additionalInfos", additionalInfos);
                reqToDocument.set("data", reqToDocumentData);
                JsonNode resTemplate= documentClient.getWithdrawalTemplate(reqToDocument);
                attachments.add(new Attachment(resTemplate.get("nome_file").asText(),
                        URLConnection.guessContentTypeFromName(resTemplate.get("nome_file").asText()),
                        resTemplate.get("file").asText()));
                if(props.get("withdraw").get("generateTemplate").hasNonNull("attachTemplate")){
                    ObjectNode reqUploadPolicy=JsonNodeFactory.instance.objectNode();
                    ArrayNode documents= JsonNodeFactory.instance.arrayNode();
                    documents.add(DocumentManagerUtility.getDocument(resTemplate.get("nome_file").asText(),
                            resTemplate.get("link").asText(), LocalDateTime.now().toString(), "requests"));
                    reqUploadPolicy.set("data", documents);
                    documentManagerClient.addDocument("Bearer "+jsonWebToken.getRawToken(),
                            req.getPolicyDto().getData().getPolicyCode(), reqUploadPolicy);
                }
            }
        }
        List<TemplatePlaceholder> placeholders=new ComunicationManagerMapper()
                .dtoToPlaceholders(req,value);
        options.setTemplatePlaceholder(placeholders);
        Message message= new Message();
        message.setKey(value);
        communicationManagerRequest.setAttachments(attachments);
        communicationManagerRequest.setOptions(options);
        communicationManagerRequest.setMessage(message);

        return  communicationManagerRequest;
    }
}
