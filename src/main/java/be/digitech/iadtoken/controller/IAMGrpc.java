package be.digitech.iadtoken.controller;

import be.digitech.iadtoken.grpc.*;
import be.digitech.iadtoken.model.*;
import be.digitech.iadtoken.service.CaptchaService;
import be.digitech.iadtoken.service.IAMService;
import com.google.protobuf.ByteString;
import io.quarkus.grpc.GrpcService;
import io.quarkus.logging.Log;
import io.smallrye.common.annotation.Blocking;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import javax.inject.Inject;
import java.util.Optional;
import java.util.function.Function;

@GrpcService
public class IAMGrpc implements IamGrpc {

    @Inject
    IAMService service;

    @Inject
    CaptchaService captchaService;

    @ConfigProperty(name = "apiKey")
    String configApiKey;

    @Blocking
    @Override
    public Uni<TokenResponse> ssoRegistration(Credential request) {
        Log.info("SSO registration start");

        try {
            javax.ws.rs.core.Response responseSSORegistration = service.ssoRegistration(new CredentialsDTO(request));
            Response response = Response.newBuilder().setStatusNumber(javax.ws.rs.core.Response.Status.OK.getStatusCode()).build();

            TokenResponseDTO tokenResponseDTO = responseSSORegistration.readEntity(TokenResponseDTO.class);

            TokenResponse tokenResponse = TokenResponse.newBuilder().setResponse(response).setToken(tokenResponseDTO.getToken()).build();
            Log.info("SSO registration end");
            return Uni.createFrom().item(()->tokenResponse);
        }catch (Exception e){
            Response response = Response.newBuilder()
                    .setStatusNumber(javax.ws.rs.core.Response.Status.BAD_REQUEST.getStatusCode())
                    .setErrorMessage(e.getMessage())
                    .build();
            return Uni.createFrom().item(()->TokenResponse.newBuilder().setResponse(response).build());
        }
    }

    @Blocking
    @Override
    public Uni<TokenResponse> refresh(Token request) {
        Log.info("Refresh start");

        try {
            javax.ws.rs.core.Response response = service.refresh(request.getToken());
            TokenResponseDTO tokenResponseDTO = response.readEntity(TokenResponseDTO.class);
            Response responseOk = Response.newBuilder().setStatusNumber(javax.ws.rs.core.Response.Status.OK.getStatusCode()).build();
            TokenResponse tokenResponse = TokenResponse.newBuilder().setResponse(responseOk).setToken(tokenResponseDTO.getToken()).build();

            Log.info("Refresh end");
            return Uni.createFrom().item(()->tokenResponse);
        }catch (Exception e){
            Response responseUnauthorized = Response.newBuilder()
                    .setStatusNumber(javax.ws.rs.core.Response.Status.UNAUTHORIZED.getStatusCode()).build();
            return Uni.createFrom().item(()-> TokenResponse.newBuilder().setResponse(responseUnauthorized).build());
        }
    }

    @Blocking
    @Override
    public Uni<UserInfoResponse> getUserInfo(Token request) {
        Log.info("Get UserInfo start");
        try {
            javax.ws.rs.core.Response response = service.getUserInfo(request.getToken());

            CredentialsDTO credentialsDTO = response.readEntity(CredentialsDTO.class);
            Credential credential = credentialMapper.apply(credentialsDTO);

            Response responseOk = Response.newBuilder()
                    .setStatusNumber(javax.ws.rs.core.Response.Status.OK.getStatusCode()).build();

            UserInfoResponse userInfoResponse = UserInfoResponse.newBuilder()
                    .setResponse(responseOk)
                    .setCredential(credential)
                    .build();
            Log.info("Get UserInfo end");
            return Uni.createFrom().item(()->userInfoResponse);

        }catch (Exception e){
            Response responseUnauthorized = Response.newBuilder()
                    .setStatusNumber(javax.ws.rs.core.Response.Status.UNAUTHORIZED.getStatusCode()).build();
            return Uni.createFrom().item(()-> UserInfoResponse.newBuilder().setResponse(responseUnauthorized).build());
        }
    }

    @Blocking
    @Override
    public Uni<Response> updateUser(UpdateUserRequest request) {
        Log.info("Update User start");
        try {
            service.updateUser(new CredentialsDTO(request.getCredential()), request.getToken().getToken());

            Log.info("Update User end");
            return Uni.createFrom().item(()->Response.newBuilder()
                    .setStatusNumber(javax.ws.rs.core.Response.Status.OK.getStatusCode()).build());
        }catch (Exception e){
            return Uni.createFrom().item(()-> Response.newBuilder()
                    .setStatusNumber(javax.ws.rs.core.Response.Status.UNAUTHORIZED.getStatusCode()).build());

        }
    }

    @Blocking
    @Override
    public Uni<TokenResponse> retiredRegistration(Credential request) {
        Log.info("Retired Registration start");
        try {
            service.registration(this.setCredentialsDTO(request), "retired-users" );

            Log.info("Retired User end");
            return Uni.createFrom().item(()->TokenResponse.newBuilder()
                    .setResponse(Response.newBuilder().setStatusNumber(201).build()).build());
        }catch (Exception e){
            return Uni.createFrom().item(()->TokenResponse.newBuilder()
                    .setResponse(Response.newBuilder().setStatusNumber(500).setErrorMessage(e.getMessage()).build()).build());

        }
    }

    @Blocking
    @Override
    public Uni<RegistrationResponse> registration(Credential request) {
        Log.infov("Registration start - username = {0}", request.getUsername());

        javax.ws.rs.core.Response response = service.registration(this.setCredentialsDTO(request), null);
        RegistrationResponse registrationResponse = RegistrationResponse.newBuilder().setStatusNumber(response.getStatus()).build();
        Log.info("Registration end");

        return Uni.createFrom().item(() -> registrationResponse);
    }

    @Blocking
    @Override
    public Uni<Users> getAllUsers(Apikey request) {
        Log.info("GetAll Users start");

        if (request.getApikey() !=null && request.getApikey().equals(configApiKey)){
            javax.ws.rs.core.Response listUsersResult = service.getAllUser();
            byte[] byteArray = (byte[]) listUsersResult.getEntity();

            Users users = Users.newBuilder().setUsersBytes(ByteString.copyFrom(byteArray)).build();
            Log.info("GetAll Users end");
            return Uni.createFrom().item(() -> users);
        } else{
            Log.infov("Error response - statusNumber = {0}, invalid apikey", 403);
            Users errorResponse = Users.newBuilder().setResponse(Response.newBuilder().setStatusNumber(403).setErrorMessage("Invalid apikey").build()).build();
            return Uni.createFrom().item(() -> errorResponse);
        }

    }

    @Blocking
    @Override
    public Uni<CaptchaResponseGrpc> verifyCaptcha(TokenCaptcha request) {
        Log.info("VerifyCaptcha() start");
        CaptchaResponse response = captchaService.checkToken(request.getTokenCaptcha());
        Log.infov("Token captcha isSuccess = {0}", response.isSuccess());
        CaptchaResponseGrpc responseGrpc = CaptchaResponseGrpc.newBuilder()
                .setIsSuccess(response.isSuccess())
                .build();
        Log.info("VerifyCaptcha() end");
        return Uni.createFrom().item(()->responseGrpc);
    }

    @Blocking
    @Override
    public Uni<TokenResponse> login(LoginRequest request) {
        Log.infov("Login() start - username = {0}", request.getUsername());

        UserDTO userDTO = new UserDTO(request.getUsername(), request.getPassword());
        javax.ws.rs.core.Response response = service.login(userDTO, Boolean.FALSE);
        if(response.getStatus() == 200){
            TokenResponseDTO tokenResponse = response.readEntity(TokenResponseDTO.class);
            Response responseGrpc = Response.newBuilder().setStatusNumber(response.getStatus()).build();
            Log.info("Login() end");
            return Uni.createFrom().item(() -> TokenResponse
                    .newBuilder()
                    .setToken(tokenResponse.getToken())
                    .setResponse(responseGrpc)
                    .build());
        }else {
            ErrorDTO errorDTO = response.readEntity(ErrorDTO.class);

            Log.infov("Status = {0}, message = {1}", errorDTO.getCode(), errorDTO.getMessage());
            Response responseGrpc = Response.newBuilder()
                    .setStatusNumber(response.getStatus())
                    .setErrorMessage(errorDTO.getMessage())
                    .build();
            Log.info("Login() end");
            return Uni.createFrom().item(() -> TokenResponse
                    .newBuilder()
                    .setResponse(responseGrpc)
                    .build());

        }

    }

    private CredentialsDTO setCredentialsDTO(Credential credential) {
        CredentialsDTO credentialsDTO = new CredentialsDTO();
        credentialsDTO.setName(credential.getName()!= null || !credential.getName().isEmpty() ? credential.getName() : null);
        credentialsDTO.setSurname(credential.getSurname()!= null || !credential.getSurname().isEmpty() ? credential.getSurname() : null);
        credentialsDTO.setEmail(credential.getEmail());
        credentialsDTO.setPhoneNumber(credential.getPhoneNumber()!= null || !credential.getPhoneNumber().isEmpty() ? credential.getPhoneNumber() : null);
        credentialsDTO.setNdgCode(credential.getNdgCode()!= null || !credential.getNdgCode().isEmpty() ? credential.getNdgCode() : null);
        credentialsDTO.setUserSso(false);
        credentialsDTO.setUsername(credential.getUsername());
        credentialsDTO.setBirthDate(credential.getBirthDate()!= null || !credential.getBirthDate().isEmpty() ? credential.getBirthDate() : null);
        credentialsDTO.setPrivacy(credential.getPrivacy());
        credentialsDTO.setCommerce(credential.getCommerce());
        credentialsDTO.setTrace(credential.getTrace());
        credentialsDTO.setPassword(credential.getPassword());
        credentialsDTO.setExternalInfo(credential.getExternalInfo()!= null || !credential.getExternalInfo().isEmpty() ? credential.getExternalInfo() : null);

        return credentialsDTO;
    }

    private final Function<CredentialsDTO, Credential> credentialMapper =

            (in) -> Credential.newBuilder()
                    .setName(Optional.ofNullable(in.getName()).orElse(""))
                    .setSurname(Optional.ofNullable(in.getSurname()).orElse(""))
                    .setEmail(in.getEmail())
                    .setPhoneNumber(Optional.ofNullable(in.getPhoneNumber()).orElse(""))
                    .setNdgCode(Optional.ofNullable(in.getNdgCode()).orElse(""))
                    .setUserSso(Optional.ofNullable(in.getUserSso()).orElse(false))
                    .setUsername(in.getUsername())
                    .setPassword(Optional.ofNullable(in.getPassword()).orElse(""))
                    .setBirthDate(Optional.ofNullable(in.getBirthDate()).orElse(""))
                    .setPrivacy(Optional.ofNullable(in.getPrivacy()).orElse(false))
                    .setCommerce(Optional.ofNullable(in.getCommerce()).orElse(false))
                    .setTrace(Optional.ofNullable(in.getTrace()).orElse(false))
                    .setExternalInfo(Optional.ofNullable(in.getExternalInfo()).orElse(""))
                    .build();



}
