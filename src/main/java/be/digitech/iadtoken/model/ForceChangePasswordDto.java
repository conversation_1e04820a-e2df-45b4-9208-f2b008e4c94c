/*
 * Copyright (c) 2023. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package be.digitech.iadtoken.model;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

public class ForceChangePasswordDto {

    @NotNull
    private String session;
    @NotNull
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[\\^.\\[\\]{}()\\-\"#\\/\\\\,<>':;|_~`+=@$!%*?&])[A-Za-z\\d\\^.\\[\\]{}()\\-\"#\\/\\\\,<>':;|_~`+=@$!%*?&]{8,}$", message = "Password must have minimum eight characters, at least one uppercase letter, one lowercase letter, one number and one special character")
    private String newPassword;
    @NotNull
    private String username;

    public ForceChangePasswordDto() {
    }

    public ForceChangePasswordDto(String session, String newPassword, String username) {
        this.session = session;
        this.newPassword = newPassword;
        this.username = username;
    }

    public String getSession() {
        return session;
    }

    public void setSession(String session) {
        this.session = session;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
}
