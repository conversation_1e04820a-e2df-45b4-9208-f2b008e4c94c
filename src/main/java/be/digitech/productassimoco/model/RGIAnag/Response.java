package be.digitech.productassimoco.model.RGIAnag;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class Response {

    private Subject subject;

    @JsonProperty("chiaveOggetto")
    private String objectKey;

    @JsonProperty("consensi")
    private List<Consents> consents;

    @JsonProperty("datiTerzoSettore")
    private DataThirdSector dataThirdSector;

    @JsonProperty("soggettoModificabile")
    private Boolean editableSubject;

    @JsonProperty("datiAnagraficiModificabili")
    private Boolean registryEditableData;

    @JsonProperty("soggettoPresenteInAnagUnica")
    private Boolean subjectPresentSingleRegistry;

    public Subject getSubject() {
        return subject;
    }

    public void setSubject(Subject subject) {
        this.subject = subject;
    }

    public String getObjectKey() {
        return objectKey;
    }

    public void setObjectKey(String objectKey) {
        this.objectKey = objectKey;
    }

    public List<Consents> getConsents() {
        return consents;
    }

    public void setConsents(List<Consents> consents) {
        this.consents = consents;
    }

    public DataThirdSector getDataThirdSector() {
        return dataThirdSector;
    }

    public void setDataThirdSector(DataThirdSector dataThirdSector) {
        this.dataThirdSector = dataThirdSector;
    }

    public Boolean getEditableSubject() {
        return editableSubject;
    }

    public void setEditableSubject(Boolean editableSubject) {
        this.editableSubject = editableSubject;
    }

    public Boolean getRegistryEditableData() {
        return registryEditableData;
    }

    public void setRegistryEditableData(Boolean registryEditableData) {
        this.registryEditableData = registryEditableData;
    }

    public Boolean getSubjectPresentSingleRegistry() {
        return subjectPresentSingleRegistry;
    }

    public void setSubjectPresentSingleRegistry(Boolean subjectPresentSingleRegistry) {
        this.subjectPresentSingleRegistry = subjectPresentSingleRegistry;
    }

    @Override
    public String toString() {
        return "Response{" +
                "subject=" + subject +
                ", objectKey='" + objectKey + '\'' +
                ", consents=" + consents +
                ", dataThirdSector=" + dataThirdSector +
                ", editableSubject=" + editableSubject +
                ", registryEditableData=" + registryEditableData +
                ", subjectPresentSingleRegistry=" + subjectPresentSingleRegistry +
                '}';
    }
}