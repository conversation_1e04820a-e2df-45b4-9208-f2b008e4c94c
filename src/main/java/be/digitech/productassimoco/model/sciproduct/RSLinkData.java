package be.digitech.productassimoco.model.sciproduct;

import be.digitech.productassimoco.utils.TypeEnumRecord;

public class RSLinkData {

    private String linkSubjectNominative;

    private TypeEnumRecord linkType;

    private String linkedPartyUniqueKey;

    private Long linkedPartyObjectId;

    private Long idSubjectLinkType;

    public String getLinkSubjectNominative() {
        return linkSubjectNominative;
    }

    public void setLinkSubjectNominative(String linkSubjectNominative) {
        this.linkSubjectNominative = linkSubjectNominative;
    }

    public TypeEnumRecord getLinkType() {
        return linkType;
    }

    public void setLinkType(TypeEnumRecord linkType) {
        this.linkType = linkType;
    }

    public String getLinkedPartyUniqueKey() {
        return linkedPartyUniqueKey;
    }

    public void setLinkedPartyUniqueKey(String linkedPartyUniqueKey) {
        this.linkedPartyUniqueKey = linkedPartyUniqueKey;
    }

    public Long getLinkedPartyObjectId() {
        return linkedPartyObjectId;
    }

    public void setLinkedPartyObjectId(Long linkedPartyObjectId) {
        this.linkedPartyObjectId = linkedPartyObjectId;
    }

    public Long getIdSubjectLinkType() {
        return idSubjectLinkType;
    }

    public void setIdSubjectLinkType(Long idSubjectLinkType) {
        this.idSubjectLinkType = idSubjectLinkType;
    }

    @Override
    public String toString() {
        return "RSLinkData{" +
                "linkSubjectNominative='" + linkSubjectNominative + '\'' +
                ", linkType=" + linkType +
                ", linkedPartyUniqueKey='" + linkedPartyUniqueKey + '\'' +
                ", linkedPartyObjectId=" + linkedPartyObjectId +
                ", idSubjectLinkType=" + idSubjectLinkType +
                '}';
    }
}