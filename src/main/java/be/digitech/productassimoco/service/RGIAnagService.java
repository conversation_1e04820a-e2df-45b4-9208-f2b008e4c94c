package be.digitech.productassimoco.service;

import be.digitech.productassimoco.client.RGIAnagClient;
import be.digitech.productassimoco.client.RaiffeisenClientGrpc;
import be.digitech.productassimoco.client.UtilityClientGrpc;
import be.digitech.productassimoco.model.RGIAnag.*;
import be.digitech.productassimoco.model.RGIAnagResponseDTO;
import be.digitech.productassimoco.model.login.ResponseLogin;
import be.digitech.productassimoco.model.sciproduct.*;
import be.digitech.productassimoco.utils.Decompress;
import be.digitech.productassimoco.utils.TypeEnumRecord;
import be.digitech.raiffeisenservice.grpc.AnagResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.resteasy.reactive.ClientWebApplicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.ClientErrorException;
import javax.ws.rs.ProcessingException;
import javax.ws.rs.client.ClientResponseContext;
import javax.ws.rs.core.Response;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@RequestScoped
public class RGIAnagService {

    public final static Logger log = LoggerFactory.getLogger(RGIAnagService.class);

    @Inject
    @RestClient
    RGIAnagClient rgiAnagClient;

    @Inject
    LoginService loginService;

    @Inject
    IterativeAgenciesService iterativeAgenciesService;

    @ConfigProperty(name = "idNode")
    private String idNode;

    @ConfigProperty(name = "archiveCode")
    private String archiveCode;

    @Inject
    RaiffeisenClientGrpc raiffeisenClientGrpc;

    @Inject
    UtilityClientGrpc utilityClientGrpc;


    public Response findByFC(JsonWebToken jwt) {

        RGIAnagResponseDTO responseAnag = null;

        ResponseLogin login = null;

        String idSp = null;

        String fiscalCode = jwt.getClaim("cf").toString();

        try {

            log.info("RGIAnagService.findByFC() started...");

            AnagResponse responseRaiffeisen = raiffeisenClientGrpc
                    .getUser(jwt.getClaim("cf").toString(), jwt.getClaim("abi").toString()).await().indefinitely();

            String token = getToken(jwt);
            Response uniresponse = null;
            try {
                Uni<Response> response = rgiAnagClient.searchByFiscalCode("", archiveCode, idNode,
                        jwt.getClaim("cf").toString(), token);
                uniresponse = response.await().indefinitely();
            } catch (Exception e) {
                loginService.invalidateCache(jwt.getClaim("cf").toString());
                token = getToken(jwt);
                uniresponse = rgiAnagClient
                        .searchByFiscalCode("", archiveCode, idNode, jwt.getClaim("cf").toString(), token).await()
                        .indefinitely();
            }
            byte[] responseFC = uniresponse.readEntity(byte[].class);
            ObjectMapper object = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
                    false);

            responseAnag = object.readValue(Decompress.decompress(responseFC), RGIAnagResponseDTO.class);

            ResponseAnagDTO responseAnagDTO = new ResponseAnagDTO();
            List<ExternalCode> listExternalCode = new ArrayList<>();
            if (responseAnag != null && responseAnag.getData() != null && responseAnag.getData().getSubject() != null) {
                listExternalCode.add(new ExternalCode("existAssimoco", "true"));
            }

            if (responseAnag != null && responseAnag.getData() != null && responseAnag.getData().getSubject() != null
                    && responseAnag.getData().getSubject().getObjectId() != null) {
                listExternalCode.add(
                        new ExternalCode("partyCode", responseAnag.getData().getSubject().getObjectId().toString()));
                listExternalCode.add(new ExternalCode("specializedPartyCode",
                        responseAnag.getData().getSubject().getIdLatestPhotos().toString()));
                // responseAnagDTO.setId(responseAnag.getData().getSubject().getObjectId());
                responseAnagDTO.setCustomerCode(
                        responseAnag != null ? responseAnag.getData().getSubject().getObjectId().toString() : null);
            }
            if (responseAnag != null && responseAnag.getData() != null && responseAnag.getData().getSubject() != null
                    && responseAnag.getData().getSubject().getIdLatestPhotos() != null) {
                listExternalCode.add(
                        new ExternalCode("idLatestPhotos", responseAnag.getData().getSubject().getIdLatestPhotos().toString()));
            }
            String codeAgency = jwt.getClaim("codeAgency").toString();
            try{
                idSp = iterativeAgenciesService.getIterativeAgencies(codeAgency, jwt);
            } catch(Exception e){
                log.error("Error getInterativeAgencies: {}", e.getMessage());
            }
            // responseAnagDTO.setUsername(responseAnag.getData().getSubject().get);
            listExternalCode.add(new ExternalCode("idSp", idSp));
            listExternalCode.add(new ExternalCode("codeAgency", codeAgency));
            if (responseRaiffeisen.getSocio() != null) {
                listExternalCode.add(new ExternalCode("socio", responseRaiffeisen.getSocio()));
            }
            if (responseRaiffeisen.getContoCorrenteInfosType() != null) {
                if (responseRaiffeisen.getContoCorrenteInfosType().getIban() != null)
                listExternalCode.add(new ExternalCode("iban", responseRaiffeisen.getContoCorrenteInfosType().getIban()));
            }
            if (responseRaiffeisen.getGruppoCliente() != null) {
                listExternalCode.add(new ExternalCode("gruppoCliente", responseRaiffeisen.getGruppoCliente()));
            }

            responseAnagDTO.setExternalCode(listExternalCode);
            responseAnagDTO.setName(responseRaiffeisen.getName());
            responseAnagDTO.setSurname(responseRaiffeisen.getSurname());
            responseAnagDTO.setDateOfBirth(responseRaiffeisen.getDateOfBirth());
            responseAnagDTO.setTaxCode(responseRaiffeisen.getFiscalCode());
            responseAnagDTO.setStreet(responseRaiffeisen.getResidenceAddress());
            responseAnagDTO.setStreetNumber(responseRaiffeisen.getResidenceNumber());
            responseAnagDTO.setGender(responseRaiffeisen.getGender());
            responseAnagDTO.setCity(responseRaiffeisen.getResidenceCity());
            responseAnagDTO.setCountry(responseRaiffeisen.getResidenceCountry());
            responseAnagDTO.setZipCode(responseRaiffeisen.getResidenceZipCode());
            responseAnagDTO.setPrimaryMail(responseRaiffeisen.getEmail());
            // responseAnagDTO.setSecondary_mail(responseAnag.getData().getSubject().getEmails());
            responseAnagDTO.setPrimaryPhone(responseRaiffeisen.getPhoneNumber());
            if (responseAnagDTO.getPrimaryPhone() == null || responseAnagDTO.getPrimaryPhone().isEmpty()) {
                responseAnagDTO.setPrimaryPhone(responseAnag != null ? responseAnag.getData().getSubject().getMobilePhone().get(0).getNumber() : null);
            }
            responseAnagDTO.setBirthCity(responseRaiffeisen.getBirthPlace());
            responseAnagDTO.setProvince(responseRaiffeisen.getResidenceProvince());
            responseAnagDTO.setBirthProvince(responseRaiffeisen.getBirthProvince());
            responseAnagDTO.setLegalForm(
                    responseRaiffeisen.getCodFormaGiuridica().equalsIgnoreCase("01") ? "PRIVATO" : "SOCIETA'");
            responseAnagDTO.setLanguage(responseRaiffeisen.getCodeLanguage().equalsIgnoreCase("1") ? "DE" : "IT");
            // responseAnagDTO.setSecondary_phone(responseAnag.getData().getSubject().getMobilePhone());


            return Response.ok(responseAnagDTO).build();
        } catch (Exception e) {
            log.info("Error Assimoco - findByFC() - fiscalCode {}: {}", fiscalCode, e.getMessage());
            log.error("Error search by fiscalCode" + e.getMessage());

            throw new RuntimeException();
        }
    }

    public Response findByEticaFc(JsonWebToken jwt) {

        String fiscalCode = jwt.getClaim("cf").toString();
        String codeAgency = jwt.getClaim("codeAgency").toString();
        String token = getToken(jwt);
        // Inizializziamo responseAnagDTO prima del blocco try-catch
        ResponseAnagDTO responseAnagDTO = new ResponseAnagDTO();
        List<ExternalCode> externalCodes = new ArrayList<>();

        try {
            log.info("RGIAnagService.findByFC() started for fiscalCode: {}", fiscalCode);
            Response uniResponse = getResponseFromAnagService(jwt, fiscalCode, token);
            log.info("RGIAnagService.findByFC() - getResponseFromAnagService()");
            RGIAnagResponseDTO responseAnag = deserializeAnagResponse(uniResponse);
            responseAnagDTO = buildResponseDTO(responseAnag, fiscalCode, codeAgency, jwt);

            log.info("Get Customer: {}", responseAnagDTO);
            return Response.ok(responseAnagDTO).build();

        } catch (Exception e) {
            log.error("Error Assimoco - findByEticaFc() - fiscalCode {}: {}", fiscalCode, e.getMessage());

            // Anche se l'anagrafica non è stata trovata, recuperiamo comunque l'idSp e il codeAgency
            externalCodes.add(new ExternalCode("idSp", retrieveIterativeAgencies(codeAgency, jwt)));
            externalCodes.add(new ExternalCode("codeAgency", codeAgency));
            responseAnagDTO.setExternalCode(externalCodes);
            responseAnagDTO.setTaxCode(getSafeString(fiscalCode));

            // Restituiamo l'oggetto responseAnagDTO anche in caso di errore
            log.info("Returning partial data with idSp for fiscalCode: {}", fiscalCode);
            return Response.status(Response.Status.PARTIAL_CONTENT).entity(responseAnagDTO).build();
        }
    }

    public Response anagPut(ResponseAnagDTO input, JsonWebToken jwt) throws JsonProcessingException {

        // AnagResponse responseRaiffeisen =
        // raiffeisenClientGrpc.getUser(input.getTaxCode(),
        // jwt.getClaim("abi").toString()).await().indefinitely();

        String token = getToken(jwt);

        String fiscalCode = jwt.getClaim("cf").toString();

        AnagSubjectResponse subject = getSubject(input, jwt);
        subject.setExtensionSubject(this.buildDefaultExtensionSubject());

        ConsensoPrivacy marketingConsens = getConsensoPrivacy(input);
        ConsensoPrivacy getConsensoProfil = getConsensoPrivacy(input);
        ConsensoPrivacy getConsensoDocument = getConsensoPrivacy(input);

        AssociazioniPFModel thirdSectorPhysical = new AssociazioniPFModel();
        thirdSectorPhysical.setCodiceFiscalePF(input.getTaxCode());
        thirdSectorPhysical.setRelazioniPF(null);
        thirdSectorPhysical.setNessunRapportoAttivo(true);

        AnagSubjectInput anagSubjectInput = new AnagSubjectInput();
        anagSubjectInput.setSubject(subject);
        anagSubjectInput.setConsensuses(List.of(marketingConsens, getConsensoProfil, getConsensoDocument));
        anagSubjectInput.setThirdSectorPhysical(thirdSectorPhysical);
        anagSubjectInput.setPresenteInAnagUnica(true);
        anagSubjectInput.setRoleForConsensuses("1");
        anagSubjectInput.setUpdateConsensuses(false);

        RGIAnagResponseDTO responseAnag = null;
        Response uniresponse = null;

        AnagSubjectResponse subject2 = getSubject2(input, jwt);
        subject2.setExtensionSubject(this.buildDefaultExtensionSubject());

        ConsensoPrivacy marketingConsens2 = getConsensoPrivacy(input);
        ConsensoPrivacy getConsensoProfil2 = getConsensoPrivacy(input);
        ConsensoPrivacy getConsensoDocument2 = getConsensoPrivacy(input);

        AssociazioniPFModel thirdSectorPhysical2 = new AssociazioniPFModel();
        thirdSectorPhysical2.setCodiceFiscalePF(input.getTaxCode());
        thirdSectorPhysical2.setRelazioniPF(null);
        thirdSectorPhysical2.setNessunRapportoAttivo(true);

        AnagSubjectInput anagSubjectInput2 = new AnagSubjectInput();
        anagSubjectInput2.setSubject(subject2);
        anagSubjectInput2.setConsensuses(List.of(marketingConsens2, getConsensoProfil2, getConsensoDocument2));
        anagSubjectInput2.setThirdSectorPhysical(thirdSectorPhysical2);
        anagSubjectInput2.setPresenteInAnagUnica(true);
        anagSubjectInput2.setRoleForConsensuses("1");
        anagSubjectInput2.setUpdateConsensuses(false);

        RGIAnagResponseDTO responseAnag2 = null;

        try {
            try {
                Uni<Response> response = rgiAnagClient.searchByFiscalCode("", archiveCode, idNode,
                        jwt.getClaim("cf").toString(), token);
                uniresponse = response.await().indefinitely();

                byte[] responseFC = uniresponse.readEntity(byte[].class);
                ObjectMapper object = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
                        false);

                responseAnag = object.readValue(Decompress.decompress(responseFC), RGIAnagResponseDTO.class);

            } catch (Exception e) {
                loginService.invalidateCache(jwt.getClaim("cf").toString());
                token = getToken(jwt);
                uniresponse = rgiAnagClient
                        .searchByFiscalCode("", archiveCode, idNode, jwt.getClaim("cf").toString(), token).await()
                        .indefinitely();

                byte[] responseFC = uniresponse.readEntity(byte[].class);
                ObjectMapper object = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
                        false);

                responseAnag = object.readValue(Decompress.decompress(responseFC), RGIAnagResponseDTO.class);
            }
            if (responseAnag.getData() != null) {

                if (responseAnag.getData().getSubject().getPrivacyConsensusType() != null) {
                    subject.setPrivacyConsensusType(responseAnag != null ? responseAnag.getData().getSubject().getPrivacyConsensusType() : null);
                }
                if (responseAnag.getData().getSubject().getMainContact() != null) {
                    subject.setMainContact(responseAnag != null ? responseAnag.getData().getSubject().getMainContact() : null);
                }
                if (responseAnag.getData().getSubject().getPhoneFax() != null) {
                    subject.setPhoneFax(responseAnag != null ? responseAnag.getData().getSubject().getPhoneFax() : null);
                }
                if (responseAnag.getData().getSubject().getMobilePhone() != null) {
                    List<RSAnagPhone> mobilePhones = responseAnag != null ? responseAnag.getData().getSubject().getMobilePhone() : null;
                    RSAnagPhone rsAnagPhone = new RSAnagPhone();
                    rsAnagPhone.setObjectId("0");
                    rsAnagPhone.setNumber(input.getPrimaryPhone());

                    RSAnagPhone rsAnagPhone1 = new RSAnagPhone();
                    rsAnagPhone1.setObjectId("1");
                    rsAnagPhone1.setNumber(mobilePhones.get(1).getNumber());

                    RSAnagPhone rsAnagPhone2 = new RSAnagPhone();
                    rsAnagPhone2.setObjectId("2");
                    rsAnagPhone2.setNumber(mobilePhones.get(2).getNumber());
                    subject.setMobilePhone(List.of(rsAnagPhone, rsAnagPhone1, rsAnagPhone2));
                }
                if (responseAnag.getData().getSubject().getGroupAE() != null) {
                    subject.setGruppoAE(responseAnag != null ? responseAnag.getData().getSubject().getGroupAE() : null);
                }
                if (responseAnag.getData().getSubject().getDenomination() != null) {
                    subject.setDenomination(responseAnag != null ? responseAnag.getData().getSubject().getDenomination() : null);
                }
                if (responseAnag.getData().getSubject().getLinks() != null) {
                    subject.setLinks(responseAnag != null ? responseAnag.getData().getSubject().getLinks() : null);
                }
                if (responseAnag.getData().getSubject().getProfessionType() != null) {
                    subject.setProfessionType(responseAnag != null ? responseAnag.getData().getSubject().getProfessionType() : null);
                }
                if (responseAnag.getData().getSubject().getVat() != null) {
                    subject.setVat(responseAnag != null ? responseAnag.getData().getSubject().getVat() : null);
                }
                if (responseAnag.getData().getSubject().getCodAteco() != null) {
                    subject.setCodAteco(responseAnag != null ? responseAnag.getData().getSubject().getCodAteco() : null);
                }
                if (responseAnag.getData().getSubject().getMaritalStatus() != null) {
                    subject.setMaritalStatus(responseAnag != null ? responseAnag.getData().getSubject().getMaritalStatus() : null);
                }
                if (responseAnag.getData().getSubject().getPayments() != null) {
                    subject.setPayments(responseAnag != null ? responseAnag.getData().getSubject().getPayments() : null);
                }
                if (responseAnag.getData().getSubject().getCorporateSector() != null) {
                    subject.setCorporateSector(responseAnag != null ? responseAnag.getData().getSubject().getCorporateSector() : null);
                }
                if (responseAnag.getData().getSubject().getCorporateForm() != null) {
                    subject.setCorporateForm(responseAnag != null ? responseAnag.getData().getSubject().getCorporateForm() : null);
                }
                if (responseAnag.getData().getSubject().getPartyNumber() != null) {
                    subject.setPartyNumber(responseAnag != null ? responseAnag.getData().getSubject().getPartyNumber() : null);
                }
                if (responseAnag.getData().getSubject().getPrivacyConsensusDate() != null) {
                    subject.setPrivacyConsensusDate(responseAnag != null ? responseAnag.getData().getSubject().getPrivacyConsensusDate() : null);
                }
                if (responseAnag.getData().getSubject().getProfessionDetail() != null) {
                    subject.setProfessionDetail(responseAnag != null ? responseAnag.getData().getSubject().getProfessionDetail() : null);
                }
                if (responseAnag.getData().getSubject().getOtherAddress() != null) {
                    subject.setOtherAddress(responseAnag != null ? responseAnag.getData().getSubject().getOtherAddress() : null);
                }
                if (responseAnag.getData().getSubject().getCompany() != null) {
                    subject.setCompany(responseAnag != null ? responseAnag.getData().getSubject().getCompany() : null);
                }
                if (responseAnag.getData().getSubject().getIddQuestionnaires() != null) {
                    subject.setIddQuestionnaires(responseAnag != null ? responseAnag.getData().getSubject().getIddQuestionnaires() : null);
                }
                if (responseAnag.getData().getSubject().getOutcome() != null) {
                    subject.setOutcome(responseAnag != null ? responseAnag.getData().getSubject().getOutcome() : null);
                }
                if (responseAnag.getData().getSubject().getCitizenship() != null) {
                    subject.setCitizenship(responseAnag != null ? responseAnag.getData().getSubject().getCitizenship() : null);
                }
                if (responseAnag.getData().getSubject().getGroups() != null) {
                    subject.setGroups(responseAnag != null ? responseAnag.getData().getSubject().getGroups() : null);
                }
                if (responseAnag.getData().getSubject().getClientNumber() != null) {
                    subject.setClientNumber(responseAnag != null ? responseAnag.getData().getSubject().getClientNumber() : null);
                }
                if (responseAnag.getData().getSubject().getEducationalQualification() != null) {
                    subject.setEducationalQualification(responseAnag != null ? responseAnag.getData().getSubject().getEducationalQualification() : null);
                }
                if (responseAnag.getData().getSubject().getBondingCode() != null) {
                    subject.setBondingCode(responseAnag != null ? responseAnag.getData().getSubject().getBondingCode() : null);
                }
                if (responseAnag.getData().getSubject().getLandLineNumber() != null) {
                    RSAnagPhone landLineNumber = responseAnag != null ? responseAnag.getData().getSubject().getLandLineNumber() : null;
                    RSAnagPhone landLine = new RSAnagPhone();
                    landLine.setObjectId("0");
                    landLine.setNumber(landLineNumber.getNumber());
                    subject.setLandlineNumber(landLine);
                }
                if (responseAnag.getData().getSubject().getExtensionSubject() != null) {
                    subject.setExtensionSubject(responseAnag != null ? responseAnag.getData().getSubject().getExtensionSubject() : null);
                }
                if (responseAnag.getData().getSubject().getSubgroupAE() != null) {
                    subject.setSottogruppoAE(responseAnag != null ? responseAnag.getData().getSubject().getSubgroupAE() : null);
                }
                if (responseAnag.getData().getSubject().getDocument() != null) {
                    subject.setDocument(responseAnag != null ? responseAnag.getData().getSubject().getDocument() : null);
                }
                if (responseAnag.getData().getSubject().getSubgroupAE() == null) {
                    subject.setSottogruppoAE(this.buildSottoGruppoAEByExternalCodes(input));
                }
            }
        } catch (Exception ex) {
            log.error("Error Get Customer" + ex.getMessage());
        }
            try {
                log.info("anagSubjectInputUpdate... {}", anagSubjectInput);
                log.info("SubjectSubjectInputInsert: {}", anagSubjectInput2);
                Optional<ExternalCode> partyCode = input.getExternalCode().stream()
                        .filter(externalCode -> externalCode.getName().equalsIgnoreCase("partyCode")).findFirst();

                Optional<ExternalCode> idLatestPhotos = input.getExternalCode().stream()
                        .filter(externalCode -> externalCode.getName().equalsIgnoreCase("idLatestPhotos")).findFirst();

                if (partyCode.isPresent() || idLatestPhotos.isPresent()) {
                    try {
                        log.info("RGIAnagService.anagPut() started...");
                        subject.setObjectId(Integer.valueOf(partyCode.get().getValue()));
                        anagSubjectInput.setSubject(subject);
                        log.info("anagSubjectInput: {}", anagSubjectInput);
                        byte[] responseProduct = rgiAnagClient.anagPut(partyCode.get().getValue(), idNode, anagSubjectInput, token);
                        String decompressedProduct = Decompress.decompress(responseProduct);
                        log.info("Decompressed anagPut response: {}", decompressedProduct);
                        InputPutSubject inputPutSubject = new InputPutSubject(subject);
                        log.info("inputPutSubject: {}", inputPutSubject);
                        byte[] responseSubject = rgiAnagClient.subjectPut(partyCode.get().getValue(), idNode, inputPutSubject, token, idNode);
                        String decompressedSubject = Decompress.decompress(responseSubject);
                        log.info("Decompressed subjectPut response: {}", decompressedSubject);
                    } catch (ClientWebApplicationException ex) {
                        logDecompressedClientError(ex, "anagPut");
                        throw ex;
                    } catch (Exception e) {
                        log.error("Error update anag: {}", e.getMessage());
                        throw new RuntimeException();
                    }
                } else {
                    try {
                        log.info("RGIAnagService.registration started...");
                        byte[] responseProduct = rgiAnagClient.registrationUpdate(idNode, anagSubjectInput2, token);
                        String decompressedProduct = Decompress.decompress(responseProduct);
                        log.info("Decompressed registrationUpdate response: {}", decompressedProduct);
                        InputPutSubject inputPutSubject2 = new InputPutSubject(subject2);
                        log.info("InputPutSubject: {}", inputPutSubject2);
                        byte[] responseSubject = rgiAnagClient.subjectPost(idNode, inputPutSubject2, token, idNode);
                        String decompressedSubject = Decompress.decompress(responseSubject);
                        log.info("Decompressed subjectPost response: {}", decompressedSubject);
                    } catch (ClientWebApplicationException ex) {
                        logDecompressedClientError(ex, "registrationUpdate");
                        throw ex;
                    } catch (Exception e) {
                        log.error("Errore durante la registrazione: {}", e.getMessage());
                        log.info("Proseguendo con subjectPost...");
                    }

                    try {
                        InputPutSubject inputPutSubject2 = new InputPutSubject(subject2);
                        log.info("InputPutSubject: {}", inputPutSubject2);
                        byte[] responseSubject = rgiAnagClient.subjectPost(idNode, inputPutSubject2, token, idNode);
                        String decompressedSubject = Decompress.decompress(responseSubject);
                        log.info("Decompressed subjectPost response: {}", decompressedSubject);
                    } catch (ClientWebApplicationException ex) {
                        logDecompressedClientError(ex, "subjectPost");
                        throw ex;
                    } catch (Exception e2) {
                        log.error("Errore durante subjectPost: {}", e2.getMessage());
                        log.info("Proseguendo con anagPut...");
                    }

                    try {
                        anagSubjectInput.setSubject(subject);
                        anagSubjectInput.getSubject().setObjectId(-1);
                        String objectId = anagSubjectInput.getSubject().getObjectId().toString();
                        byte[] responseProduct = rgiAnagClient.anagPut(objectId, idNode, anagSubjectInput, token);
                        String decompressedProduct = Decompress.decompress(responseProduct);
                        log.info("Decompressed anagPut response: {}", decompressedProduct);
                        InputPutSubject inputPutSubject = new InputPutSubject(subject);
                        log.info("InputPutSubject: {}", inputPutSubject);
                        byte[] responseSubject = rgiAnagClient.subjectPut(objectId, idNode, inputPutSubject, token, idNode);
                        String decompressedSubject = Decompress.decompress(responseSubject);
                        log.info("Decompressed subjectPut response: {}", decompressedSubject);
                    } catch (ClientWebApplicationException ex) {
                        logDecompressedClientError(ex, "anagPut");
                        throw ex;
                    } catch (Exception e3) {
                        log.error("Errore durante anagPut: {}", e3.getMessage());
                        log.info("Proseguendo con subjectPut...");
                        InputPutSubject inputPutSubject = new InputPutSubject(subject);
                        inputPutSubject.getSubject().setObjectId(-1);
                        String objectId = inputPutSubject.getSubject().getObjectId().toString();
                        log.info("InputPutSubject: {}", inputPutSubject);
                        byte[] responseSubject = rgiAnagClient.subjectPut(objectId, idNode, inputPutSubject, token, idNode);
                        String decompressedSubject = Decompress.decompress(responseSubject);
                        log.info("Decompressed subjectPut response: {}", decompressedSubject);
                    }
                }
            } catch (WebApplicationException e) {
                Response errorResponse = e.getResponse();
                if (errorResponse != null && errorResponse.hasEntity()) {
                    try {
                        byte[] errorBytes = errorResponse.readEntity(byte[].class);
                        String decompressedError = Decompress.decompress(errorBytes);
                        log.error("Errore response decompressa da RGIAnagClient: {}", decompressedError);
                    } catch (Exception ex) {
                        log.error("Errore durante la decompressione della response di errore: {}", ex.getMessage());
                    }
                } else {
                    log.error("WebApplicationException senza entity: {}", e.getMessage());
                }
                throw e;
            } catch (Exception e) {
                log.info("Error Assimoco - anagPut() - fiscalCode {}: {}", fiscalCode, e.getMessage());
                throw new RuntimeException();
            }
            return Response.ok().build();
    }

    private RSAnagExtensionSubject buildDefaultExtensionSubject() {
        RSAnagExtensionSubject extensionSubject = new RSAnagExtensionSubject();
        List<TypeProperty> listProperty = new ArrayList<>();
        TypeProperty typeGASProperty = new TypeProperty();
        typeGASProperty.setKey("GRUPPO");
        typeGASProperty.setValue("GAS");
        listProperty.add(typeGASProperty);

        TypeProperty typeisNewProperty = new TypeProperty();
        typeisNewProperty.setKey("isNew");
        typeisNewProperty.setValue("true");
        listProperty.add(typeisNewProperty);

        extensionSubject.setProperties(listProperty);
        return extensionSubject;
    }

    private AnagSubjectResponse getSubject(ResponseAnagDTO input, JsonWebToken jwt) {
        TypeEnumRecord enumLanguage;
        if (input.getLanguage().equalsIgnoreCase("DE"))
            enumLanguage = new TypeEnumRecord("5", "TEDESCO");
        else {
            enumLanguage = new TypeEnumRecord("1", "ITALIANO");
        }

        TypeEnumRecord enumFormaGiuridica;
        if (input.getLegalForm().equalsIgnoreCase("PRIVATO"))
            enumFormaGiuridica = new TypeEnumRecord("1", "Privato");
        else {
            enumFormaGiuridica = new TypeEnumRecord("8", null);
        }

        TypeEnumRecord enumBirthPlace;
        if (!input.getBirthProvince().equalsIgnoreCase("EE"))
            enumBirthPlace = new TypeEnumRecord("IT", null);
        else {
            enumBirthPlace = new TypeEnumRecord(input.getBirthCity().toUpperCase(), null);
        }

        TypeEnumRecord mainContact;
        if (input.getGender().equalsIgnoreCase("F"))
            mainContact = new TypeEnumRecord("2", "");
        else {
            mainContact = new TypeEnumRecord("1", "");
        }

        TypeEnumRecord personType = new TypeEnumRecord("1", "Fisico");

        RSAnagAddress residence = new RSAnagAddress();
        // residence.setCountry();
        TypeEnumRecord country = new TypeEnumRecord(input.getCountry(), null);
        residence.setCountry(country);
        residence.setCity(input.getCity().toUpperCase());
        residence.setNormalized(false);
        residence.setLatitude(Double.valueOf("0"));
        residence.setLongitude(Double.valueOf("0"));
        residence.setPlaceAddress(input.getStreet());
        residence.setNumber(input.getStreetNumber());
        residence.setCap(input.getZipCode());
        residence.setCountryCode(input.getCountry());
        residence.setAdminLevel3(input.getCity().toUpperCase());
        String codeCity = utilityClientGrpc.getCodeCity(input.getCity(), input.getProvince()).await().indefinitely()
                .getCode();
        residence.setAdminLevel3Short(codeCity.toUpperCase());
        // residence.setAdminLevel2(input.getProvince().toUpperCase());
        // String codeProvince =
        // utilityClientGrpc.getCodeProvince(input.getProvince()).await().indefinitely().getCode();
        residence.setAdminLevel2Short(input.getProvince().toUpperCase());
        // residence.setAdminLevel12Short("");

        RSPtfAllldentification node = new RSPtfAllldentification();
        node.setIdentification(Long.valueOf(idNode));

        RSAnagPhone rsAnagPhone = new RSAnagPhone();
        rsAnagPhone.setObjectId("0");
        rsAnagPhone.setNumber(input.getPrimaryPhone());

        DateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");
        String dateString;
        try {
            Date date = formatter.parse(input.getDateOfBirth());
            SimpleDateFormat newFormat = new SimpleDateFormat("yyyy-MM-dd");
            dateString = newFormat.format(date);
        } catch (Exception e) {
            log.error("Error to format date");
            throw new RuntimeException();
        }

        Key key1 = new Key("Codice Fiscale", 0, null, input.getTaxCode());
        Key key2 = new Key("Partita IVA", 0, null, null);
        Key key3 = new Key("Pseudocode", 0, null, null);
        PartyKey partykey1 = new PartyKey(null, key1, input.getCountry(), key2, null, key3, false);

        RSAnagAddress birthAndress = new RSAnagAddress();
        if (input.getBirthProvince().equalsIgnoreCase("EE")) {
            birthAndress.setAdminLevel2("EE");
            birthAndress.setAdminLevel2Short("EE");
            birthAndress.setCountry(new TypeEnumRecord(input.getBirthCity().toUpperCase(), null));
            birthAndress.setCountryCode(input.getBirthCity().toUpperCase());
            birthAndress.setDetailCode(0);
            birthAndress.setFormatAddress(input.getBirthCity().toUpperCase());
            birthAndress.setNormalized(false);
            birthAndress.setLongitude(Double.valueOf("0"));
            birthAndress.setLatitude(Double.valueOf("0"));
        } else {
            birthAndress.setCountryCode("IT");
            birthAndress.setAdminLevel3Short(input.getTaxCode().substring(11, 15));
            birthAndress.setAdminLevel3(input.getBirthCity().toUpperCase());
            birthAndress.setAdminLevel2(input.getBirthProvince().toUpperCase());
            birthAndress.setAdminLevel2Short(input.getBirthProvince().toUpperCase());
        }


        AnagSubjectResponse subject = new AnagSubjectResponse();
        subject.setLanguage(enumLanguage);
        subject.setSubjectType(enumFormaGiuridica);
        subject.setCountryOfBirth(enumBirthPlace);
        subject.setResidence(residence);
        subject.setNode(node);
        subject.setName(input.getName().toUpperCase());
        subject.setNominative(input.getName().concat(" ").concat(input.getSurname()));
        subject.setEmails(
                List.of(null == input.getSecondaryMail() || input.getSecondaryMail().isEmpty() ? input.getPrimaryMail()
                        : input.getSecondaryMail()));
        subject.setMainContact(mainContact);
        subject.setSurname(input.getSurname().toUpperCase());
        subject.setFiscalCode(input.getTaxCode());
        subject.setPersonType(personType);
        subject.setSex(mainContact);
        subject.setDateOfBirth(dateString);
        subject.setPartyKey(List.of(partykey1));
        subject.setBirthAddress(birthAndress);
        subject.setSonsNumber(0);
        subject.setSottogruppoAE(this.buildSottoGruppoAEByExternalCodes(input));

        log.info("subject {}", subject);
        return subject;
    }

    private RSPtfAllldentification buildSottoGruppoAEByExternalCodes(ResponseAnagDTO input) {
        log.info("buildSottoGruppoAEByExternalCodes...");
        RSPtfAllldentification sottoGruppoAE = new RSPtfAllldentification();
        // Estrazione sicura del codice
        String codice = input.getExternalCode().stream()
                .filter(externalCode -> externalCode != null && "saeCodice".equalsIgnoreCase(externalCode.getName()))
                .findFirst()
                .map(ExternalCode::getValue)
                .orElse(null);

        // Estrazione sicura della descrizione
        String descrizione = input.getExternalCode().stream()
                .filter(externalCode -> externalCode != null && "saeDescrizione".equalsIgnoreCase(externalCode.getName()))
                .findFirst()
                .map(ExternalCode::getValue)
                .orElse(null);
        log.info("saeCodice: {}, saeDescrizione: {}", codice, descrizione);
        sottoGruppoAE.setCode(codice);
        sottoGruppoAE.setDescription(descrizione);
        sottoGruppoAE.setIdentification(0L);
        return sottoGruppoAE;
    }


    RSAnagPhone anagPhone = new RSAnagPhone(null, "", null, null, null);

    private AnagSubjectResponse getSubject2(ResponseAnagDTO input, JsonWebToken jwt) {
        TypeEnumRecord enumLanguage;
        if (input.getLanguage().equalsIgnoreCase("DE"))
            enumLanguage = new TypeEnumRecord("5", "TEDESCO");
        else {
            enumLanguage = new TypeEnumRecord("1", "ITALIANO");
        }

        TypeEnumRecord enumFormaGiuridica;
        if (input.getLegalForm().equalsIgnoreCase("PRIVATO"))
            enumFormaGiuridica = new TypeEnumRecord("1", "Privato");
        else {
            enumFormaGiuridica = new TypeEnumRecord("8", null);
        }

        TypeEnumRecord enumBirthPlace;
        if (input.getBirthProvince().equalsIgnoreCase("EE"))
            enumBirthPlace = new TypeEnumRecord("IT", null);
        else {
            enumBirthPlace = new TypeEnumRecord(input.getBirthCity().toUpperCase(), null);
        }

        TypeEnumRecord mainContact;
        if (input.getGender().equalsIgnoreCase("F"))
            mainContact = new TypeEnumRecord("2", "");
        else {
            mainContact = new TypeEnumRecord("1", "");
        }

        TypeEnumRecord personType = new TypeEnumRecord("1", "Fisico");

        RSAnagAddress residence = new RSAnagAddress();
        // residence.setCountry();
        TypeEnumRecord country = new TypeEnumRecord(input.getCountry(), null);
        residence.setCountry(country);
        residence.setCity(input.getCity().toUpperCase());
        residence.setNormalized(false);
        residence.setLatitude(Double.valueOf("0"));
        residence.setLongitude(Double.valueOf("0"));
        residence.setPlaceAddress(input.getStreet());
        residence.setNumber(input.getStreetNumber());
        residence.setCap(input.getZipCode());
        residence.setCountryCode(input.getCountry());
        residence.setAdminLevel3(input.getCity().toUpperCase());
        residence.setAdminLevel2(input.getCity().toUpperCase());
        String codeCity = utilityClientGrpc.getCodeCity(input.getCity(), input.getProvince()).await().indefinitely()
                .getCode();
        residence.setAdminLevel3Short(codeCity.toUpperCase());
        // residence.setAdminLevel2(input.getProvince().toUpperCase());
        // String codeProvince =
        // utilityClientGrpc.getCodeProvince(input.getProvince()).await().indefinitely().getCode();
        residence.setAdminLevel2Short(input.getProvince().toUpperCase());
        // residence.setAdminLevel12Short("");

        RSPtfAllldentification node = new RSPtfAllldentification();
        node.setIdentification(Long.valueOf(idNode));

        RSAnagPhone rsAnagPhone = new RSAnagPhone();
        rsAnagPhone.setObjectId("0");
        rsAnagPhone.setNumber(input.getPrimaryPhone());

        RSAnagPhone rsAnagPhone1 = new RSAnagPhone();
        rsAnagPhone1.setObjectId("1");

        RSAnagPhone rsAnagPhone2 = new RSAnagPhone();
        rsAnagPhone2.setObjectId("2");

        DateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");
        String dateString;
        try {
            Date date = formatter.parse(input.getDateOfBirth());
            SimpleDateFormat newFormat = new SimpleDateFormat("yyyy-MM-dd");
            dateString = newFormat.format(date);
        } catch (Exception e) {
            log.error("Error to format date");
            throw new RuntimeException();
        }

        Key key1 = new Key("Codice Fiscale", 0, null, input.getTaxCode());
        Key key2 = new Key("Partita IVA", 0, null, null);
        Key key3 = new Key("Pseudocode", 0, null, null);
        PartyKey partykey1 = new PartyKey(null, key1, input.getCountry(), key2, null, key3, false);

        RSAnagAddress birthAndress = new RSAnagAddress();
        if (input.getBirthProvince().equalsIgnoreCase("EE")) {
            birthAndress.setAdminLevel2("EE");
            birthAndress.setAdminLevel2Short("EE");
            birthAndress.setCountry(new TypeEnumRecord(input.getBirthCity().toUpperCase(), null));
            birthAndress.setCountryCode(input.getBirthCity().toUpperCase());
            birthAndress.setDetailCode(0);
            birthAndress.setFormatAddress(input.getBirthCity().toUpperCase());
            birthAndress.setNormalized(false);
            birthAndress.setLongitude(Double.valueOf("0"));
            birthAndress.setLatitude(Double.valueOf("0"));
        } else {
            birthAndress.setCountryCode("IT");
            birthAndress.setAdminLevel3Short(input.getTaxCode().substring(11, 15));
            birthAndress.setAdminLevel3(input.getBirthCity().toUpperCase());
            birthAndress.setAdminLevel2(input.getBirthProvince().toUpperCase());
            birthAndress.setAdminLevel2Short(input.getBirthProvince().toUpperCase());
        }

        AnagSubjectResponse subject2 = new AnagSubjectResponse();
        subject2.setLanguage(enumLanguage);
        subject2.setSubjectType(enumFormaGiuridica);
        subject2.setCountryOfBirth(enumBirthPlace);
        subject2.setResidence(residence);
        subject2.setNode(node);
        subject2.setMobilePhone(List.of(rsAnagPhone, rsAnagPhone1, rsAnagPhone2));
        subject2.setName(input.getName().toUpperCase());
        subject2.setNominative(input.getName().concat(" ").concat(input.getSurname()));
        subject2.setEmails(
                List.of(null == input.getSecondaryMail() || input.getSecondaryMail().isEmpty() ? input.getPrimaryMail()
                        : input.getSecondaryMail()));
        subject2.setMainContact(mainContact);
        subject2.setSurname(input.getSurname().toUpperCase());
        subject2.setFiscalCode(input.getTaxCode());
        subject2.setPersonType(personType);
        subject2.setSex(mainContact);
        subject2.setDateOfBirth(dateString);
        subject2.setPartyKey(List.of(partykey1));
        subject2.setBirthAddress(birthAndress);
        subject2.setPhoneFax(anagPhone);
        subject2.setSonsNumber(0);
        subject2.setSottogruppoAE(this.buildSottoGruppoAEByExternalCodes(input));

        log.info("subject2 {}", subject2);
        return subject2;
    }

    public String getToken(JsonWebToken jwt) {
        ResponseLogin login = null;
        login = loginService.getToken(jwt.getClaim("cf").toString());
        String token = "Bearer ".concat(login.token());
        return token;
    }

    public String getTokenCF(String cf) {
        ResponseLogin login = null;
        login = loginService.getToken(cf);
        String token = "Bearer ".concat(login.token());
        return token;
    }

    private static ConsensoPrivacy getConsensoPrivacy(ResponseAnagDTO input) {
        ConsensoPrivacy marketingConsens = new ConsensoPrivacy();
        marketingConsens.setCodice("CS01");
        DatoAggiuntoPrivacy datiAggiuntiviMail = new DatoAggiuntoPrivacy();
        datiAggiuntiviMail.setTipoDato("EMAIL");
        datiAggiuntiviMail.setObbligatorio(true);
        datiAggiuntiviMail.setFlagRichiestaCertificazione(false);
        datiAggiuntiviMail.setFlagStatoCertificazione(false);
        datiAggiuntiviMail.setValoreDefault(input.getPrimaryMail());
        DatoAggiuntoPrivacy datiAggiuntiviPhone = new DatoAggiuntoPrivacy();
        datiAggiuntiviPhone.setTipoDato("TELEFONO");
        datiAggiuntiviPhone.setObbligatorio(false);
        datiAggiuntiviPhone.setFlagRichiestaCertificazione(false);
        datiAggiuntiviPhone.setFlagStatoCertificazione(false);
        datiAggiuntiviPhone.setValoreDefault(input.getPrimaryPhone());
        marketingConsens.setDatiAggiuntivi(List.of(datiAggiuntiviMail, datiAggiuntiviPhone));
        marketingConsens.setDescrizione(
                "Acconsento al trattamento dei Dati Personali per ricerche di mercato e/o finalità promozionali (Marketing)");
        marketingConsens.setRisposta(
                input.getExternalCode().stream().filter(externalCode -> externalCode.getName().equalsIgnoreCase("CS01"))
                        .findFirst().get().getValue().equalsIgnoreCase("true") ? "SI" : "NO");
        marketingConsens.setMesiValidita(24);
        marketingConsens.setClasse("MARKETING");
        marketingConsens.setTipoGestioneVigore("ARCHIVIAZ_DOC");
        marketingConsens.setInformativaPrivacy(getInformativaPrivacy());
        return marketingConsens;
    }

    private static ConsensoPrivacy getConsensoProfil(ResponseAnagDTO input) {
        ConsensoPrivacy marketingConsens = new ConsensoPrivacy();
        marketingConsens.setCodice("C012");
        marketingConsens.setDatiAggiuntivi(new ArrayList<>());
        marketingConsens.setDescrizione(
                "Mi oppongo alla personalizzazione delle comunicazioni promozionali a me destinate (opposizione a trattamenti di profilazione svolti sulla base del legittimo interesse)");
        marketingConsens.setRisposta(
                input.getExternalCode().stream().filter(externalCode -> externalCode.getName().equalsIgnoreCase("C012"))
                        .findFirst().get().getValue().equalsIgnoreCase("true") ? "SI" : "NO");
        marketingConsens.setMesiValidita(9999);
        marketingConsens.setClasse("OPPOS_PROFIL");
        marketingConsens.setTipoGestioneVigore("REGISTRAZ");
        marketingConsens.setInformativaPrivacy(getInformativaPrivacy());
        return marketingConsens;
    }

    private static ConsensoPrivacy getConsensoDocument(ResponseAnagDTO input) {
        ConsensoPrivacy marketingConsens = new ConsensoPrivacy();
        marketingConsens.setCodice("C007");
        DatoAggiuntoPrivacy datiAggiuntiviMail = new DatoAggiuntoPrivacy();
        datiAggiuntiviMail.setTipoDato("EMAIL");
        datiAggiuntiviMail.setObbligatorio(true);
        datiAggiuntiviMail.setFlagRichiestaCertificazione(false);
        datiAggiuntiviMail.setFlagStatoCertificazione(false);
        datiAggiuntiviMail.setValoreDefault(input.getPrimaryMail());
        marketingConsens.setDatiAggiuntivi(List.of(datiAggiuntiviMail));
        marketingConsens.setDescrizione("Aderisco all'invio della documentazione Precontrattuale");
        marketingConsens.setRisposta(
                input.getExternalCode().stream().filter(externalCode -> externalCode.getName().equalsIgnoreCase("C007"))
                        .findFirst().get().getValue().equalsIgnoreCase("true") ? "SI" : "NO");
        marketingConsens.setMesiValidita(9999);
        marketingConsens.setClasse("INVIO_PRECONTR");
        marketingConsens.setTipoGestioneVigore("REGISTRAZ");
        marketingConsens.setInformativaPrivacy(getInformativaPrivacy());
        return marketingConsens;
    }

    private static InformativaPrivacy getInformativaPrivacy() {
        InformativaPrivacy informativaPrivacy = new InformativaPrivacy();
        informativaPrivacy.setCodiceInformativa("I00010");
        informativaPrivacy.setCodiceStampa("S010");
        informativaPrivacy.setDescrizioneInformativa("Nota Informativa sulla Privacy");
        FileInformativaPrivacy fileInformativaPrivacy = new FileInformativaPrivacy();
        fileInformativaPrivacy.setLingua("000001");
        fileInformativaPrivacy.setNomeFileInformativa("notaInformativaPrivacy_ed112021.pdf");
        informativaPrivacy.setFilePerLingua(List.of(fileInformativaPrivacy));
        return informativaPrivacy;
    }

    private Response getResponseFromAnagService(JsonWebToken jwt, String fiscalCode, String token) {
        try {
            return rgiAnagClient
                    .searchByFiscalCode("", archiveCode, idNode, fiscalCode, token)
                    .await()
                    .indefinitely();
        } catch (Exception e) {
            loginService.invalidateCache(fiscalCode);
            token = getToken(jwt);  // Recupera nuovo token
            return rgiAnagClient
                    .searchByFiscalCode("", archiveCode, idNode, fiscalCode, token)
                    .await()
                    .indefinitely();
        }
    }

    private RGIAnagResponseDTO deserializeAnagResponse(Response response) throws IOException {
        byte[] responseFC = response.readEntity(byte[].class);
        ObjectMapper objectMapper = new ObjectMapper()
                .configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, false)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        String decompressedData = Decompress.decompress(responseFC);
        log.info("Decompressed response data: {}", decompressedData);
        return objectMapper.readValue(decompressedData, RGIAnagResponseDTO.class);
    }

    private ResponseAnagDTO buildResponseDTO(RGIAnagResponseDTO responseAnag, String fiscalCode, String codeAgency, JsonWebToken jwt) {
        ResponseAnagDTO responseAnagDTO = new ResponseAnagDTO();
        List<ExternalCode> externalCodes = new ArrayList<>();

        if (responseAnag != null && responseAnag.getData() != null && responseAnag.getData().getSubject() != null) {
            externalCodes.add(new ExternalCode("existAssimoco", "true"));

            if (responseAnag.getData().getSubject().getObjectId() != null) {
                responseAnagDTO.setCustomerCode(responseAnag.getData().getSubject().getObjectId().toString());
                externalCodes.add(new ExternalCode("partyCode", responseAnagDTO.getCustomerCode()));
            } else {
                log.info("ObjectId is null for fiscalCode: {}", fiscalCode);
            }

            if (responseAnag.getData().getSubject().getIdLatestPhotos() != null) {
                externalCodes.add(new ExternalCode("specializedPartyCode", responseAnag.getData().getSubject().getIdLatestPhotos().toString()));
            } else {
                log.info("IdLatestPhotos is null for fiscalCode: {}", fiscalCode);
            }

            responseAnagDTO.setName(getSafeString(responseAnag.getData().getSubject().getName()));
            responseAnagDTO.setSurname(getSafeString(responseAnag.getData().getSubject().getSurname()));
            responseAnagDTO.setDateOfBirth(getSafeString(responseAnag.getData().getSubject().getDateOfBirth()));
            responseAnagDTO.setTaxCode(getSafeString(responseAnag.getData().getSubject().getFiscalCode()));
            responseAnagDTO.setGender(getSafeString(responseAnag.getData().getSubject().getSex()));
            responseAnagDTO.setLanguage(determineLanguage(getSafeString(responseAnag.getData().getSubject().getLanguage())));
        } else {
            log.info("ResponseAnag or its data/subject is null for fiscalCode: {}", fiscalCode);
        }


        externalCodes.add(new ExternalCode("idSp", retrieveIterativeAgencies(codeAgency, jwt)));
        externalCodes.add(new ExternalCode("codeAgency", codeAgency));
        responseAnagDTO.setExternalCode(externalCodes);

        return responseAnagDTO;
    }

    private String getSafeString(Object obj) {
        return obj != null ? obj.toString() : "";
    }

    private String determineLanguage(String languageCode) {
        return "1".equalsIgnoreCase(languageCode) ? "DE" : "IT";
    }

    private String retrieveIterativeAgencies(String codeAgency, JsonWebToken jwt) {
        try {
            return iterativeAgenciesService.getIterativeAgencies(codeAgency, jwt);
        } catch (Exception e) {
            log.info("Error retrieving iterative agencies: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Utility per loggare e decomprimere la response di errore da ClientWebApplicationException
     */
    private void logDecompressedClientError(ClientWebApplicationException ex, String context) {
        Response errorResponse = ex.getResponse();
        if (errorResponse != null && errorResponse.hasEntity()) {
            try {
                byte[] errorBytes = errorResponse.readEntity(byte[].class);
                String decompressedError = Decompress.decompress(errorBytes);
                log.error("{} - Errore response decompressa da RGIAnagClient: {}", context, decompressedError);
            } catch (Exception dex) {
                log.error("{} - Errore durante la decompressione della response di errore: {}", context, dex.getMessage());
            }
        } else {
            log.error("{} - ClientWebApplicationException senza entity: {}", context, ex.getMessage());
        }
    }

}
