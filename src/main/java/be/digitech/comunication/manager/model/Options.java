package be.digitech.comunication.manager.model;

import be.digitech.comunication.manager.enums.MessageTypeEnum;
import be.digitech.comunication.manager.validator.ValueInEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.Email;
import java.util.Arrays;
import java.util.List;


public class Options   {

  @Email(message = "fromemail must be a valid email address")
  @JsonProperty("fromemail")
  private String fromemail = null;

  @JsonProperty("fromname")
  private String fromname = null;

  @Email(message = "replyto must be a valid email address")
  @JsonProperty("replyto")
  private String replyto = null;

  @JsonProperty("toMail")
  private String toMail;

  @JsonProperty("toMailList")
  private List<String> toMailList;

  @JsonProperty("cc")
  private List<String> cc;

  @JsonProperty("subject")
  private String subject = null;

  //@Null
  @JsonProperty("name")
  private String name = null;

  //@Null
  @JsonProperty("surname")
  private String surname = null;

  @JsonProperty("messagebody")
  private String messagebody = null;

  @ValueInEnum(enumClass = MessageTypeEnum.class, message = "messaggetype must be text or html")
  @JsonProperty("messaggetype")
  private String messaggetype = null;

  @JsonProperty("emailtype")
  private String emailtype;

  @JsonProperty("language")
  private String language;

  @JsonProperty("toNumberPhone")
  private String toNumberPhone;

  @JsonProperty("fromNumberPhone")
  private String fromNumberPhone;

  @JsonProperty("template-placeholder")
  private List<MapTemplate> templatePlaceholder;

  @JsonProperty("urgent")
  private Boolean urgent = false;

  public String getMessagebody() {
    return messagebody;
  }

  public void setMessagebody(String messagebody) {
    this.messagebody = messagebody;
  }

  public String getMessaggetype() {
    return messaggetype;
  }

  public void setMessaggetype(String messaggetype) {
    this.messaggetype = messaggetype;
  }

  public String getFromemail() {
    return fromemail;
  }

  public void setFromemail(String fromemail) {
    this.fromemail = fromemail;
  }

  public String getFromname() {
    return fromname;
  }

  public void setFromname(String fromname) {
    this.fromname = fromname;
  }

  public String getReplyto() {
    return replyto;
  }

  public void setReplyto(String replyto) {
    this.replyto = replyto;
  }

  public String getSubject() {
    return subject;
  }

  public void setSubject(String subject) {
    this.subject = subject;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getSurname() {
    return surname;
  }

  public void setSurname(String surname) {
    this.surname = surname;
  }

  public String getEmailtype() {
    return emailtype;
  }

  public void setEmailtype(String emailtype) {
    this.emailtype = emailtype;
  }

  public String getLanguage() {
    return language;
  }

  public void setLanguage(String language) {
    this.language = language;
  }

  public List<String> getToMailList() {
    return toMailList;
  }

  public void setToMailList(List<String> toMailList) {
    this.toMailList = toMailList;
  }

  public String getToMail() {
    return toMail;
  }

  public void setToMail(String toMail) {
    this.toMail = toMail;
  }

  public List<String> getCc() {
    return cc;
  }

  public void setCc(List<String> cc) {
    this.cc = cc;
  }

  public String getToNumberPhone() {
    return toNumberPhone;
  }

  public void setToNumberPhone(String toNumberPhone) {
    this.toNumberPhone = toNumberPhone;
  }

  public String getFromNumberPhone() {
    return fromNumberPhone;
  }

  public void setFromNumberPhone(String fromNumberPhone) {
    this.fromNumberPhone = fromNumberPhone;
  }

  public List<MapTemplate> getTemplatePlaceholder() {
    return templatePlaceholder;
  }

  public void setTemplatePlaceholder(List<MapTemplate> templatePlaceholder) {
    this.templatePlaceholder = templatePlaceholder;
  }
  public Boolean getUrgent() {
    return urgent;
  }

  public void setUrgent(Boolean urgent) {
    this.urgent = urgent;
  }

  @Override
  public String toString() {
    return "Options{" +
            "fromemail='" + fromemail + '\'' +
            ", fromname='" + fromname + '\'' +
            ", replyto='" + replyto + '\'' +
            ", toMail='" + toMail + '\'' +
            ", toMailList=" + toMailList +
            ", cc=" + cc +
            ", subject='" + subject + '\'' +
            ", name='" + name + '\'' +
            ", surname='" + surname + '\'' +
            ", messagebody='" + messagebody + '\'' +
            ", messaggetype='" + messaggetype + '\'' +
            ", emailtype='" + emailtype + '\'' +
            ", language='" + language + '\'' +
            ", toNumberPhone='" + toNumberPhone + '\'' +
            ", fromNumberPhone='" + fromNumberPhone + '\'' +
            ", templatePlaceholder=" + templatePlaceholder +
            ", urgent=" + urgent +
            '}';
  }
}

