package be.digitech.comunication.manager.utils;

import com.google.common.annotations.VisibleForTesting;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;
import io.quarkus.grpc.GlobalInterceptor;
import org.apache.commons.lang3.RandomStringUtils;
import org.jboss.logging.MDC;

import javax.enterprise.context.ApplicationScoped;

@ApplicationScoped
@GlobalInterceptor
public class InterceptorServerGrpc implements ServerInterceptor {

    @VisibleForTesting
    static final Metadata.Key<String> CUSTOM_HEADER_KEY = Metadata.Key.of("traceId",
            Metadata.ASCII_STRING_MARSHALLER);

    @Override
    public <I, O> ServerCall.Listener<I> interceptCall(
            ServerCall<I, O> call,
            final Metadata requestHeaders,
            ServerCallHandler<I, O> next) {

        MDC.put("spanId", RandomStringUtils.randomAlphanumeric(16).toLowerCase());
        MDC.put("traceId",requestHeaders.get(CUSTOM_HEADER_KEY) != null ? requestHeaders.get(CUSTOM_HEADER_KEY) : RandomStringUtils.randomAlphanumeric(32).toLowerCase() );


        return next.startCall(call, requestHeaders);
    }


}