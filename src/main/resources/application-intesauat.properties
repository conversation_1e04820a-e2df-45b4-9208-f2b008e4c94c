#DATABASE
quarkus.datasource.db-kind=postgresql
quarkus.datasource.username=userapp
quarkus.datasource.password=srC34UVvMoeoio772JK8m5jEDGesV86a
quarkus.datasource.jdbc.url=**********************************************************************************************
quarkus.datasource.jdbc.driver=org.postgresql.Driver
#OIDC AUTH SERVER VALIDATION TOKEN
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_kqvqiowgN
#PRODUCT REST CLIENT
quarkus.rest-client.product.url=https://intesa-api.preprod.yoloassicurazioni.it/iad-product