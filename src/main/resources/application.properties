# quarkus.application.name=
quarkus.banner.enabled=false
quarkus.http.port=8081
# Configure default tenant datasource
quarkus.datasource.db-kind=postgresql
quarkus.datasource.username=desio_uat
quarkus.datasource.password=5n93BwV3yTd5Wv6Zv
quarkus.datasource.jdbc.url=****************************************************************************************
quarkus.datasource.jdbc.driver=org.postgresql.Driver
# Hibernate
quarkus.hibernate-orm.dialect=org.hibernate.dialect.PostgreSQLDialect
quarkus.hibernate-orm.jdbc.timezone=UTC
quarkus.hibernate-orm.log.sql=true
# Image build
#quarkus.native.container-build=true
#JWT configuration
# JWT disable token
quarkus.http.auth.proactive=false
quarkus.oauth2.enabled=false
quarkus.security.enabled=false
quarkus.smallrye-jwt.enabled=false
# Logging
quarkus.log.console.format=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{2.}] (%t) %s%e%n
# Open API Settings
quarkus.smallrye-openapi.path=/openapi
quarkus.smallrye-openapi.info-title=Order API
quarkus.smallrye-openapi.info-version=1.0.0
# Swagger UI
quarkus.swagger-ui.always-include=true
quarkus.swagger-ui.path=/openapi/swagger-ui
quarkus.rest-client.extensions-api.hostname-verifier=io.quarkus.restclient.NoopHostnameVerifier
quarkus.tls.trust-all=true
#POLICY REST CLIENT
quarkus.rest-client.policy.url=http://iad-policy-deploy:8080/
quarkus.rest-client.policy.scope=javax.inject.Singleton
#CUSTOMER MANAGER REST CLIENT
quarkus.rest-client.customer-manager.url=http://iad-customer-manager-deploy:8080/
quarkus.rest-client.customer-manager.scope=javax.inject.Singleton
#PRODUCT REST CLIENT
quarkus.rest-client.product.url=http://iad-product-deploy:8080/
quarkus.rest-client.product.scope=javax.inject.Singleton
#CATALOG REST CLIENT
#quarkus.rest-client.catalog.url=https://api.preprod.yoloassicurazioni.it/iad-policy
quarkus.rest-client.catalog.url=http://iad-catalog-deploy.yep--uat.svc.cluster.local:8080/
quarkus.rest-client.catalog.scope=javax.inject.Singleton
quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG
quarkus.log.level=DEBUG
#OIDC AUTH SERVER VALIDATION TOKEN
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_6vciiWUmN
quarkus.grpc.clients.cmsServiceGrpc.host=localhost
quarkus.grpc.clients.cmsServiceGrpc.port=9003
quarkus.rest-client.cms-service.url=http://cms-service-deploy:8080
language=it_IT
quarkus.grpc.server.port=9001
