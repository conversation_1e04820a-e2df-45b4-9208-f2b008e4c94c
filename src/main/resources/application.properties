quarkus.banner.enabled=true
quarkus.profile=dev
tenant = yolo
base-url = https://${tenant}-api.${quarkus.profile}.yoloassicurazioni.it
quarkus.http.port=8085
quarkus.rest-client.quote.url=http://providers-gateway-deploy.yep-dev.svc.cluster.local:8080/providers/quote
quarkus.rest-client.quote.scope=javax.inject.Singleton
#Logging opentelemetry
quarkus.opentelemetry.enabled=true
quarkus.opentelemetry.tracer.enabled=false
quarkus.log.console.format=%d{HH:mm:ss} %-5p traceId=%X{traceId}, spanId=%X{spanId}, correlationId=%X{correlationId}, [%c] -> %s%e%n
quarkus.log.level=DEBUG
#COUPON REST CLIENT
quarkus.rest-client.coupon-url.url=${base-url}/multitenant-backoffice-be
quarkus.rest-client.coupon-url.scope=javax.inject.Singleton
#ORDER REST CLIENT
quarkus.rest-client.iad-order.url=${base-url}/iad-order
quarkus.rest-client.iad-order.scope=javax.inject.Singleton
#OIDC-AUTH-SERVER
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_lJs5dJrPD
#CUSTOMER REST CLIENT
quarkus.rest-client.iad-customer.url=${base-url}/iad-customer
quarkus.rest-client.iad-customer.scope=javax.inject.Singleton
#IAM REST CLIENT
quarkus.rest-client.iam.url=${base-url}/iad-token
quarkus.rest-client.iam.scope=javax.inject.Singleton
#PROVIDER-GATEWAY REST CLIENT
quarkus.rest-client.provider-gateway.url=http://localhost:3000
quarkus.rest-client.provider-gateway.scope=javax.inject.Singleton
#IAD-PRODUCT REST CLIENT
quarkus.rest-client.iad-product.url=${base-url}/iad-product
quarkus.rest-client.iad-product.scope=javax.inject.Singleton
#IAD-ADP REST CLIENT
quarkus.rest-client.adapter.url=http://iad-adp-deploy.yep-dev.svc.cluster.local:8080
quarkus.rest-client.adapter.scope=javax.inject.Singleton
#ENGINE-PRICE REST CLIENT
quarkus.rest-client.engine-price.url=http://internal:8080 #to be defined
quarkus.rest-client.engine-price.scope=javax.inject.Singleton
# VERTI PRICING
quarkus.rest-client.verti-motor.engine-price.url=http://verti-pricing-deploy:8080
quarkus.rest-client.verti-motor.read-timeout=50000
quarkus.rest-client.verti-motor.engine-price.scope=javax.inject.Singleton
#TCH USER
tch.user.username=tchusr29c23d122s
tch.user.password=0eRFN6Uq09N9!
#BUSINESS PROP
tenant.name=YOLO
#quarkus.resteasy-json.default-json=false
#GRPC CLIENT-TOKEN
quarkus.grpc.clients.tokenService.host=iad-token-deploy.yolo-dev.svc.cluster.local
quarkus.grpc.clients.tokenService.port=9000
