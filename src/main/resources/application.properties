quarkus.banner.enabled=false
quarkus.http.port=8080
# Configure default tenant datasource
quarkus.datasource.db-kind=postgresql
quarkus.datasource.username=megauser
quarkus.datasource.password=W5ZDxien5zuNskAQef8Mnz96gsdMHHo
quarkus.datasource.jdbc.url=***************************************************************************************
quarkus.datasource.jdbc.driver=org.postgresql.Driver
# Image build
#quarkus.native.container-build=true
# JWT configuration
# JWT disable token
quarkus.http.auth.proactive=false
quarkus.oauth2.enabled=true
quarkus.security.enabled=false
quarkus.smallrye-jwt.enabled=false
# JWT configuration
mp.jwt.verify.publickey.location=publicKey.pem
mp.jwt.decrypt.key.location=keyEncryptPrivate.pem
smallrye.jwt.decrypt.algorithm=RSA_OAEP_256
smallrye.jwt.key-encryption-algorithm=RSA-OAEP-256
# Logging
quarkus.log.level=DEBUG
quarkus.log.console.format=%d{HH:mm:ss} %-5p traceId=%X{traceId}, parentId=%X{parentId}, spanId=%X{spanId}, sampled=%X{sampled} [%c{2.}] (%t) %s%e%n
# Open Telemetry
quarkus.opentelemetry.enabled=true
quarkus.opentelemetry.tracer.enabled=false
quarkus.log.console.format=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{2.}] (%t) %s%e%n
quarkus.rest-client.logging.scope=request-response
quarkus.rest-client.logging.body-limit=9024
quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG
# Configurazione client anagrafica
quarkus.rest-client.arsso.url=https://arsso-t.assimoco.it/ar-sso/assimoco
client.anagrafica.assimoco.apikey=YWY4Zjc1YTVlY2FjMDgxYTY3ODBiMTViNmNkMmYwZmZiNzA3
quarkus.rest-client.extensions-api.hostname-verifier=io.quarkus.restclient.NoopHostnameVerifier
quarkus.tls.trust-all=false
quarkus.rest-client.rest-api.follow-redirects=true
quarkus.rest-client.assimocoanag.url=https://yolo-api.dev.yoloassicurazioni.it/assimoco-service
quarkus.rest-client.assimocoanag.scope=javax.inject.Singleton
quarkus.rest-client.coumication.url=https://yolo-api.dev.yoloassicurazioni.it/comunication-manager
quarkus.rest-client.coumication.scope=javax.inject.Singleton
quarkus.rest-client.ar-sso.url=https://arsso-t.assimoco.it
quarkus.rest-client.ar-sso.scope=javax.inject.Singleton
# Configurazione Certificati
#quarkus.http.ssl.certificate.files=certificate/certificate.pem
#quarkus.http.ssl.certificate.key-files=certificate/key.pem
prova.mock={"external_code":[{"ObjectCode":"ObjCode1"},{"PartiesCode":"1111"}]}
quarkus.ssl.native=true
quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG
quarkus.log.level=DEBUG
#GRPC CLIENT-TOKEN
quarkus.grpc.clients.iamGrpc.host=iad-token-deploy.yolo-dev.svc.cluster.local
quarkus.grpc.clients.iamGrpc.port=9000
#GRPC SERVER
#quarkus.grpc.server.port=9001
#ADDRESS-GATEWAY
quarkus.rest-client.iad-address.url=http://iad-address-gateway-deploy:8080/v1/stub/yolo
#OIDC-AUTH-SERVER
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_lJs5dJrPD
#ADAPTER CUSTOMER REST CLIENT
quarkus.rest-client.adp-customer.url=http://iad-adp-deploy.yep-dev.svc.cluster.local:8080
quarkus.rest-client.adp-customer.scope=javax.inject.Singleton
#IAD-UTILITY
quarkus.rest-client.iad-utility.url=https://yolo-api.dev.yoloassicurazioni.it/iad-utility-deploy
quarkus.rest-client.iad-utility.scope=javax.inject.Singleton
#CORE RUBY CLIENT
quarkus.rest-client.core-ruby.url=https://yolo-integration.yolo-insurance.com
quarkus.rest-client.core-ruby.scope=javax.inject.Singleton
#BUSINESS PROP
ndg.techincalUser=TECHNICAL_USER
intermediary.users=intermediary-users
technical.users=technical-users
language=it_IT
quarkus.grpc.clients.cmsServiceGrpc.host=cms-service-deploy.yolo-dev.svc.cluster.local
quarkus.grpc.clients.cmsServiceGrpc.port=9000

#COMUNICATION-MANAGER REST CLIENT
quarkus.rest-client.communication-manager.url=http://comunication-manager-deploy:8080
quarkus.rest-client.communication-manager.scope=javax.inject.Singleton

# CLIENT iad-configuration
quarkus.rest-client.iad-configuration.url=http://iad-configuration-deploy:8080
quarkus.rest-client.iad-configuration.scope=javax.inject.Singleton

preregistration.redirect.url=https://yolo.dev.yoloassicurazioni.it/nyp-login/register-confirm-data
preregistration.mail.key=PREREGISTRAZIONE_UTENTE_MAIL
preregistration.sms.key=PREREGISTRAZIONE_UTENTE_SMS
registration.mail.key=REGISTRAZIONE_UTENTE_MAIL
registration.mail.privateAreaUrl=https://yolo.dev.yoloassicurazioni.it/nyp-private-area/home

# Add the missing pagination configuration
default.itemTotal.pagination=10

quarkus.http.auth.lazy: true