quarkus.banner.enabled=false
quarkus.http.port=8080
tentant = yolo
quarkus.profile=dev
base-url = https://${tentant}-api.${quarkus.profile}.yoloassicurazioni.it
# JWT configuration
# JWT disable token
quarkus.http.auth.proactive=false
quarkus.oauth2.enabled=false
quarkus.security.enabled=false
quarkus.smallrye-jwt.enabled=false
# Image build
#quarkus.native.container-build=false
# Open API Settings
quarkus.smallrye-openapi.path=/openapi
quarkus.smallrye-openapi.info-title=Address Gateway Deploy API
quarkus.smallrye-openapi.info-version=1.0.0
# Swagger UI
quarkus.swagger-ui.always-include=true
quarkus.swagger-ui.path=/openapi/swagger-ui
#Quarkus Config ssl
quarkus.rest-client.extensions-api.hostname-verifier=io.quarkus.restclient.NoopHostnameVerifier
quarkus.tls.trust-all=true
# LOGGING /LEVEL
quarkus.log.level=DEBUG
quarkus.log.console.format=%d{HH:mm:ss} %-5p traceId=%X{traceId}, parentId=%X{parentId}, spanId=%X{spanId}, sampled=%X{sampled} [%c{2.}] (%t) %s%e%n
#MOTION CLOUD REST CLIENT
quarkus.rest-client.motion-cloud.url=https://netins-staging.motionscloud.com/api
quarkus.rest-client.motion-cloud.scope=javax.inject.Singleton
#IAD-POLICY REST CLIENT
quarkus.rest-client.iad-policy.url=${base-url}/iad-policy
quarkus.rest-client.iad-policy.scope=javax.inject.Singleton
#COMUNICATION-MANAGER REST CLIENT
quarkus.rest-client.communication-manager.url=${base-url}/comunication-manager
quarkus.rest-client.communication-manager.scope=javax.inject.Singleton
#IAD-DOCUMENT REST CLIENT
quarkus.rest-client.iad-document.url=${base-url}/iad-document
quarkus.rest-client.iad-document.scope=javax.inject.Singleton
#DOCUMENT-MANAGER REST CLIENT
quarkus.rest-client.iad-document-manager.url=${base-url}/iad-document-manager
quarkus.rest-client.iad-document-manager.scope=javax.inject.Singleton
#IAD PRODUCT
quarkus.rest-client.iad-product.url=${base-url}/iad-product
quarkus.rest-client.iad-product.scope=javax.inject.Singleton
#BRAIN-TREE
quarkus.rest-client.brain-tree.url=${base-url}/payment-braintree
quarkus.rest-client.brain-tree.scope=javax.inject.Singleton
#ADP-PG
quarkus.rest-client.adp-pg.url=http://iad-adp-deploy.yep-dev.svc.cluster.local:8080
quarkus.rest-client.adp-pg.scope=javax.inject.Singleton
#PROVIDER-GATEWAY REST CLIENT
quarkus.rest-client.provider-gateway.url=http://localhost:3000
quarkus.rest-client.provider-gateway.scope=javax.inject.Singleton
#ORDER REST CLIENT
quarkus.rest-client.iad-order.url=${base-url}/iad-order
quarkus.rest-client.iad-order.scope=javax.inject.Singleton
#PRICE REST CLIENT
quarkus.rest-client.iad-price.url=${base-url}/iad-pricing
quarkus.rest-client.iad-price.scope=javax.inject.Singleton
#IAD CUSTOMER
quarkus.rest-client.iad-customer.url=${base-url}/iad-customer
quarkus.rest-client.iad-customer.scope=javax.inject.Singleton
#IAD-CONFIGURATION
quarkus.rest-client.iad-configuration.url=${base-url}/iad-configuration
quarkus.rest-client.iad-configuration.scope=javax.inject.Singleton
#STRIPE
quarkus.rest-client.stripe.url=${base-url}/payment-stripe
quarkus.rest-client.stripe.scope=javax.inject.Singleton
#PG
quarkus.rest-client.pg.url=http://localhost:3000
quarkus.rest-client.pg.scope=javax.inject.Singleton
#REST CLIENT LOG
quarkus.rest-client.logging.scope=request-response
quarkus.rest-client.logging.body-limit=2000000
quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG
#OIDC AUTH SERVER VALIDATION TOKEN
quarkus.oidc.auth-server-url=https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_lJs5dJrPD
#BUSINESS PROP
tenant.name=YOLO
motionCloud.registration.name=Yolo
motionCloud.registration.email=<EMAIL>
motionCloud.authToken=eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjozMiwiZXhwIjoxODA4MjkyMzI2fQ.agkDLFMlBzUJzhVS6NVKnaIMOEUQGfgWHVF0I3kryrg
motionCloud.userAgent=YoloService
pg.usernameFailed = ""
pg.password=""
pg.username=""
# JWT Redirect Configuration
app.jwt.redirect.expiry-minutes=15
app.jwt.redirect.issuer=insurance-platform
app.jwt.redirect.secret= myverysecretkeyforredirectjwtservice123456789012345678901234
# Cache Configuration for Redirect Tokens
quarkus.cache.caffeine."redirect-tokens".initial-capacity=50
quarkus.cache.caffeine."redirect-tokens".maximum-size=500
quarkus.cache.caffeine."redirect-tokens".expire-after-write=PT15M
