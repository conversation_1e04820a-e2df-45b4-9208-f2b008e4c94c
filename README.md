# **IAM API v1.0.1**

| AUTORE | VERSIONE | DATA | APPROVAL |
|---|---|---|---|
|-| 1.0.1| 12/02/2023|-| 

---
---

## **Sommario**
1. &ensp; Registrazione (Rest)
2. &ensp; <PERSON>gin (Rest)
3. &ensp; Get userInfo (REST e GRPC)
4. &ensp; Logout
5. &ensp; Refresh (REST e GRPC)
6. &ensp; Update (REST e GRPC)
7. &ensp; Forgot Password
8. &ensp; Reset Password
9. &ensp; SSO registrazione (GRPC)
10. &ensp; Confirm SignUp
11. &ensp; GetAll Users
12. &ensp; AddImportedUserInToGroup
13. &ensp; LoginTechnicalUser
14. &ensp; DeleteUsers
15. &ensp; Intermediaryregistration
16. &ensp; ForceChangePassword
17. &ensp; Changepassword
18. &ensp; VerifyCaptcha

---

## **1.** **Registrazione (REST)**
La seguente api può essere utilizzata per registrare un utente sullo IAM Cognito.
Se risponde con stato created, significa che è stato creato l’utente ma prima di poter effettuare l’acceso, bisogna confermare la mail.
La conferma avviene cliccando sul link inviato nel corpo della mail.


| API                                                            | host/v1/iam/registration                                                                                                                                                                                                                                                                                                                                                                                       |
|----------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Protocollo                                                     | https                                                                                                                                                                                                                                                                                                                                                                                                          |
| Tipo                                                           | POST                                                                                                                                                                                                                                                                                                                                                                                                           |
| Campi Header <br> **Grassetto = campo** <br>Obbligatorio       ||
| TIPO DATI INPUT                                                ||
| Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio | {<br>**"username"**: "Codice fiscale",<br>**"password"**: "Password utente", #1<br>"userSso": false, #2 <br>"ndgCode": "code external",<br>"phoneNumber": "phone number con il prefisso +39",<br>**"email"**: "Email",<br>"surname": "Cognome",<br>"name": "Nome",<br>"birthDate":"dd-mm-aaaa", #3<br>"privacy": true,<br>"commerce":false,<br>"trace":false,<br>“externalinfo”: informazioni aggiuntive"<br>} |
| Caso KO 400 <br> Bad request <br>Errore campo mancante         |                                                                                                                                                                                                                                                                                                                                                                                                                |
| Caso KO 409 <br> Conflict <br>Username Exists                  |
| Caso OK 201 Created                                            ||

> #1 Deve essere almeno 8 caratteri, con almeno un numero, un carattere speciale, un carattere maiuscolo      e un carattere minuscolo.
>
> #2 Inserire false o non popolarlo se è un utente che sta effettuando la registrazione su yolo.
>
> #3 Il formato della data deve essere dd-mm-aaaa.

---
## **2.** **Login (REST)**
La seguente api può essere utilizzata per ottenere un access token per un utente registarto e che ha confermato la mail.

| API                                                                      | host/v1/iam/login                                                                                                                                                                                                                                                                                                                                                                                                           |
|--------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Protocollo                                                               | https                                                                                                                                                                                                                                                                                                                                                                                                                       |
| Tipo                                                                     | POST                                                                                                                                                                                                                                                                                                                                                                                                                        |
| Campi Header <br> **Grassetto = campo** <br>Obbligatorio                 ||
| TIPO DATI INPUT                                                          ||
| Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio           | {<br> **"username"**:"tlscld53a23d162s",<br> **"password"**: "Password2343!"<br>}                                                                                                                                                                                                                                                                                                                                           |
| Caso KO 401 <br> Unauthorize - usename o psw errati/email non confermata | {<br>"code": 401,<br>“error” : “username or password are not correct”<br>}                                                                                                                                                                                                                                                                                                                                                                  |
| Caso OK Rest API 200 <br> Output Atteso                                  | {<br>"token": "eyJjdHkiOiJKV1QiLCJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIn0.H6JuKk11gJFijifm7KVEBaYiWmz1vBOtH5dCIY2T2hgebGvP6dQw_7lzhXUEitlwP-f26s1NwAwRGbBhImr8S1Mufw49FKkz54UKGViFfX-TCJOg-xgsHpxkH9_pnPEiXzsDNlbRTVcP2m1WDyq9sQei8J2BUKj3F0eGGQQqLLNw3Rbgi7QouPvV1bNSOyplSt0CusRFVsimpz9hm_A_ _B5XLJXztbTCp-UPDxSp5BIIvUT4RU5n4Vz5MS6MQNuftsx4hp0b__RVHRo3lyM087Jy6Fv2UqA2jcOvINddug.Z5mFrYSl_PA711np_Do0Aw"<br>} |

---

## **3.** **Get userInfo (REST e GRPC)**
La seguente api può essere utilizzata per recuperare le informazione presente sullo iam grazie all’access token.
Per accedere all’api è necessario quindi avere un token valido. Nel caso di chiamata gRPC l’access token non viene passato all’interno dell’header ma nel corpo della request.

|API| host/v1/iam/userInfo|
|---|---|
|Protocollo| https |
|Tipo| GET |
|Campi Header <br> **Grassetto = campo** <br>Obbligatorio|**Authorization: Bearer** <br> eyJjdHkiOiJKV1QiLCJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIn0.H6JuKk11gJFijifm7KVEBaYiWmz1vBOtH5dCIY2T2hgebGvP6dQw_7lzhXUEitlwP-f26s1NwAwRGbBhImr8S1Mufw49FKkz54UKGViFfX-TCJOg-xgsHpxkH9_pnPEiXzsDNlbRTVcP2m1WDyq9sQei8J2BUKj3F0eGGQQqLLNw3Rbgi7QouPvV1bNSOyplSt0CusRFVsimpz9hm_A_VTjbOY0t0sGkCrZzgkyEQruqjJOBt18MgIS1jS7FohOEKknDtk3PWGO9moZ8TaztH92reAPdjtTp2Abl6AVAfzSdRuRRyY2oaaY5ea8Sz3n3Wn8kenSvrYtiDvOjYWsMvA.Rl-TKzuVJzxIvAjL.gQXSKR82zNZed2E9npljsrEwkioHRtUSv9YhHyRGq2hpQy9uOMnxWkEcoi2aLwxzufmXPt9aiNp8PL13xUC6ySvhsblS0huUWOn_6Z8Hc_BUdqi3cqwnlxKoTS76DKHp8L05RglvlHSyXE61J-qttlbD6nCEalNaLhOwiEf4nxpwU6oeJWlqHSp3LE6XCvqfS9AwCRtR_dC95vKDrDE06okSAVB_OV1Y3RF1oOBGQpQws1MsDcFeKPxPET9gaNAkSkz8w60tXNArtQkcgMcb3q_b- _B5XLJXztbTCp-UPDxSp5BIIvUT4RU5n4Vz5MS6MQNuftsx4hp0b__RVHRo3lyM087Jy6Fv2UqA2jcOvINddug.Z5mFrYSl_PA711np_Do0Aw|
|TIPO DATI INPUT||
|Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio||
|Caso KO 401 <br> Unauthorized <br> Jwt mancante o non valido||
|Caso OK 200 <br>Output Atteso|{<br>"username": "Codice fiscale",<br>"password": "Password utente", <br>"userSso": false, <br>"ndgCode": "code external",<br>"phoneNumber": "phone number con il prefisso +39",<br>"email": "Emal",<br>"surname": "Cognome",<br>"name": "Nome",<br>"birthDate":"dd/mm/aaaa",<br>"privacy": true,<br>"commerce":false,<br>"trace":false,<br>“externalinfo”: "informazioni aggiuntive"<br>}|

|API| HOST : 900|
|---|---|
|Protocollo| gRPC |
|Tipo| |
|TIPO DATI INPUT||
|Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio| {<br>"token": "eyJjdHkiOiJKV1QiLCJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIn0.H6JuKk11gJFijifm7KVEBaYiWmz1vBOtH5dCIY2T2hgebGvP6dQw_7lzhXUEitlwP-f26s1NwAwRGbBhImr8S1Mufw49FKkz54UKGViFfX-TCJOg-xgsHpxkH9_pnPEiXzsDNlbRTVcP2m1WDyq9sQei8J2BUKj3F0eGGQQqLLNw3Rbgi7QouPvV1bNSOyplSt0CusRFVsimpz9hm_A_ _B5XLJXztbTCp-UPDxSp5BIIvUT4RU5n4Vz5MS6MQNuftsx4hp0b__RVHRo3lyM087Jy6Fv2UqA2jcOvINddug.Z5mFrYSl_PA711np_Do0Aw"<br>}|
|Caso KO 401 <br> Unauthorized <br> Jwt mancante o non valido|{<br>“response”: {<br>“statusNumber”: 401,<br>            “errorMessage”: null<br>}<br>}|
|Caso OK 200 Output Atteso||
    {
    “response”: {
                    “statusNumber”: 200,
                “errorMessage”: null


        }
        “credential”:{
                            "username": "Codice fiscale",
                            "password": "Password utente", 
                            "userSso": false, 
                            "ndgCode": "code external",
                            "phoneNumber": "phone number con il prefisso +39",
                            "email": "Emal",
                            "surname": "Cognome",
                            "name": "Nome",
                            "birthDate":"dd/mm/aaaa",
                            "privacy": true,
                            "commerce":false,
                            "trace":false,
                            “externalinfo”: "informazioni aggiuntive"
                            }
    }

---

## **4.** **Logout (REST)**
La seguente api può essere utilizzata per invalidare il token inviato nell’header Authorizzation.
Per accedere all’api è necessario quindi avere un token valido.

|API| host/v1/iam/logout|
|---|---|
|Protocollo| https |
|Tipo| GET |
|Campi Header <br> **Grassetto = campo** <br>Obbligatorio|**Authorization: Bearer** <br> eyJjdHkiOiJKV1QiLCJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIn0.H6JuKk11gJFijifm7KVEBaYiWmz1vBOtH5dCIY2T2hgebGvP6dQw_7lzhXUEitlwP-f26s1NwAwRGbBhImr8S1Mufw49FKkz54UKGViFfX-TCJOg-xgsHpxkH9_pnPEiXzsDNlbRTVcP2m1WDyq9sQei8J2BUKj3F0eGGQQqLLNw3Rbgi7QouPvV1bNSOyplSt0CusRFVsimpz9hm_A_VTjbOY0t0sGkCrZzgkyEQruqjJOBt18MgIS1jS7FohOEKknDtk3PWGO9moZ8TaztH92reAPdjtTp2Abl6AVAfzSdRuRRyY2oaaY5ea8Sz3n3Wn8kenSvrYtiDvOjYWsMvA.Rl-TKzuVJzxIvAjL.gQXSKR82zNZed2E9npljsrEwkioHRtUSv9YhHyRGq2hpQy9uOMnxWkEcoi2aLwxzufmXPt9aiNp8PL13xUC6ySvhsblS0huUWOn_6Z8Hc_BUdqi3cqwnlxKoTS76DKHp8L05RglvlHSyXE61J-qttlbD6nCEalNaLhOwiEf4nxpwU6oeJWlqHSp3LE6XCvqfS9AwCRtR_dC95vKDrDE06okSAVB_OV1Y3RF1oOBGQpQws1MsDcFeKPxPET9gaNAkSkz8w60tXNArtQkcgMcb3q_b- _B5XLJXztbTCp-UPDxSp5BIIvUT4RU5n4Vz5MS6MQNuftsx4hp0b__RVHRo3lyM087Jy6Fv2UqA2jcOvINddug.Z5mFrYSl_PA711np_Do0Aw|
|TIPO DATI INPUT||
|Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio||
|Caso KO 401 <br> Unauthorized <br> Jwt mancante o non valido| |
|Caso OK 200 Output Atteso||

---

## **5.** **Refresh (REST e GRPC)**
La seguente api può essere richiamata con un access token anche scaduto per generarne uno nuovo.

Ha esito positivo solo se vi è un refresh token ancora valido associato all’access token inviato.
Il refresh token ha una validità (che può essere modificata in base al tenant) e viene eliminato quando si effettua un logout.

|API| host/v1/iam/refresh|
|---|---|
|Protocollo| https |
|Tipo| GET |
|Campi Header <br> **Grassetto = campo** <br>Obbligatorio|**Authorization: Bearer** <br> eyJjdHkiOiJKV1QiLCJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIn0.H6JuKk11gJFijifm7KVEBaYiWmz1vBOtH5dCIY2T2hgebGvP6dQw_7lzhXUEitlwP-f26s1NwAwRGbBhImr8S1Mufw49FKkz54UKGViFfX-TCJOg-xgsHpxkH9_pnPEiXzsDNlbRTVcP2m1WDyq9sQei8J2BUKj3F0eGGQQqLLNw3Rbgi7QouPvV1bNSOyplSt0CusRFVsimpz9hm_A_VTjbOY0t0sGkCrZzgkyEQruqjJOBt18MgIS1jS7FohOEKknDtk3PWGO9moZ8TaztH92reAPdjtTp2Abl6AVAfzSdRuRRyY2oaaY5ea8Sz3n3Wn8kenSvrYtiDvOjYWsMvA.Rl- _dC95vKDrDE06okSAVB_OV1Y3RF1oOBGQpQws1MsDcFeKPxPET9gaNAkSkz8w60tXNArtQkcgMcb3q_b- _B5XLJXztbTCp-UPDxSp5BIIvUT4RU5n4Vz5MS6MQNuftsx4hp0b__RVHRo3lyM087Jy6Fv2UqA2jcOvINddug.Z5mFrYSl_PA711np_Do0Aw|
|TIPO DATI INPUT||
|Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio||
|Caso KO 401 <br> Unauthorized <br> Jwt mancante o non valido| |
|Caso OK 200 Output Atteso|{<br>"token": "eyJjdHkiOiJKV1QiLCJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIn0.H6JuKk11gJFijifm7KVEBaYiWmz1vBOtH5dCIY2T2hgebGvP6dQw_7lzhXUEitlwP-f26s1NwAwRGbBhImr8S1Mufw49FKkz54UKGViFfX-TCJOg-xgsHpxkH9_pnPEiXzsDNlbRTVcP2m1WDyq9sQei8J2BUKj3F0eGGQQqLLNw3Rbgi7QouPvV1bNSOyplSt0CusRFVsimpz9hm_A_ _B5XLJXztbTCp-UPDxSp5BIIvUT4RU5n4Vz5MS6MQNuftsx4hp0b__RVHRo3lyM087Jy6Fv2UqA2jcOvINddug.Z5mFrYSl_PA711np_Do0Aw" <br>}|

|API| HOST : 9000|
|---|---|
|Protocollo| gRPC |
|Tipo| GET |
|Campi Header <br> **Grassetto = campo** <br>Obbligatorio| |
|TIPO DATI INPUT||
|Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio|{<br>"token": "eyJjdHkiOiJKV1QiLCJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIn0.H6JuKk11gJFijifm7KVEBaYiWmz1vBOtH5dCIY2T2hgebGvP6dQw_7lzhXUEitlwP-f26s1NwAwRGbBhImr8S1Mufw49FKkz54UKGViFfX-TCJOg-xgsHpxkH9_pnPEiXzsDNlbRTVcP2m1WDyq9sQei8J2BUKj3F0eGGQQqLLNw3Rbgi7QouPvV1bNSOyplSt0CusRFVsimpz9hm_A_ _B5XLJXztbTCp-UPDxSp5BIIvUT4RU5n4Vz5MS6MQNuftsx4hp0b__RVHRo3lyM087Jy6Fv2UqA2jcOvINddug.Z5mFrYSl_PA711np_Do0Aw"<br>}|
|Caso KO 401 <br> Unauthorized <br> Jwt mancante o non valido|{<br>“response”: {<br>“statusNumber”: 401,<br>         “errorMessage”: null<br>},<br>“token”: null<br>}|
|Caso OK 200 Output Atteso||
        {
            “response”: {
                    “statusNumber”: 200,
                “errorMessage”: null
                    },
        “token”: “eyJjdHkiOiJKV1QiLCJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIn0.H6JuKk11gJFijifm7KVEBaYiWmz1vBOtH5dCIY2T2hgebGvP6dQw_7lzhXUEitlwP-f26s1NwAwRGbBhImr8S1Mufw49FKkz54UKGViFfX-TCJOg-xgsHpxkH9_pnPEiXzsDNlbRTVcP2m1WDyq9sQei8J2BUKj3F0eGGQQqLLNw3Rbgi7QouPvV1bNSOyplSt0CusRFVsimpz9hm_A_ _B5XLJXztbTCp-UPDxSp5BIIvUT4RU5n4Vz5MS6MQNuftsx4hp0b__RVHRo3lyM087Jy6Fv2UqA2jcOvINddug.Z5mFrYSl_PA711np_Do0Aw"   }


---

## **6.** **Update (REST e GRPC)**
La seguente api (Rest/gRPC) può essere utilizzata per modificare gli attributi salvati sullo iam.
Può essere richiamata solo con un access token valido dell’utente la quale si vuole cambiare gli attributi.

|API| host/v1/iam/update|
|---|---|
|Protocollo| https |
|Tipo| PUT |
|Campi Header <br> **Grassetto = campo** <br>Obbligatorio|**Authorization: Bearer** <br> eyJjdHkiOiJKV1QiLCJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIn0.H6JuKk11gJFijifm7KVEBaYiWmz1vBOtH5dCIY2T2hgebGvP6dQw_7lzhXUEitlwP-f26s1NwAwRGbBhImr8S1Mufw49FKkz54UKGViFfX-TCJOg-xgsHpxkH9_pnPEiXzsDNlbRTVcP2m1WDyq9sQei8J2BUKj3F0eGGQQqLLNw3Rbgi7QouPvV1bNSOyplSt0CusRFVsimpz9hm_A_ _jRfDO5zO_-urxUN1c4a9LuaY1trkcFmbT2oHHWADn7WTmonoh3LeGWdU7rMvJEtBbyNhNqW6Q5hm4- _B5XLJXztbTCp-UPDxSp5BIIvUT4RU5n4Vz5MS6MQNuftsx4hp0b__RVHRo3lyM087Jy6Fv2UqA2jcOvINddug.Z5mFrYSl_PA711np_Do0Aw|
|TIPO DATI INPUT||
|Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio|{<br>"ndgCode": "code external",<br> "phoneNumber": "phone number con il prefisso +39",<br>"surname": "Cognome",<br>"name": "Nome",<br>"birthDate":"dd/mm/aaaa",<br>"privacy": true,<br>"commerce":false,<br>"trace":false,<br>“externalinfo”: "informazioni aggiuntive"<br>}|
|Caso OK | |
|Caso KO Rest <br>Jwt non valido o non presente - 401||


|API| HOST : 9000|
|---|---|
|Protocollo| gRPC |
|Tipo|  |
|Campi Header <br> **Grassetto = campo** <br>Obbligatorio||
|TIPO DATI INPUT||
|Caso OK|{<br>“statusNumber”: 200,<br>“errorMessage”: null<br>}|
|Caso KO Rest <br>Jwt non valido o non presente - 401|{<br>“statusNumber”: 401,<br>“errorMessage”: null<br>}|
|Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio||
    {
        “token”: “eyJjdHkiOiJKV1QiLCJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIn0.H6JuKk11gJFijifm7KVEBaYiWmz1vBOtH5dCIY2T2hgebGvP6dQw_7lzhXUEitlwP-f26s1NwAwRGbBhImr8S1Mufw49FKkz54UKGViFfX-TCJOg-xgsHpxkH9_pnPEiXzsDNlbRTVcP2m1WDyq9sQei8J2BUKj3F0eGGQQqLLNw3Rbgi7QouPvV1bNSOyplSt0CusRFVsimpz9hm_A_ _jRfDO5zO_-urxUN1c4a9LuaY1trkcFmbT2oHHWADn7WTmonoh3LeGWdU7rMvJEtBbyNhNqW6Q5hm4- _B5XLJXztbTCp-UPDxSp5BIIvUT4RU5n4Vz5MS6MQNuftsx4hp0b__RVHRo3lyM087Jy6Fv2UqA2jcOvINddug.Z5mFrYSl_PA711np_Do0Aw”,

        “credential”:{
                            "username": "Codice fiscale",
                            "password": "Password utente", 
                            "userSso": true, 
                            "ndgCode": "code external",
                            "phoneNumber": "phone number con il prefisso +39",
                            "email": "Emal",
                            "surname": "Cognome",
                            "name": "Nome",
                            "birthDate":"dd/mm/aaaa",
                            "privacy": true,
                            "commerce":false,
                            "trace":false,
                            “externalinfo”: "informazioni aggiuntive"
                            }
    }


---
## **7.** **Forgot Password**
La seguente api (Rest) può essere utilizzata per avviare il flusso di recupero password.
Se invocata con un username valido invierà una mail all’utente con link o codice per poter effettuare il reset password.

| API                                                            | host/v1/iam/ forgotpassword/{username}|
|----------------------------------------------------------------|---|
| Protocollo                                                     | https |
| Tipo                                                           | GET |
| Campi Header <br> **Grassetto = campo** <br>Obbligatorio       ||
| TIPO DATI INPUT                                                ||
| Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio ||
| Caso OK <br>403 Forbidden                                      | |
| Caso KO                                                        | |

---

## **8.** **Reset Password**

La seguente api (Rest) può essere utilizzata per resettare una password avendo un codice ricevuto tramite la precedente api.
Se invocata con un username, password, e codice validi aggiornerà la password dell’utente.

| API                                                            | host/v1/iam/resetpassword      |      
|----------------------------------------------------------------|--------------------------------------|
| Protocollo                                                     | https                                |
| Tipo                                                           | POST                                 |
| Campi Header <br> **Grassetto = campo** <br>Obbligatorio       |
| TIPO DATI INPUT                                                ||
| Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio | {<br>**"username"**: "sclcld93a13d1622s",<br>**"code"**:"433133",<br>**"password"**: "New Password" #1<br>} |||
| Caso OK <br>200                                                |                                      |
| Caso KO <br>400                                                ||

**#1 Deve essere almeno 8 caratteri, con almeno un numero, un carattere speciale, un carattere maiuscolo      e un carattere minuscolo.**

---

## **9.** **SSO registrazione**
La seguente chiamata gRPC può essere utilizzata per registrare un utente, tramite SSO sullo IAM Cognito. In input non deve essere passata la psw, viene settata direttamente una psw admin.
Una volta registrato l’utente, viene automaticamente fatta la login e staccato un token restituito poi all’interno della response.

|API| HOST : 9000|
|---|---|
|Protocollo| gRPC |
|Tipo| POST |
|Campi Header <br> **Grassetto = campo** <br>Obbligatorio||
|TIPO DATI INPUT||
|Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio| {<br>  "username": "Codice fiscale",<br>"userSso": true, <br> "ndgCode": "code external",<br>"phoneNumber": "phone number con il prefisso +39",<br>"email": "Emal",<br>"surname": "Cognome",<br>"name": "Nome",<br>  "birthDate":"dd/mm/aaaa",<br>"privacy": true,<br>"commerce":false,<br>  "trace":false,<br>“externalinfo”: "informazioni aggiuntive"<br>}|
|Caso KO Rest<br> Username già registrato - 400|{<br>   “response”: {<br>“statusNumber”: 400,<br>           “errorMessage”: null<br>},<br>“token”: null<br>}|
|Caso OK | |
    {
        “response”: {
                    “statusNumber”: 200,
                “errorMessage”: null
                    },
        “token”: “eyJjdHkiOiJKV1QiLCJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIn0.H6JuKk11gJFijifm7KVEBaYiWmz1vBOtH5dCIY2T2hgebGvP6dQw_7lzhXUEitlwP-f26s1NwAwRGbBhImr8S1Mufw49FKkz54UKGViFfX-TCJOg-xgsHpxkH9_pnPEiXzsDNlbRTVcP2m1WDyq9sQei8J2BUKj3F0eGGQQqLLNw3Rbgi7QouPvV1bNSOyplSt0CusRFVsimpz9hm_A_ _B5XLJXztbTCp-UPDxSp5BIIvUT4RU5n4Vz5MS6MQNuftsx4hp0b__RVHRo3lyM087Jy6Fv2UqA2jcOvINddug.Z5mFrYSl_PA711np_Do0Aw"   
    }
---

## **10.** **Confirm SignUp**

La seguente api può essere utilizzata per confermare la registrazione, passando come pathparam username e codice di conferma.

| API                                                            | host/v1/iam/confirmsignup/{username}/{code}    |                                                             
|----------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------|
| Protocollo                                                     | https                                                                                                       |
| Tipo                                                           | GET                                                                                                         |
| Campi Header <br> **Grassetto = campo** <br>Obbligatorio       |
| TIPO DATI INPUT                                                ||
| Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio |
| Caso OK <br>200                                                |                                                                                                             |
| Caso KO <br>400                                                |                                                                                                             |

---

## **11.** **GetAll Users**

La seguente api può essere utilizzata per recuperare la lista di utenti, passando nell'header il parametro apikey. 
La lista viene restituita in formato excel.

| API                                                            | host/v1/iam/alluser   |                                                             
|----------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------|
| Protocollo                                                     | https                                                                                                       |
| Tipo                                                           | GET                                                                                                         |
| Campi Header <br> **Grassetto = campo** <br>Obbligatorio       |**"apiKey"**|
| TIPO DATI INPUT                                                ||
| Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio |
| Caso OK <br>200                                                |                                                                                                             |
| Caso KO <br>401 <br> apikey not valid                          |                                                                                                             |

---

## **12.** **AddImportedUserInToGroup**

La seguente api può essere utilizzata per aggiungere una lista di utenti ad un gruppo. Deve essere passato
nell'header il parametro apikey.

| API                                                            | host/v1/iam/addImportedUserInToGroup          |
|----------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------|
| Protocollo                                                     | https                                                                                                       |
| Tipo                                                           | GET                                                                                                         |
| Campi Header <br> **Grassetto = campo** <br>Obbligatorio       |**"apiKey"**|
| TIPO DATI INPUT                                                ||
| Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio |
| Caso OK <br>200                                                |                                                                                                             |
| Caso KO <br>401 <br> apikey not valid                          |                                                                                                             |

---

## **13.** **LoginTechnicalUser**

La seguente api può essere utilizzata per effettuare una login tecnica. All'interno dell'header deve essere passato il
sitekey (token captcha).

| API                                                            | host/v1/iam/login/technicaluser |
|----------------------------------------------------------------|---------------------------------|
| Protocollo                                                     | https                           |
| Tipo                                                           | POST                            |
| Campi Header <br> **Grassetto = campo** <br>Obbligatorio       | **"sitekey"**: "captcha token"  |                  
| TIPO DATI INPUT                                                ||
| Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio |
| Caso OK <br>200                                                |{<br>"token": "eyJjdHkiOiJKV1QiLCJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIn0.H6JuKk11gJFijifm7KVEBaYiWmz1vBOtH5dCIY2T2hgebGvP6dQw_7lzhXUEitlwP-f26s1NwAwRGbBhImr8S1Mufw49FKkz54UKGViFfX-TCJOg-xgsHpxkH9_pnPEiXzsDNlbRTVcP2m1WDyq9sQei8J2BUKj3F0eGGQQqLLNw3Rbgi7QouPvV1bNSOyplSt0CusRFVsimpz9hm_A_ _B5XLJXztbTCp-UPDxSp5BIIvUT4RU5n4Vz5MS6MQNuftsx4hp0b__RVHRo3lyM087Jy6Fv2UqA2jcOvINddug.Z5mFrYSl_PA711np_Do0Aw"<br>} |
| Caso KO <br>401 <br> sitekey not valid                         |                                 |
| Caso KO <br>403 <br> Forbidden                                 |                                 |

---

## **14.** **DeleteUsers**

La seguente api può essere utilizzata per eliminare utenti il cui user status è "RESET_REQUIRED".

| API                                                            | host/v1/iam/delete |
|----------------------------------------------------------------|---------------------------------|
| Protocollo                                                     | https                           |
| Tipo                                                           | DELETE                            |
| Campi Header <br> **Grassetto = campo** <br>Obbligatorio       | **"apiKey"**:                    |
| TIPO DATI INPUT                                                ||
| Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio |
| Caso OK <br>200                                                ||
| Caso KO <br>401 <br> apiKey not valid                          |                                 |

---

## **15.** **Intermediaryregistration**

La seguente api può essere utilizzata per effetuare una registrazione intermediari.

| API                                                            | host/v1/iam/intermediaryregistration |
|----------------------------------------------------------------|---------------------------------|
| Protocollo                                                     | https                           |
| Tipo                                                           | POST                            |
| Campi Header <br> **Grassetto = campo** <br>Obbligatorio       |
| TIPO DATI INPUT                                                ||
| Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio |{<br>**"username"**: "Codice fiscale",<br>**"password"**: "Password utente", #1<br>"userSso": false, #2 <br>"ndgCode": "code external",<br>"phoneNumber": "phone number con il prefisso +39",<br>**"email"**: "Email",<br>"surname": "Cognome",<br>"name": "Nome",<br>"birthDate":"dd-mm-aaaa", #3<br>"privacy": true,<br>"commerce":false,<br>"trace":false,<br>“externalinfo”: informazioni aggiuntive"<br>} |
| Caso OK <br>201 <br> Created                                   ||
| Caso KO <br>401                                                |                                 |
| Caso KO <br>409 <br> UsernameExists                            |                                 |

---

## **16.** **ForceChangePassword**

La seguente api può essere utilizzato effettuare il cambio forzato della password.

| API                                                            | host/v1/iam/forcechangepassword              |                                                 
|----------------------------------------------------------------|-----------------------------------------------------------------------------------------------|
| Protocollo                                                     | https                                                                                         |
| Tipo                                                           | POST                                                                                          |
| Campi Header <br> **Grassetto = campo** <br>Obbligatorio       |
| TIPO DATI INPUT                                                ||
| Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio | {<br>**"session"**: "String",<br>**"newPassword"**:"String",<br>**"username"**: "String"<br>} |
| Caso OK <br>200                                    ||
| Caso KO <br>401                                                |                                                                                               |

---

## **17.** **Changepassword**

La seguente api può essere utilizzata per effettuare il cambio della password, passando in input la vecchia e la nuova psw.

| API                                                            | host/v1/iam/changepassword              |                                     
|----------------------------------------------------------------|------------------------------------------------------------------------------|
| Protocollo                                                     | https                                                                        |
| Tipo                                                           | POST                                                                         |
| Campi Header <br> **Grassetto = campo** <br>Obbligatorio       |**Authorization: Bearer** <br> eyJjdHkiOiJKV1QiLCJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIn0.H6JuKk11gJFijifm7KVEBaYiWmz1vBOtH5dCIY2T2hgebGvP6dQw_7lzhXUEitlwP-f26s1NwAwRGbBhImr8S1Mufw49FKkz54UKGViFfX-TCJOg-xgsHpxkH9_pnPEiXzsDNlbRTVcP2m1WDyq9sQei8J2BUKj3F0eGGQQqLLNw3Rbgi7QouPvV1bNSOyplSt0CusRFVsimpz9hm_A_VTjbOY0t0sGkCrZzgkyEQruqjJOBt18MgIS1jS7FohOEKknDtk3PWGO9moZ8TaztH92reAPdjtTp2Abl6AVAfzSdRuRRyY2oaaY5ea8Sz3n3Wn8kenSvrYtiDvOjYWsMvA.Rl-TKzuVJzxIvAjL.gQXSKR82zNZed2E9npljsrEwkioHRtUSv9YhHyRGq2hpQy9uOMnxWkEcoi2aLwxzufmXPt9aiNp8PL13xUC6ySvhsblS0huUWOn_6Z8Hc_BUdqi3cqwnlxKoTS76DKHp8L05RglvlHSyXE61J-qttlbD6nCEalNaLhOwiEf4nxpwU6oeJWlqHSp3LE6XCvqfS9AwCRtR_dC95vKDrDE06okSAVB_OV1Y3RF1oOBGQpQws1MsDcFeKPxPET9gaNAkSkz8w60tXNArtQkcgMcb3q_b- _B5XLJXztbTCp-UPDxSp5BIIvUT4RU5n4Vz5MS6MQNuftsx4hp0b__RVHRo3lyM087Jy6Fv2UqA2jcOvINddug.Z5mFrYSl_PA711np_Do0Aw|
| TIPO DATI INPUT                                                ||
| Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio | {<br>**"oldPassword"**: "String",<br>**"newPassword"**:"String"<br>} |
| Caso OK <br>200                                                ||
| Caso KO <br>401 Unauthorized                                   |
| Caso KO <br>400 Password not changed                           |

---

## **18.** **VerifyCaptcha**

Api usata per verificare la validità del token capthca.

| API                                                            | host/v1/captcha                                  |                                     
|----------------------------------------------------------------|--------------------------------------------------|
| Protocollo                                                     | https                                            |
| Tipo                                                           | POST                                             |
| Campi Header <br> **Grassetto = campo** <br>Obbligatorio       ||
| TIPO DATI INPUT                                                ||
| Body as Json rest <br> **Grassetto = campo** <br> Obbligatorio | {<br>**"token"**: "Token captcha"}               |
| Caso OK <br>200                                                | {<br>"success": "true",<br>"error-codes":"error"} |
| Caso KO <br>401 Unauthorized                                   |


---