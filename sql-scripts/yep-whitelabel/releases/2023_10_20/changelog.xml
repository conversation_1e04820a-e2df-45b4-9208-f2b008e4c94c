<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
	 <changeSet author="iad-policy"
	           id="1">
		<sqlFile dbms="postgresql"
		         endDelimiter=";"
		         path="initializeDBPolicy.sql"
		         relativeToChangelogFile="true"
		         splitStatements="true"
		         stripComments="false"/>
		<rollback>
			<sqlFile dbms="postgresql"
			         endDelimiter=";"
			         path="initializeDBPolicy-rollback.sql"
			         relativeToChangelogFile="true"
			         splitStatements="true"
			         stripComments="false"/>
		</rollback>
	</changeSet>
</databaseChangeLog>
