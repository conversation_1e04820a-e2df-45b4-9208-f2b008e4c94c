-- "catalog".brokers definition

-- Drop table

-- DROP TABLE "catalog".brokers;

CREATE TABLE "catalog".brokers (
	id serial4 NOT NULL,
	"name" varchar NOT NULL,
	identitycode varchar NOT NULL,
	description varchar NULL,
	CONSTRAINT brokers_identitycode_key UNIQUE (identitycode),
	CONSTRAINT brokers_pkey PRIMARY KEY (id)
);


-- "catalog".companies definition

-- Drop table

-- DROP TABLE "catalog".companies;

CREATE TABLE "catalog".companies (
	id serial4 NOT NULL,
	"name" varchar NOT NULL,
	description varchar NULL,
	CONSTRAINT companies_pkey PRIMARY KEY (id)
);


-- "catalog".company_broker definition

-- Drop table

-- DROP TABLE "catalog".company_broker;

CREATE TABLE "catalog".company_broker (
	id serial4 NOT NULL,
	companyid int4 NOT NULL,
	brokerid int4 NOT NULL,
	CONSTRAINT company_broker_pkey PRIMARY KEY (id),
	CONSTRAINT fk_broker FOREIG<PERSON>EY (brokerid) REFERENCES "catalog".brokers(id) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT fk_company FOREIGN KEY (companyid) REFERENCES "catalog".companies(id) ON DELETE CASCADE ON UPDATE CASCADE
);