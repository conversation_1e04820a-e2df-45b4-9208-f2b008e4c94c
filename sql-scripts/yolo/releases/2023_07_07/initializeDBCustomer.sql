CREATE SCHEMA "customer";

-- customer.customers definition

-- Drop table

-- DROP TABLE customer.customers;

CREATE TABLE customer.customers (
	id int4 NOT NULL GENERATED BY DEFAULT AS IDENTITY,
	customer_code varchar NULL,
	external_code jsonb NULL,
	username varchar NULL,
	"name" varchar <PERSON>,
	surname varchar NULL,
	date_of_birth varchar NULL,
	birth_city varchar NULL,
	birth_country varchar NULL,
	birth_state varchar NULL,
	tax_code varchar NOT NULL,
	gender varchar NULL,
	street varchar NULL,
	street_number varchar NULL,
	city varchar NULL,
	country varchar NULL,
	country_id int4 NULL,
	zip_code varchar NULL,
	state varchar NULL,
	state_id int4 NULL,
	primary_mail varchar NULL,
	secondary_mail varchar NULL,
	primary_phone varchar NULL,
	secondary_phone varchar NULL,
	"language" varchar NULL,
	legal_form varchar NULL,
	education varchar NULL,
	salary varchar NULL,
	profession varchar NULL,
	created_at timestamp NOT NULL DEFAULT now(),
	updated_at timestamp NOT NULL DEFAULT now(),
	ndg varchar NULL,
	city_id int4 NULL,
	birth_state_id int4 NULL,
	birth_city_id int4 NULL,
	birth_country_id int4 NULL,
	state_abbr varchar NULL,
	birth_state_abbr varchar NULL,
	id_card varchar NULL,
	registration_info jsonb NULL DEFAULT '{}'::jsonb,
	retired_code varchar NULL,
	CONSTRAINT customers_pkey PRIMARY KEY (id)
);

-- customer.flags definition

-- Drop table

-- DROP TABLE customer.flags;

CREATE TABLE customer.flags (
	id serial4 NOT NULL,
	description varchar NOT NULL,
	"position" int4 NULL DEFAULT 0,
	created_at timestamp NOT NULL,
	updated_at timestamp NULL,
	deleted_at timestamp NULL,
	tag varchar NULL,
	kind varchar NULL,
	mandatory bool NULL DEFAULT false,
	CONSTRAINT flags_pkey PRIMARY KEY (id)
);

-- customer.user_acceptances definition

-- Drop table

-- DROP TABLE customer.user_acceptances;

CREATE TABLE customer.user_acceptances (
	id serial4 NOT NULL,
	user_id int4 NOT NULL,
	flag_id int4 NOT NULL,
	value bool NULL,
	created_at timestamp NOT NULL DEFAULT now(),
	updated_at timestamp NULL,
	deleted_at timestamp NULL,
	CONSTRAINT user_acceptances_pkey PRIMARY KEY (id)
);

INSERT INTO customer.flags
(id, description, "position", created_at, updated_at, deleted_at, tag, kind, mandatory)
VALUES(4, 'attività di marketing diretto, offerte commerciali relative ai propri prodotti e/o servizi ovvero prodotti e servizi della Società di YOLO Group Srl, attività promozionali ed iniziative (es. eventi, concorsi a premi, manifestazioni, etc), ricerche ed analisi statistiche, impiegando forme "tradizionali" ovvero "automatizzate" di contatto (art. 130 c. 1 e 2 del D.lgs. 196/03 e s.m.i).', 4, '2021-02-22 09:49:33.851', '2021-03-16 15:10:15.529', '2021-03-16 15:10:15.528', 'promotional_activities', 'privacy', false);
INSERT INTO customer.flags
(id, description, "position", created_at, updated_at, deleted_at, tag, kind, mandatory)
VALUES(5, 'Attività di comunicazione personalizzata sulla base dei dati relativi alla posizione e al percorso raccolti, ad esempio sotto forma di segnali GPS, abbinati a un timestamp, e di altri dati inviati dal dispositivo mobile su cui è installata e attivata l''Applicazione YOLO.', 5, '2021-02-22 09:50:44.985', '2021-03-16 15:10:17.989', '2021-03-16 15:10:17.988', 'geolocalization', 'privacy', false);
INSERT INTO customer.flags
(id, description, "position", created_at, updated_at, deleted_at, tag, kind, mandatory)
VALUES(1, 'Acconsento al trattamento dei miei dati personali per l''invio di comunicazioni promozionali e materiale pubblicitario, l''offerta di prodotti e/o servizi propri o di terzi, il compimento di ricerche di mercato, anche mediante l''uso di telefono con operatore e/o mediante sistemi automatizzati (es.: email)', 6, '2021-02-22 09:40:15.920', '2021-02-22 14:22:25.434', '2021-02-22 13:59:07.181', 'profiling_2', 'privacy', false);
INSERT INTO customer.flags
(id, description, "position", created_at, updated_at, deleted_at, tag, kind, mandatory)
VALUES(6, 'Acconsento al trattamento dei miei dati personali per l''invio di comunicazioni promozionali e materiale pubblicitario, l''offerta di prodotti e/o servizi propri o di terzi, il compimento di ricerche di mercato, anche mediante l''uso di telefono con operatore e/o mediante sistemi automatizzati (es.: email)', 3, '2021-02-22 14:22:21.230', '2021-03-16 15:10:43.187', '2021-03-16 15:10:11.941', 'profiling_2', 'privacy', false);
INSERT INTO customer.flags
(id, description, "position", created_at, updated_at, deleted_at, tag, kind, mandatory)
VALUES(3, 'Do il consenso liberamente all’invio di materiale pubblicitario, vendita diretta, compimento di ricerche di mercato e comunicazione commerciale, con riguardo all’attività di brokeraggio di TIM MY BROKER e/o all’offerta di prodotti e servizi di terze parti, con modalità automatizzate di contatto (come sms, mms, fax, fonie, e-mail ed applicazioni web) e tradizionali (come telefonate con operatore, posta cartacea)', 1, '2021-02-22 09:46:50.219', '2021-03-23 10:54:58.909', NULL, 'third_parts_comunication', 'privacy', false);
INSERT INTO customer.flags
(id, description, "position", created_at, updated_at, deleted_at, tag, kind, mandatory)
VALUES(2, 'Do il consenso liberamente all’identificazione, anche mediante elaborazioni elettroniche, dei miei comportamenti ed abitudini di consumo in modo da migliorare i servizi forniti, soddisfare le specifiche esigenze ed indirizzare le proposte commerciali di interesse', 2, '2021-02-22 09:44:48.779', '2021-03-23 10:55:11.384', NULL, 'profiling', 'privacy', false);

INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code)
VALUES(50, 'LQR3F', NULL, 'TCHUSR29C23D122S', 'Rullo', 'DiTamburi', '02-06-1990', 'Napoli', 'Italia', 'Napoli', 'RULDIT88M30C129C', 'Male', 'Via casa asd', NULL, 'Napoli', 'Italia', 110, '80060', 'Napoli', 3675, '<EMAIL>', NULL, '+393333333331', NULL, NULL, NULL, NULL, NULL, NULL, '2023-06-23 09:29:31.583', '2023-06-26 20:28:17.207', 'TCHUSR29C23D122S', 7810, 3675, 31, 110, 'NA', NULL, NULL, NULL, NULL);


COMMIT;
