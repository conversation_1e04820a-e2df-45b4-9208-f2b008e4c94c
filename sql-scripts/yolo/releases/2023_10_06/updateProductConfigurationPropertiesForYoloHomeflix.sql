UPDATE product."configuration" SET properties = '{"withdraw": {"days": 14, "state": "PendingWithdraw", "callPg": false, "withdrawable": "true"}, "properties": [{"name": "claimType", "value": "external"}, {"name": "language", "value": "xtl"}, {"name": "ClaimCompanyMessageKey", "value": "SINISTRO_POLIZZA", "placeHolder": ["nome", "cognome", "contactMail"]}, {"name": "claimProvider", "value": "MotionsCloud"}, {"name": "claimTypeCompany", "value": "digital"}, {"name": "ClaimUserMessageKey", "value": "APERTURA_CLAIM_UTENTE"}, {"name": "WithdrawUserMessageKey", "value": "RECESSO_POLIZZA", "placeHolder": ["nome", "cognome", "contactMail", "productName"]}, {"name": "WithdrawCompanyMessageKey", "value": "RECESSO_POLIZZA_COMPAGNIA", "placeHolder": ["nome", "cognome", "contactMail", "productName"]}, {"name": "DeactivateUserMessageKey", "value": "DISATTIVAZIONE_POLIZZA", "placeHolder": ["nome", "productName", "dataRinnovo"]}, {"name": "messageType", "value": "html"}, {"name": "companyMail", "value": "<EMAIL>"}, {"name": "withdrawCompanyMail", "value": "<EMAIL>"}, {"name": "DeactivationProvider", "value": "braintree"}], "contactMail": "<EMAIL>"}' WHERE id = 1;