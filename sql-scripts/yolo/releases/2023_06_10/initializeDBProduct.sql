CREATE SCHEMA "product";


-- product.product_images definition

-- Drop table

-- DROP TABLE product.product_images;

CREATE TABLE product.product_images (
	id serial4 NOT NULL,
	images jsonb NULL,
	CONSTRAINT product_images_pkey PRIMARY KEY (id)
);


-- product.products definition

 

-- Drop table

 

-- DROP TABLE product.products;

 

CREATE TABLE product.products (
    id serial4 NOT NULL,
    product_code varchar NOT NULL,
    product_description varchar NULL,
    start_date date NULL,
    end_date date NULL,
    recurring bool NOT NULL DEFAULT false,
    external_id varchar NULL,
    insurance_premium numeric(10, 4) NULL,
    insurance_company varchar NULL,
    insurance_company_logo varchar NULL,
    business varchar NULL,
    title_prod varchar NULL,
    short_description varchar NULL,
    description varchar NULL,
    conditions varchar NULL,
    information_package varchar NULL,
    conditions_package varchar NULL,
    display_price varchar NULL,
    price numeric(10, 4) NULL,
    only_contractor bool NULL,
    maximum_insurable numeric NULL,
    can_open_claim bool NULL,
    holder_maximum_age numeric NULL,
    holder_minimum_age numeric NULL,
    show_in_dashboard bool NULL,
    product_image_id int4 NOT NULL,
    catalog_id int4 NULL,
    properties jsonb NULL,
    quotator_type varchar NULL,
    show_addons_in_shopping_cart bool NULL,
    thumbnail bool NULL,
    privacy_documentation_link varchar NULL,
    informative_set varchar NULL,
    attachment_3_4 varchar NULL,
    extras jsonb NULL,
    plan_id varchar NULL,
    plan_name varchar NULL,
    duration int4 NULL,
    product_type varchar NULL,
    legacy jsonb NULL,
    duration_type varchar NULL,
    medium_tax_ratio float8 NULL,
    ia_code varchar NULL,
    ia_net_commission float8 NULL,
    CONSTRAINT products_pkey PRIMARY KEY (id),
    CONSTRAINT uk_products UNIQUE (product_code)
);

 


-- product.products foreign keys

 

ALTER TABLE product.products ADD CONSTRAINT products_product_image_id_fkey FOREIGN KEY (product_image_id) REFERENCES product.product_images(id);

-- product.branches definition

-- Drop table

-- DROP TABLE product.branches;

CREATE TABLE product.branches (
	id serial4 NOT NULL,
	branch_name varchar NOT NULL,
	external_code varchar NULL,
	"section" varchar NULL,
	CONSTRAINT branches_pkey PRIMARY KEY (id),
	CONSTRAINT uk_branches UNIQUE (branch_name)
);


-- product.anag_warranties definition

-- Drop table

-- DROP TABLE product.anag_warranties;

CREATE TABLE product.anag_warranties (
	id serial4 NOT NULL,
	branch_id int4 NOT NULL,
	"name" varchar NOT NULL,
	ceilings jsonb NOT NULL DEFAULT '{}'::jsonb,
	images jsonb NOT NULL DEFAULT '{}'::jsonb,
	CONSTRAINT anag_warranties_pkey PRIMARY KEY (id),
	CONSTRAINT uk_anag_warranties_id UNIQUE (id),
	CONSTRAINT uk_anag_warranties_name UNIQUE (name)
);


-- product.anag_warranties foreign keys

ALTER TABLE product.anag_warranties ADD CONSTRAINT anag_warranties_branch_id_fkey FOREIGN KEY (branch_id) REFERENCES product.branches(id);


-- product.categories definition

-- Drop table

-- DROP TABLE product.categories;

CREATE TABLE product.categories (
	id serial4 NOT NULL,
	category_name varchar NOT NULL,
	external_code varchar NOT NULL,
	asset jsonb NOT NULL DEFAULT '{}'::jsonb,
	CONSTRAINT categories_pkey PRIMARY KEY (id),
	CONSTRAINT uk_categories UNIQUE (category_name)
);

-- product."configuration" definition

-- Drop table

-- DROP TABLE product."configuration";

CREATE TABLE product."configuration" (
	id serial4 NOT NULL,
	product_id int4 NOT NULL,
	emission varchar NULL,
	emission_prefix varchar NULL,
	certificate varchar NULL,
	can_open_claim bool NOT NULL DEFAULT false,
	claim_type varchar NULL,
	claim_provider varchar NULL,
	withdraw_type varchar NULL,
	deactivate_type varchar NULL,
	properties jsonb NULL DEFAULT '{}'::jsonb,
	deactivate_provider varchar NULL,
	CONSTRAINT configuration_pkey PRIMARY KEY (id)
);

-- product.packets definition

-- Drop table

-- DROP TABLE product.packets;

CREATE TABLE product.packets (
	id serial4 NOT NULL,
	"name" varchar NOT NULL,
	broker_id int4 NULL,
	product_id int4 NOT NULL,
	sku varchar NOT NULL,
	packet_premium numeric(10, 4) NULL,
	description varchar NULL,
	duration varchar NULL,
	fixed_end_date timestamp NULL,
	fixed_start_date timestamp NULL,
	plan_id varchar NULL,
	plan_name varchar NULL,
	duration_type varchar NULL,
	medium_tax_ratio float8 NULL,
	ia_code varchar NULL,
	ia_net_commission float8 NULL,
	CONSTRAINT packets_pkey PRIMARY KEY (id)
);


-- product.packets foreign keys

ALTER TABLE product.packets ADD CONSTRAINT packets_product_id_fkey FOREIGN KEY (product_id) REFERENCES product.products(id);




-- product.payment_methods definition

-- Drop table

-- DROP TABLE product.payment_methods;

CREATE TABLE product.payment_methods (
	id serial4 NOT NULL,
	payment_method varchar NOT NULL,
	active bool NOT NULL DEFAULT true,
	payment_method_type varchar NULL,
	"type" varchar NULL,
	external_id int4 NULL,
	provider varchar NULL,
	CONSTRAINT payment_methods_pkey PRIMARY KEY (id)
);

-- product.product_categories definition

-- Drop table

-- DROP TABLE product.product_categories;

CREATE TABLE product.product_categories (
	category_id int4 NOT NULL,
	product_id int4 NOT NULL,
	CONSTRAINT product_categories_pkey PRIMARY KEY (category_id, product_id)
);


-- product.product_categories foreign keys

ALTER TABLE product.product_categories ADD CONSTRAINT product_categories_category_id_fkey FOREIGN KEY (category_id) REFERENCES product.categories(id);
ALTER TABLE product.product_categories ADD CONSTRAINT product_categories_product_id_fkey FOREIGN KEY (product_id) REFERENCES product.products(id);



-- product.product_splits definition

-- Drop table

-- DROP TABLE product.product_splits;

CREATE TABLE product.product_splits (
	id serial4 NOT NULL,
	product_id int4 NOT NULL,
	"name" varchar NULL,
	value varchar NULL,
	sku varchar NULL,
	CONSTRAINT product_splits_pkey PRIMARY KEY (id)
);


-- product.product_splits foreign keys

ALTER TABLE product.product_splits ADD CONSTRAINT product_splits_product_id_fkey FOREIGN KEY (product_id) REFERENCES product.products(id);



-- product.products_payment_methods definition

-- Drop table

-- DROP TABLE product.products_payment_methods;

CREATE TABLE product.products_payment_methods (
	id serial4 NOT NULL,
	product_id int4 NOT NULL,
	payment_method_id int4 NOT NULL,
	CONSTRAINT products_payment_methods_pkey PRIMARY KEY (id)
);

-- product.related_warranties definition

-- Drop table

-- DROP TABLE product.related_warranties;

CREATE TABLE product.related_warranties (
	id serial4 NOT NULL,
	packet_id int4 NOT NULL,
	category_id int4 NULL,
	warranties_id int4 NOT NULL,
	parent_id int4 NULL,
	mandatory bool NOT NULL DEFAULT false,
	insurance_premium numeric(10, 4) NULL,
	start_date date NULL,
	end_date date NULL,
	recurring bool NOT NULL DEFAULT false,
	"rule" jsonb NULL,
	external_code varchar NULL,
	ceilings jsonb NULL,
	taxons jsonb NULL,
	CONSTRAINT related_warranties_pkey PRIMARY KEY (id),
	CONSTRAINT uk_related_warranties UNIQUE (packet_id, warranties_id)
);


-- product.related_warranties foreign keys

ALTER TABLE product.related_warranties ADD CONSTRAINT related_warranties_category_id_fkey FOREIGN KEY (category_id) REFERENCES product.categories(id);
ALTER TABLE product.related_warranties ADD CONSTRAINT related_warranties_packet_id_fkey FOREIGN KEY (packet_id) REFERENCES product.packets(id);
ALTER TABLE product.related_warranties ADD CONSTRAINT related_warranties_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES product.related_warranties(id);
ALTER TABLE product.related_warranties ADD CONSTRAINT related_warranties_warranties_id_fkey FOREIGN KEY (warranties_id) REFERENCES product.anag_warranties(id);

-- product.survey_questions definition

-- Drop table

-- DROP TABLE product.survey_questions;

CREATE TABLE product.survey_questions (
	id serial4 NOT NULL,
	"content" varchar NOT NULL,
	product_id int4 NOT NULL,
	"position" int4 NOT NULL,
	external_code varchar NULL,
	CONSTRAINT survey_questions_pkey PRIMARY KEY (id)
);


-- product.survey_questions foreign keys

ALTER TABLE product.survey_questions ADD CONSTRAINT survey_questions_product_id_fkey FOREIGN KEY (product_id) REFERENCES product.products(id);

-- product.survey_answers definition

-- Drop table

-- DROP TABLE product.survey_answers;

CREATE TABLE product.survey_answers (
	id serial4 NOT NULL,
	value varchar NOT NULL,
	survey_questions_id int4 NOT NULL,
	"position" int4 NOT NULL,
	default_value bool NOT NULL DEFAULT false,
	"rule" varchar NULL,
	external_code varchar NULL,
	CONSTRAINT survey_answers_pkey PRIMARY KEY (id)
);


-- product.survey_answers foreign keys

ALTER TABLE product.survey_answers ADD CONSTRAINT survey_answers_survey_questions_id_fkey FOREIGN KEY (survey_questions_id) REFERENCES product.survey_questions(id);

INSERT INTO product.categories
(id, category_name, external_code, asset)
VALUES(1, 'Sport', 'sport', '{}'::jsonb);
INSERT INTO product.categories
(id, category_name, external_code, asset)
VALUES(2, 'People', 'people', '{}'::jsonb);
INSERT INTO product.categories
(id, category_name, external_code, asset)
VALUES(3, 'Casa e Famiglia', 'home_n_family', '{}'::jsonb);
INSERT INTO product.categories
(id, category_name, external_code, asset)
VALUES(4, 'Salute', 'health', '{}'::jsonb);


INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(7, 'Infortuni (infortuni sul lavoro, malattie professionali; persone trasportate)', 'DANNI-1', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(8, 'Malattia', 'DANNI-2', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(9, 'Corpi di veicoli terrestri (non ferroviari)', 'DANNI-3', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(10, 'Corpi di veicoli ferroviari', 'DANNI-4', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(11, 'Corpi di veicoli aerei', 'DANNI-5', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(13, 'Merci trasportate (anche bagagli e altri bene)', 'DANNI-7', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(14, 'Incendio ed elementi naturali (danni ai beni)', 'DANNI-8', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(12, 'Corpi di veicoli marittimi, lacustri e fluviali', 'DANNI-6', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(5, 'Operazioni di gestione fondi collettivi per prestazioni in caso di morte, di vita, riduzione o cessazione attività lavorativa (es. fondi pensione)', 'VITA-5', 'VITA');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(3, 'Assicurazioni malattia e contro il rischio di non autosufficienza', 'VITA-3', 'VITA');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(4, 'Operazioni di capitalizzazione (accantonamenti)', 'VITA-4', 'VITA');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(1, 'Assicurazioni sulla durata della vita umana', 'VITA-1', 'VITA');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(2, 'Assicurazioni di nuzialità e natalità', 'VITA-2', 'VITA');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(15, 'Altri danni ai beni causato da grandine, gelo o altro evento (furto) non compreso dal precedente n. 8', 'DANNI-9', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(16, 'Responsabilità civile autoveicoli terrestri', 'DANNI-10', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(17, 'Responsabilità civile aeromobili', 'DANNI-11', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(18, 'Responsabilità civile veicoli marittimi, lacustri e fluviali', 'DANNI-12', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(19, 'Responsabilità civile generale (RC non compresa nelle precedenti, es. RC professionale)', 'DANNI-13', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(20, 'Credito', 'DANNI-14', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(21, 'Cauzione', 'DANNI-15', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(22, 'Perdite pecuniarie di vario genere', 'DANNI-16', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(23, 'Tutela legale', 'DANNI-17', 'DANNI');
INSERT INTO product.branches
(id, branch_name, external_code, "section")
VALUES(24, 'Assistenza', 'DANNI-18', 'DANNI');

INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(1, 7, 'Responsabilità Civile', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(2, 7, 'Tutela Legale', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(3, 7, 'Assistenza', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(4, 7, 'Recupero e salvataggio', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(5, 7, 'Spese mediche', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(6, 7, 'Infortuni', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(7, 7, 'Assistenza imprevisti', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(8, 7, 'RC Abitazione', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(9, 7, 'RC Vita Privata', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(10, 7, 'Danni all''abitazione', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(11, 7, 'Danni da acqua', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(12, 7, 'Danni al contenuto', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(13, 7, 'Terremoto e alluvione', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(14, 7, 'Rimborso spese veterinarie', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(15, 7, 'Invio di un medico / ambulanza in caso di urgenza', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(16, 7, 'Telemedicina / Teleconsultazione pediatrica', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(17, 7, 'Rilascio ricetta / consegna farmaci post teleconsultazione', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(18, 7, 'Trasferimento sanitario programmato', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(19, 7, 'Assistenza professionale', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(20, 7, 'Rimborso spese mediche da infortunio', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(21, 7, 'Invalidità permanente e decesso da infortunio', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(22, 7, 'Rimborso spese recupero e salvataggio', '{}'::jsonb, '{}'::jsonb);
INSERT INTO product.anag_warranties
(id, branch_id, "name", ceilings, images)
VALUES(23, 7, 'Rimborso costi (skipass, lezioni sci, noleggio attrezzature) in caso di infortunio', '{}'::jsonb, '{}'::jsonb);

INSERT INTO product.product_images
(id, images)
VALUES(1, '{"images": [{"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/3/mini/logo-quixa-axa.png?1627035280", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/3/large/logo-quixa-axa.png?1627035280", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/3/small/logo-quixa-axa.png?1627035280", "image_type": "fp_image", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/3/product/logo-quixa-axa.png?1627035280", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/3/original/logo-quixa-axa.png?1627035280"}, {"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/4/mini/logo-quixa-axa.png?1627035298", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/4/large/logo-quixa-axa.png?1627035298", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/4/small/logo-quixa-axa.png?1627035298", "image_type": "app_icon", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/4/product/logo-quixa-axa.png?1627035298", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/4/original/logo-quixa-axa.png?1627035298"}, {"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/4/mini/logo-quixa-axa.png?1627035298", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/4/large/logo-quixa-axa.png?1627035298", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/4/small/logo-quixa-axa.png?1627035298", "image_type": "company_image", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/4/product/logo-quixa-axa.png?1627035298", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/4/original/logo-quixa-axa.png?1627035298"}]}'::jsonb);
INSERT INTO product.product_images
(id, images)
VALUES(3, '{"images": [{"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/5/mini/unipol.jpg?1632755535", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/5/large/unipol.jpg?1632755535", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/5/small/unipol.jpg?1632755535", "image_type": "fp_image", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/5/product/unipol.jpg?1632755535", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/5/original/unipol.jpg?1632755535"}, {"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/6/mini/unipol.jpg?1632755557", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/6/large/unipol.jpg?1632755557", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/6/small/unipol.jpg?1632755557", "image_type": "app_icon", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/6/product/unipol.jpg?1632755557", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/6/original/unipol.jpg?1632755557"}, {"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/6/mini/unipol.jpg?1632755557", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/6/large/unipol.jpg?1632755557", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/6/small/unipol.jpg?1632755557", "image_type": "company_image", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/6/product/unipol.jpg?1632755557", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/6/original/unipol.jpg?1632755557"}]}'::jsonb);
INSERT INTO product.product_images
(id, images)
VALUES(4, '{"images": [{"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/9/mini/cane.png?1646644405", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/9/large/cane.png?1646644405", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/9/small/cane.png?1646644405", "image_type": "app_icon", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/9/product/cane.png?1646644405", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/9/original/cane.png?1646644405"}, {"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/10/mini/cane.png?**********", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/10/large/cane.png?**********", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/10/small/cane.png?**********", "image_type": "fp_image", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/10/product/cane.png?**********", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/10/original/cane.png?**********"}, {"mini_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "large_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "small_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "image_type": "company_image", "product_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "original_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********"}]}'::jsonb);
INSERT INTO product.product_images
(id, images)
VALUES(6, '{"images": [{"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/12/mini/ski-net.png?**********", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/12/large/ski-net.png?**********", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/12/small/ski-net.png?**********", "image_type": "app_icon", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/12/product/ski-net.png?**********", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/12/original/ski-net.png?**********"}, {"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/13/mini/ski-net.png?**********", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/13/large/ski-net.png?**********", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/13/small/ski-net.png?**********", "image_type": "fp_image", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/13/product/ski-net.png?**********", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/13/original/ski-net.png?**********"}, {"mini_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "large_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "small_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "image_type": "company_image", "product_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "original_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********"}]}'::jsonb);
INSERT INTO product.product_images
(id, images)
VALUES(2, '{"images": [{"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/7/mini/chubb.png?**********", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/7/large/chubb.png?**********", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/7/small/chubb.png?**********", "image_type": "app_icon", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/7/product/chubb.png?**********", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/7/original/chubb.png?**********"}, {"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/8/mini/chubb.png?**********", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/8/large/chubb.png?**********", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/8/small/chubb.png?**********", "image_type": "fp_image", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/8/product/chubb.png?**********", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/8/original/chubb.png?**********"}, {"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/8/mini/chubb.png?**********", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/8/large/chubb.png?**********", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/8/small/chubb.png?**********", "image_type": "company_image", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/8/product/chubb.png?**********", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/8/original/chubb.png?**********"}]}'::jsonb);
INSERT INTO product.product_images
(id, images)
VALUES(5, '{"images": [{"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/11/mini/ski-net.png?**********", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/11/large/ski-net.png?**********", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/11/small/ski-net.png?**********", "image_type": "fp_image", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/11/product/ski-net.png?**********", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/11/original/ski-net.png?**********"}, {"mini_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "large_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "small_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "image_type": "company_image", "product_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "original_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********"}]}'::jsonb);
INSERT INTO product.product_images
(id, images)
VALUES(7, '{"images": [{"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/12/mini/ski-net.png?**********", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/12/large/ski-net.png?**********", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/12/small/ski-net.png?**********", "image_type": "app_icon", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/12/product/ski-net.png?**********", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/12/original/ski-net.png?**********"}, {"mini_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/13/mini/ski-net.png?**********", "large_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/13/large/ski-net.png?**********", "small_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/13/small/ski-net.png?**********", "image_type": "fp_image", "product_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/13/product/ski-net.png?**********", "original_url": "https://tim-customers-production.s3.amazonaws.com/spree/images/attachments/13/original/ski-net.png?**********"}, {"mini_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "large_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "small_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "image_type": "company_image", "product_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********", "original_url": "https://fca-bank-integration.s3.amazonaws.com/yolo/providers/images/000/000/001/original/net_insurance.png?**********"}]}'::jsonb);



INSERT INTO product.products
(id, product_code, product_description, start_date, end_date, recurring, external_id, insurance_premium, insurance_company, insurance_company_logo, business, title_prod, short_description, description, conditions, information_package, conditions_package, display_price, price, only_contractor, maximum_insurable, can_open_claim, holder_maximum_age, holder_minimum_age, show_in_dashboard, product_image_id, catalog_id, properties, quotator_type, show_addons_in_shopping_cart, thumbnail, privacy_documentation_link, informative_set, attachment_3_4, extras, plan_id, plan_name, duration, product_type, legacy, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(1, 'ehealth-quixa-standard', 'TIM myHealth', '2022-06-12', NULL, true, NULL, 6.9000, 'Quixa', 'https://tim-customers-production.s3.amazonaws.com/yolo/providers/images/000/000/001/original/logo.png?**********', 'false', '', '', '', '', 'https://tim-customers-production.s3.amazonaws.com/spree/products/information_packages/000/000/002/original/Set_Informativo_di_polizza.pdf?**********', '/conditions_packages/original/missing.png', '6,90 €', 6.9000, false, 1, false, 150, 0, true, 1, 10, '{"properties": [{"name": "consistency", "value": "false"}, {"name": "alias", "value": "medica-my-health"}]}'::jsonb, 'quotator_ehealth-quixa-standard', false, false, '', 'https://tim-customers-production.s3.amazonaws.com/spree/products/information_packages/000/000/002/original/Set_Informativo_di_polizza.pdf', 'https://tim-broker-customers-documents.s3.eu-central-1.amazonaws.com/Allegati_3_4_4ter.pdf', '{}'::jsonb, NULL, NULL, 1, 'QuixaEHealth', NULL, 'year', NULL, NULL, NULL);
INSERT INTO product.products
(id, product_code, product_description, start_date, end_date, recurring, external_id, insurance_premium, insurance_company, insurance_company_logo, business, title_prod, short_description, description, conditions, information_package, conditions_package, display_price, price, only_contractor, maximum_insurable, can_open_claim, holder_maximum_age, holder_minimum_age, show_in_dashboard, product_image_id, catalog_id, properties, quotator_type, show_addons_in_shopping_cart, thumbnail, privacy_documentation_link, informative_set, attachment_3_4, extras, plan_id, plan_name, duration, product_type, legacy, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(6, 'tim-for-ski-gold', 'TIM Sci&Snowboard ROSSA', '2022-06-12', NULL, false, NULL, 0.0000, 'Net Insurance', 'https://tim-customers-production.s3.amazonaws.com/yolo/providers/images/000/000/006/original/logo-netinsurance.png?', 'false', '', '<strong>TIM for Ski Rossa&nbsp;</strong>ti offre una protezione completa che, oltre all'' RC obbligatoria, include: rimborso spese mediche da infortunio, rimborso spese di recupero e salvataggio, rimborso costi (skipass, lezioni di sci, noleggio attrezzatura) e tutela legale.', '', '', 'https://tim-customers-production.s3.amazonaws.com/spree/products/information_packages/000/000/010/original/SET_INFORMATIVO_TIM_SCI___SNOWBOARD.pdf?**********', '/conditions_packages/original/missing.png', '2,50 €', 2.5000, false, 5, true, 70, 4, true, 6, 15, '{"properties": [{"name": "season_start_date", "value": "01/11/2022"}, {"name": "season_end_date", "value": "15/05/2023"}, {"name": "pre_season_start_date", "value": "01/09/2022"}, {"name": "pre_season_end_date", "value": "15/05/2023"}, {"name": "uniq_name", "value": "TIM Sci&Snowboard ROSSA"}, {"name": "custom_promotion_handling", "value": "true"}, {"name": "consistency", "value": "false"}]}'::jsonb, 'quotator_net', false, false, 'https://tim-broker-customers-documents.s3.eu-central-1.amazonaws.com/Informativa-privacy-trattamento+dei+dati+NET.pdf', 'https://tim-customers-production.s3.amazonaws.com/spree/products/information_packages/000/000/010/original/SET_INFORMATIVO_TIM_SCI___SNOWBOARD.pdf', 'https://tim-broker-customers-documents.s3.eu-central-1.amazonaws.com/Allegati_3_4_4ter.pdf', '{}'::jsonb, NULL, NULL, NULL, 'NetInsuranceYoloForSki', NULL, NULL, NULL, NULL, NULL);
INSERT INTO product.products
(id, product_code, product_description, start_date, end_date, recurring, external_id, insurance_premium, insurance_company, insurance_company_logo, business, title_prod, short_description, description, conditions, information_package, conditions_package, display_price, price, only_contractor, maximum_insurable, can_open_claim, holder_maximum_age, holder_minimum_age, show_in_dashboard, product_image_id, catalog_id, properties, quotator_type, show_addons_in_shopping_cart, thumbnail, privacy_documentation_link, informative_set, attachment_3_4, extras, plan_id, plan_name, duration, product_type, legacy, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(2, 'tim-my-sci', 'TIM Sci&Snowboard', '2022-06-12', NULL, false, NULL, 0.0000, 'Chubb', 'https://tim-customers-production.s3.amazonaws.com/yolo/providers/images/000/000/005/original/chubb.png?**********', 'false', '', '', '', '', 'https://tim-customers-production.s3.amazonaws.com/spree/products/information_packages/000/000/007/original/CHUBB_TIM_Sci_Snowboard_Set_Informativo_v0001_02.2022.pdf?**********', '/conditions_packages/original/missing.png', '0,90 €', 0.9000, false, 10, true, 150, 1, true, 2, 9, '{"properties": [{"name": "consistency", "value": "false"}, {"name": "alias", "value": "sci-snowboard"}, {"name": "custom_promotion_handling", "value": "true"}]}'::jsonb, 'quotator_tim-my-sci', false, false, '', 'https://tim-customers-staging.s3.amazonaws.com/spree/products/information_packages/000/000/011/original/CHUBB_TIM_Sci_Snowboard_Set_Informativo_v0001_02.2022.pdf', 'https://tim-broker-customers-documents.s3.eu-central-1.amazonaws.com/Allegati_3_4_4ter.pdf', '{}'::jsonb, NULL, NULL, NULL, 'ChubbSki', NULL, NULL, NULL, NULL, NULL);
INSERT INTO product.products
(id, product_code, product_description, start_date, end_date, recurring, external_id, insurance_premium, insurance_company, insurance_company_logo, business, title_prod, short_description, description, conditions, information_package, conditions_package, display_price, price, only_contractor, maximum_insurable, can_open_claim, holder_maximum_age, holder_minimum_age, show_in_dashboard, product_image_id, catalog_id, properties, quotator_type, show_addons_in_shopping_cart, thumbnail, privacy_documentation_link, informative_set, attachment_3_4, extras, plan_id, plan_name, duration, product_type, legacy, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(7, 'tim-for-ski-platinum', 'TIM Sci&Snowboard NERA', '2022-06-12', NULL, false, NULL, 0.0000, 'Net Insurance', 'https://tim-customers-production.s3.amazonaws.com/yolo/providers/images/000/000/006/original/logo-netinsurance.png?', 'false', '', '<strong>TIM for Ski&nbsp;Nera</strong>&nbsp;ti offre il massimo della protezione che, oltre alle garanzie del pacchetto Rossa, include una copertura pi&ugrave; avanzata per il rimborso delle spese mediche da infortunio.', '', '', 'https://tim-customers-production.s3.amazonaws.com/spree/products/information_packages/000/000/011/original/SET_INFORMATIVO_TIM_SCI___SNOWBOARD.pdf?**********', '/conditions_packages/original/missing.png', '3,50 €', 3.5000, false, 5, true, 70, 4, true, 7, 16, '{"properties": [{"name": "season_start_date", "value": "01/11/2022"}, {"name": "season_end_date", "value": "15/05/2023"}, {"name": "pre_season_start_date", "value": "01/09/2022"}, {"name": "pre_season_end_date", "value": "15/05/2023"}, {"name": "uniq_name", "value": "TIM Sci&Snowboard NERA"}, {"name": "custom_promotion_handling", "value": "true"}, {"name": "consistency", "value": "false"}]}'::jsonb, 'quotator_net', false, false, 'https://tim-broker-customers-documents.s3.eu-central-1.amazonaws.com/Informativa-privacy-trattamento+dei+dati+NET.pdf', 'https://tim-customers-production.s3.amazonaws.com/spree/products/information_packages/000/000/010/original/SET_INFORMATIVO_TIM_SCI___SNOWBOARD.pdf', 'https://tim-broker-customers-documents.s3.eu-central-1.amazonaws.com/Allegati_3_4_4ter.pdf', '{}'::jsonb, NULL, NULL, NULL, 'NetInsuranceYoloForSki', NULL, NULL, NULL, NULL, NULL);
INSERT INTO product.products
(id, product_code, product_description, start_date, end_date, recurring, external_id, insurance_premium, insurance_company, insurance_company_logo, business, title_prod, short_description, description, conditions, information_package, conditions_package, display_price, price, only_contractor, maximum_insurable, can_open_claim, holder_maximum_age, holder_minimum_age, show_in_dashboard, product_image_id, catalog_id, properties, quotator_type, show_addons_in_shopping_cart, thumbnail, privacy_documentation_link, informative_set, attachment_3_4, extras, plan_id, plan_name, duration, product_type, legacy, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(5, 'tim-for-ski-silver', 'TIM Sci&Snowboard BLU', '2022-06-12', NULL, false, NULL, 0.0000, 'Net Insurance', 'https://tim-customers-production.s3.amazonaws.com/yolo/providers/images/000/000/006/original/logo-netinsurance.png?', 'false', '', '<strong>TIM for Ski Blu&nbsp;</strong>offre una protezione di base che ti tutela in caso di responsabilit&agrave; civile risarcendo i danni che potresti involontariamente causare a terzi.', '', '', 'https://tim-customers-production.s3.amazonaws.com/spree/products/information_packages/000/000/009/original/SET_INFORMATIVO_TIM_SCI___SNOWBOARD.pdf?**********', '/conditions_packages/original/missing.png', '0,90 €', 0.9000, false, 5, true, 70, 4, true, 5, 14, '{"properties": [{"name": "season_start_date", "value": "01/11/2022"}, {"name": "season_end_date", "value": "15/05/2023"}, {"name": "pre_season_start_date", "value": "01/09/2022"}, {"name": "pre_season_end_date", "value": "15/05/2023"}, {"name": "uniq_name", "value": "TIM Sci&Snowboard BLU"}, {"name": "custom_promotion_handling", "value": "true"}, {"name": "consistency", "value": "false"}]}'::jsonb, 'quotator_net', false, false, 'https://tim-broker-customers-documents.s3.eu-central-1.amazonaws.com/Informativa-privacy-trattamento+dei+dati+NET.pdf', 'https://tim-customers-production.s3.amazonaws.com/spree/products/information_packages/000/000/010/original/SET_INFORMATIVO_TIM_SCI___SNOWBOARD.pdf', 'https://tim-broker-customers-documents.s3.eu-central-1.amazonaws.com/Allegati_3_4_4ter.pdf', '{}'::jsonb, NULL, NULL, NULL, 'NetInsuranceYoloForSki', NULL, NULL, NULL, NULL, NULL);
INSERT INTO product.products
(id, product_code, product_description, start_date, end_date, recurring, external_id, insurance_premium, insurance_company, insurance_company_logo, business, title_prod, short_description, description, conditions, information_package, conditions_package, display_price, price, only_contractor, maximum_insurable, can_open_claim, holder_maximum_age, holder_minimum_age, show_in_dashboard, product_image_id, catalog_id, properties, quotator_type, show_addons_in_shopping_cart, thumbnail, privacy_documentation_link, informative_set, attachment_3_4, extras, plan_id, plan_name, duration, product_type, legacy, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(4, 'customers-tim-pet', 'TIM myPet', '2022-06-12', NULL, true, NULL, 4.0000, 'Net Insurance', 'https://tim-customers-production.s3.amazonaws.com/yolo/providers/images/000/000/006/original/logo-netinsurance.png?**********', 'false', '', '', '', '', 'https://tim-customers-production.s3.amazonaws.com/spree/products/information_packages/000/000/008/original/SET_INFORMATIVO_TIM_myPet_customer.pdf?**********', '/conditions_packages/original/missing.png', '4,00 €', 4.0000, false, 1, true, 150, 0, true, 4, 3, '{"properties": [{"name": "consistency", "value": "false"}, {"name": "alias", "value": "animali-domestici"}, {"name": "custom_promotion_handling", "value": "true"}, {"name": "cod_compagnia_ania", "value": "441"}, {"name": "cod_rete_vendita_agenzia", "value": "446"}]}'::jsonb, 'quotator_customers-tim-pet', false, false, 'https://tim-broker-customers-documents.s3.eu-central-1.amazonaws.com/Informativa-privacy-TIM-myPet.pdf', 'https://tim-customers-production.s3.amazonaws.com/spree/products/information_packages/000/000/008/original/SET_INFORMATIVO_TIM_myPet_customer.pdf', 'https://tim-broker-customers-documents.s3.eu-central-1.amazonaws.com/Allegati_3_4_4ter.pdf', '{}'::jsonb, NULL, NULL, 1, 'NetInsuranceMyPet', '{"variants": [{"id": 21, "sku": "tim-my-pet-smart", "name": "option1", "price": 4.0, "option_values": [{"id": 36, "name": "option1", "presentation": "Opzione 1", "option_type_id": 7, "option_type_name": "package"}]}, {"id": 22, "sku": "tim-my-pet-smart-plus", "name": "option2", "price": 15.0, "option_values": [{"id": 38, "name": "option2", "presentation": "Opzione 2", "option_type_id": 7, "option_type_name": "package"}]}, {"id": 23, "sku": "tim-my-pet-deluxe", "name": "option3", "price": 19.0, "option_values": [{"id": 37, "name": "option3", "presentation": "Opzione 3", "option_type_id": 7, "option_type_name": "package"}]}], "claimProperties": {"provider": {"name": "Yolo", "email": "<EMAIL>", "baseUrl": "https://netins-staging.motionscloud.com", "authToken": "eyJhbGciOiJIUzI1NiJ9.*******************************************.jpbhLmWX-RtG-7Wl9XYfmoJDMSxXDMf8Bm9lhMw1Zbg", "policyType": ["rc", "pet_veterinary_expense_reimbursement"], "claimEndpoint": "/api/v1/digital_broker/claims", "tokenEndpoint": "/api/v1/digital_broker/registrations"}}}'::jsonb, 'year', NULL, NULL, NULL);
INSERT INTO product.products
(id, product_code, product_description, start_date, end_date, recurring, external_id, insurance_premium, insurance_company, insurance_company_logo, business, title_prod, short_description, description, conditions, information_package, conditions_package, display_price, price, only_contractor, maximum_insurable, can_open_claim, holder_maximum_age, holder_minimum_age, show_in_dashboard, product_image_id, catalog_id, properties, quotator_type, show_addons_in_shopping_cart, thumbnail, privacy_documentation_link, informative_set, attachment_3_4, extras, plan_id, plan_name, duration, product_type, legacy, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(3, 'tim-my-home', 'TIM Protezione Casa', '2022-06-12', NULL, false, NULL, 0.0000, 'Unipol', 'https://tim-customers-production.s3.amazonaws.com/yolo/providers/images/000/000/002/original/unipol.jpg?**********', 'false', '', '', '', '', 'https://tim-customers-production.s3.amazonaws.com/spree/products/information_packages/000/000/003/original/Assistenza_TIM_Completo_LogoTIM.pdf?**********', 'https://tim-customers-production.s3.amazonaws.com/spree/products/conditions_packages/000/000/003/original/Multigaranzia_TIM_Completo_LogoTIM_20211110.pdf?**********', '10,00 €', 10.0000, false, 1, true, 150, 0, true, 3, 6, '{"links": {"multirischio": "https://tim-customers-production.s3.amazonaws.com/spree/products/conditions_packages/000/000/003/original/Multigaranzia_TIM_Completo_LogoTIM_20211110.pdf", "venditaDistanza": "https://tim-broker-customers-documents.s3.eu-central-1.amazonaws.com/Informativa_precontrattuale_vendita_a_distanza_Danni_20190101.pdf"}, "properties": [{"name": "consistency", "value": "false"}, {"name": "withdrawal_email", "value": "<EMAIL>"}, {"name": "alias", "value": "protezione-casa"}, {"name": "custom_promotion_handling", "value": "true"}]}'::jsonb, 'quotator_tim-my-home', false, false, 'https://tim-broker-customers-documents.s3.eu-central-1.amazonaws.com/Informativa+Privacy.pdf', 'https://tim-customers-staging.s3.eu-central-1.amazonaws.com/spree/products/information_packages/000/000/008/original/Assistenza_TIM_Completo_LogoTIM.pdf', 'https://tim-broker-customers-documents.s3.eu-central-1.amazonaws.com/Allegati_3_4_4ter.pdf', '{}'::jsonb, NULL, NULL, 1, 'UnipolHome', '{"addons": [{"id": 1, "code": "A", "name": "Assistenza ", "price": 0, "prices": [{"id": 1, "price": "0.0", "addon_id": 1, "currency": "EUR", "created_at": "2021-09-27T17:04:31.850+02:00", "deleted_at": null, "updated_at": "2021-09-27T17:04:31.850+02:00", "variant_id": null, "country_iso": null}], "taxons": [{"id": 11, "name": "Basic", "parent_id": 10, "pretty_name": "Proposal -> Basic", "taxonomy_id": 3}, {"id": 12, "name": "Super", "parent_id": 10, "pretty_name": "Proposal -> Super", "taxonomy_id": 3}, {"id": 13, "name": "Full", "parent_id": 10, "pretty_name": "Proposal -> Full", "taxonomy_id": 3}], "ceilings": [400], "description": "La Società tiene a proprio carico i costi d’uscita e della manodopera fino ad un importo massimo complessivo di € 400,00 per evento, con un massimo di € 200,00 per artigiano intervenuto. La polizza opera fino ad un massimo di 3 interventi complessivi annui.\r\n\r\nSono esclusi, e restano a carico dell’Assicurato, i costi per gli interventi di riparazione relativi agli impianti domotici nonché i costi dei pezzi di ricambio e dei materiali di consumo in genere", "cover_details": ""}, {"id": 2, "code": "B1", "name": "RC Conduzione Abitazione ", "price": 0, "prices": [{"id": 2, "price": "0.0", "addon_id": 2, "currency": "EUR", "created_at": "2021-09-27T17:05:10.922+02:00", "deleted_at": null, "updated_at": "2021-09-27T17:05:10.922+02:00", "variant_id": null, "country_iso": null}], "taxons": [], "ceilings": [250000], "description": "Copertura danni causati a terzi nella conduzione dell’immobile € 250.000 di massimale con franchigia di € 250 Premio Annuo: 5 € Comprende i danni derivanti dalla conduzione dell’abitazione e del suo contenuto, compresa la manutenzione ordinaria, dallo spargimento d’acqua e altri liquidi, anche se conseguente a rottura accidentale di apparecchi e macchine ad uso domestico e dalla caduta di antenne o parabole radio-telericeventi.", "cover_details": ""}, {"id": 3, "code": "B2", "name": "RC Vita privata", "price": 0, "prices": [{"id": 3, "price": "0.0", "addon_id": 3, "currency": "EUR", "created_at": "2021-09-27T17:05:51.392+02:00", "deleted_at": null, "updated_at": "2021-09-27T17:05:51.392+02:00", "variant_id": null, "country_iso": null}], "taxons": [{"id": 12, "name": "Super", "parent_id": 10, "pretty_name": "Proposal -> Super", "taxonomy_id": 3}], "ceilings": [1000000], "description": "Copertura danni a terzi provocati dal nucleo familiare e dalla conduzione dell’immobile. L’assicurato entro il massimale convenuto (€ 250.000) per sinistro e per periodo assicurativo è indenne della somma che deve risarcire, se civilmente responsabile ai sensi di legge, per i danni involontariamente causati a terzi per morte, lesioni personale/o danneggiamenti a cose, in conseguenza di un fatto verificatosi nell’ambito dei rischi connessi alla conduzione dell’abitazione. € 1.000.000 di massimale Premio Annuo: 50 €", "cover_details": ""}, {"id": 4, "code": "C", "name": "Danni al Fabbricato ", "price": 0, "prices": [{"id": 4, "price": "0.0", "addon_id": 4, "currency": "EUR", "created_at": "2021-09-27T17:06:53.734+02:00", "deleted_at": null, "updated_at": "2021-09-27T17:06:53.734+02:00", "variant_id": null, "country_iso": null}], "taxons": [{"id": 13, "name": "Full", "parent_id": 10, "pretty_name": "Proposal -> Full", "taxonomy_id": 3}], "ceilings": [50000, 80000, 100000], "description": "Protegge la tua casa da danni diversi, assicurando l''abitazione (intese le mura) ed il suo contenuto", "cover_details": ""}, {"id": 5, "code": "C1", "name": "Danni da acqua condotta e spese di ricerca del guasto", "price": 0, "prices": [{"id": 5, "price": "0.0", "addon_id": 5, "currency": "EUR", "created_at": "2021-09-27T17:07:24.752+02:00", "deleted_at": null, "updated_at": "2021-09-27T17:07:24.752+02:00", "variant_id": null, "country_iso": null}], "taxons": [{"id": 13, "name": "Full", "parent_id": 10, "pretty_name": "Proposal -> Full", "taxonomy_id": 3}], "ceilings": [5000], "description": "Garanzia importante in quanto copre le spese da sostenere a seguito di rottura o guasto accidentale di acqua condotta", "cover_details": ""}, {"id": 6, "code": "C2", "name": "Danni al contenuto", "price": 0, "prices": [{"id": 6, "price": "0.0", "addon_id": 6, "currency": "EUR", "created_at": "2021-09-27T17:07:53.874+02:00", "deleted_at": null, "updated_at": "2021-09-27T17:07:53.874+02:00", "variant_id": null, "country_iso": null}], "taxons": [], "ceilings": [25000], "description": "Prevede un massimo indennizzo per danni al contenuto di € 25.000 annuo. All''interno della presente garanzia la società indennizza i danni causati da furto, commessi all''interno dell''abitazione (escluso preziosi e valori), fino ad un importo massimo di € 5.000 annuo. Per conoscere limiti/sottolimiti e franchigie/scoperti ti invitiamo a leggere il set informativo sezione danni ai beni ", "cover_details": ""}, {"id": 7, "code": "C3", "name": "Terremoto e alluvione", "price": 0, "prices": [{"id": 7, "price": "0.0", "addon_id": 7, "currency": "EUR", "created_at": "2021-09-27T17:08:58.774+02:00", "deleted_at": null, "updated_at": "2021-09-27T17:08:58.774+02:00", "variant_id": null, "country_iso": null}], "taxons": [], "description": "Terremoto: la compagnia rimborsa fino al 70% della somma assicurata a primo rischio assoluto del massimale scelto per la garanzia danni all''abitazione. La garanzia opera con uno scoperto del 10% per un minimo di € 10.000.\r\n\r\nInondazione/Alluvione/Allagamento: la compagnia rimborsa fino al 50% della somma assicurata a primo rischio assoluto del massimale scelto per la garanzia danni all''abitazione. La garanzia opera con uno scoperto del 10% per un minimo di € 10.000", "cover_details": ""}], "promotion": [{"user_type": "ftth", "product_name": "tim-my-home", "promotion_name": "FTTH PROMO", "promotion_rules": "user_type", "promotion_active": true}]}'::jsonb, 'year', NULL, NULL, NULL);

INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(7, 'Smart', 1, 4, 'tim-my-pet-smart', 4.0000, 'option1', '1', NULL, NULL, NULL, NULL, 'year', NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(8, 'Smart+', 1, 4, 'tim-my-pet-smart-plus', 15.0000, 'option2', '1', NULL, NULL, NULL, NULL, 'year', NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(12, 'Seasonal', 1, 5, 'tim-for-ski-silver-season', 8.0000, 'Seasonal', NULL, '2023-05-15 00:00:00.000', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(14, 'Seasonal', 1, 6, 'tim-for-ski-gold-season', 25.0000, 'Seasonal', NULL, '2023-05-15 00:00:00.000', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(16, 'Seasonal', 1, 7, 'tim-for-ski-platinum-season', 35.0000, 'Seasonal', NULL, '2023-05-15 00:00:00.000', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(11, '1 day', 1, 5, 'tim-for-ski-silver-day', 0.9000, '1 day', '0', NULL, NULL, NULL, NULL, 'day', NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(13, '1 day', 1, 6, 'tim-for-ski-gold-day', 2.5000, '1 day', '0', NULL, NULL, NULL, NULL, 'day', NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(15, '1 day', 1, 7, 'tim-for-ski-platinum-day', 3.5000, '1 day', '0', NULL, NULL, NULL, NULL, 'day', NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(9, 'Deluxe', 1, 4, 'tim-my-pet-deluxe', 19.0000, 'option3', '1', NULL, NULL, NULL, NULL, 'year', NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(1, 'Blu', 1, 2, 'sci_blue', 0.0000, 'option1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(2, 'Rossa', 1, 2, 'sci_red', 0.0000, 'option2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(3, 'Nera', 1, 2, 'sci_black', 0.0000, 'option3', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(4, 'Assistenza Imprevisti', 1, 3, 'tim-my-home', 0.0000, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(5, 'Protezione Imprevisti', 1, 3, 'tim-my-home', 0.0000, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(6, 'Protezione Abitazione', 1, 3, 'tim-my-home', 0.0000, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO product.packets
(id, "name", broker_id, product_id, sku, packet_premium, description, duration, fixed_end_date, fixed_start_date, plan_id, plan_name, duration_type, medium_tax_ratio, ia_code, ia_net_commission)
VALUES(10, 'Tim myHealth', 1, 1, 'ehealth-quixa-standard', 5.9000, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);


INSERT INTO product.payment_methods
(id, payment_method, active, payment_method_type, "type", external_id, provider)
VALUES(1, 'Store Credit', true, 'Spree::PaymentMethod::StoreCredit', 'single', NULL, NULL);
INSERT INTO product.payment_methods
(id, payment_method, active, payment_method_type, "type", external_id, provider)
VALUES(4, 'Ypayments', true, 'Solidus::Gateway::YpaymentsGateway', 'single', NULL, NULL);
INSERT INTO product.payment_methods
(id, payment_method, active, payment_method_type, "type", external_id, provider)
VALUES(2, 'Braintree', true, 'Solidus::Gateway::BraintreeGateway', 'single', NULL, 'Braintree');
INSERT INTO product.payment_methods
(id, payment_method, active, payment_method_type, "type", external_id, provider)
VALUES(3, 'Braintree recurrent', true, 'Spree::Gateway::BraintreeRecurrent', 'recurring', NULL, 'Braintree');
INSERT INTO product.payment_methods
(id, payment_method, active, payment_method_type, "type", external_id, provider)
VALUES(6, 'Gup', true, 'Spree::PaymentMethod::Gup', 'single', 1, 'Gup');
INSERT INTO product.payment_methods
(id, payment_method, active, payment_method_type, "type", external_id, provider)
VALUES(7, 'Gup recurrent', true, 'Spree::PaymentMethod::GupRecurrent', 'recurring', 2, 'Gup');
INSERT INTO product.payment_methods
(id, payment_method, active, payment_method_type, "type", external_id, provider)
VALUES(5, 'No Payment CC', true, 'Spree::PaymentMethod::NoPaymentCreditCard', 'single', 5, 'NoPay');

INSERT INTO product.product_categories
(category_id, product_id)
VALUES(4, 1);
INSERT INTO product.product_categories
(category_id, product_id)
VALUES(1, 2);
INSERT INTO product.product_categories
(category_id, product_id)
VALUES(3, 3);
INSERT INTO product.product_categories
(category_id, product_id)
VALUES(2, 4);
INSERT INTO product.product_categories
(category_id, product_id)
VALUES(2, 5);
INSERT INTO product.product_categories
(category_id, product_id)
VALUES(2, 6);
INSERT INTO product.product_categories
(category_id, product_id)
VALUES(2, 7);



INSERT INTO product.products_payment_methods
(id, product_id, payment_method_id)
VALUES(1, 1, 7);
INSERT INTO product.products_payment_methods
(id, product_id, payment_method_id)
VALUES(2, 2, 6);
INSERT INTO product.products_payment_methods
(id, product_id, payment_method_id)
VALUES(3, 3, 6);
INSERT INTO product.products_payment_methods
(id, product_id, payment_method_id)
VALUES(4, 3, 5);
INSERT INTO product.products_payment_methods
(id, product_id, payment_method_id)
VALUES(5, 3, 2);
INSERT INTO product.products_payment_methods
(id, product_id, payment_method_id)
VALUES(6, 4, 7);
INSERT INTO product.products_payment_methods
(id, product_id, payment_method_id)
VALUES(7, 5, 6);
INSERT INTO product.products_payment_methods
(id, product_id, payment_method_id)
VALUES(8, 6, 6);
INSERT INTO product.products_payment_methods
(id, product_id, payment_method_id)
VALUES(9, 7, 6);

INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(1, 1, NULL, 1, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(2, 1, NULL, 2, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(3, 1, NULL, 3, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(4, 1, NULL, 4, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(5, 1, NULL, 5, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(6, 1, NULL, 6, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(7, 2, NULL, 1, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(8, 2, NULL, 2, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(9, 2, NULL, 3, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(10, 2, NULL, 4, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(11, 2, NULL, 5, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(12, 2, NULL, 6, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(13, 3, NULL, 1, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(14, 3, NULL, 2, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(15, 3, NULL, 3, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(16, 3, NULL, 4, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(17, 3, NULL, 5, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(18, 3, NULL, 6, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(19, 4, NULL, 7, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, 'A', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{"taxons": [{"name": "Basic", "pretty_name": "Proposal -> Basic"}, {"name": "Full", "pretty_name": "Proposal -> Full"}, {"name": "Super", "pretty_name": "Proposal -> Super"}]}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(20, 4, NULL, 8, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, 'B1', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(21, 4, NULL, 9, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, 'B2', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{"taxons": [{"name": "Super", "pretty_name": "Proposal -> Super"}]}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(22, 4, NULL, 10, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, 'C', '{"ceilings": ["50000", "80000", "100000"], "ceilings_rules": {}}'::jsonb, '{"taxons": [{"name": "Full", "pretty_name": "Proposal -> Full"}]}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(23, 4, NULL, 11, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, 'C1', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{"taxons": [{"name": "Full", "pretty_name": "Proposal -> Full"}]}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(24, 4, NULL, 12, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, 'C2', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(25, 4, NULL, 13, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, 'C3', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(26, 5, NULL, 7, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, 'A', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{"taxons": [{"name": "Basic", "pretty_name": "Proposal -> Basic"}, {"name": "Full", "pretty_name": "Proposal -> Full"}, {"name": "Super", "pretty_name": "Proposal -> Super"}]}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(27, 5, NULL, 8, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, 'B1', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(28, 5, NULL, 9, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, 'B2', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{"taxons": [{"name": "Super", "pretty_name": "Proposal -> Super"}]}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(29, 5, NULL, 10, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, 'C', '{"ceilings": ["50000", "80000", "100000"], "ceilings_rules": {}}'::jsonb, '{"taxons": [{"name": "Full", "pretty_name": "Proposal -> Full"}]}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(30, 5, NULL, 11, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, 'C1', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{"taxons": [{"name": "Full", "pretty_name": "Proposal -> Full"}]}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(31, 5, NULL, 12, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, 'C2', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(32, 5, NULL, 13, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, 'C3', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(33, 6, NULL, 7, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, 'A', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{"taxons": [{"name": "Basic", "pretty_name": "Proposal -> Basic"}, {"name": "Full", "pretty_name": "Proposal -> Full"}, {"name": "Super", "pretty_name": "Proposal -> Super"}]}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(34, 6, NULL, 8, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, 'B1', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(35, 6, NULL, 9, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, 'B2', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{"taxons": [{"name": "Super", "pretty_name": "Proposal -> Super"}]}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(36, 6, NULL, 10, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, 'C', '{"ceilings": ["50000", "80000", "100000"], "ceilings_rules": {}}'::jsonb, '{"taxons": [{"name": "Full", "pretty_name": "Proposal -> Full"}]}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(37, 6, NULL, 11, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, 'C1', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{"taxons": [{"name": "Full", "pretty_name": "Proposal -> Full"}]}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(38, 6, NULL, 12, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, 'C2', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(39, 6, NULL, 13, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, 'C3', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(40, 7, NULL, 3, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(41, 7, NULL, 14, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(42, 7, NULL, 1, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(43, 7, NULL, 2, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(44, 8, NULL, 3, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(45, 8, NULL, 14, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(46, 8, NULL, 1, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(47, 8, NULL, 2, NULL, false, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(48, 9, NULL, 3, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(49, 9, NULL, 14, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(50, 9, NULL, 1, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(51, 9, NULL, 2, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(52, 10, NULL, 15, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(53, 10, NULL, 16, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(54, 10, NULL, 17, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(55, 10, NULL, 18, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(56, 10, NULL, 19, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(57, 11, NULL, 1, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(58, 12, NULL, 1, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(59, 13, NULL, 1, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(60, 13, NULL, 2, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(61, 13, NULL, 20, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(62, 13, NULL, 21, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(63, 13, NULL, 22, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(64, 13, NULL, 23, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(65, 14, NULL, 1, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(66, 14, NULL, 2, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(67, 14, NULL, 20, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(68, 14, NULL, 21, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(69, 14, NULL, 22, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(70, 14, NULL, 23, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(71, 15, NULL, 1, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(72, 15, NULL, 2, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(73, 15, NULL, 20, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(74, 15, NULL, 21, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(75, 15, NULL, 22, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(76, 15, NULL, 23, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(77, 16, NULL, 1, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(78, 16, NULL, 2, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(79, 16, NULL, 20, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(80, 16, NULL, 21, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(81, 16, NULL, 22, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);
INSERT INTO product.related_warranties
(id, packet_id, category_id, warranties_id, parent_id, mandatory, insurance_premium, start_date, end_date, recurring, "rule", external_code, ceilings, taxons)
VALUES(82, 16, NULL, 23, NULL, true, NULL, NULL, NULL, false, '{}'::jsonb, '', '{"ceilings": [], "ceilings_rules": {}}'::jsonb, '{}'::jsonb);

INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(1, 'Confermi che il tuo animale domestico ha un&rsquo;et&agrave; compresa tra i 6 mesi e i 9 anni?', 4, 1, NULL);
INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(2, 'Confermi che il tuo animale domestico &egrave; munito di microchip ed &egrave; registrato all&rsquo;Anagrafe Canina o Felina?', 4, 2, NULL);
INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(3, 'Confermi&nbsp;che sei a conoscenza della presenza di massimali, franchigie e carenze presenti nel set informativo?(<a href=\"https://tim-customers-production.s3.amazonaws.com/spree/products/information_packages/000/000/008/original/SET_INFORMATIVO_TIM_myPet_customer.pdf?**********\" target=\"_blank\" rel=\"noopener noreferrer\">scarica</a>)', 4, 3, NULL);
INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(4, 'Ha necessit&agrave; di stipulare l&rsquo;assicurazione, per s&eacute; stesso o per altri, contro i danni e (ove previsto dal Pacchetto scelto) gli infortuni, provocati e/o subiti, durante l&rsquo;attivit&agrave; sportiva amatoriale di sport sulla neve (sci da discesa; telemark; sci da fondo; snowboard; discesa con slitta o slittino) all&rsquo;interno di Aree Sciabili Attrezzate.', 2, 1, NULL);
INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(5, 'E'' consapevole che la copertura Responsabilit&agrave; Civile verso Terzi non opera in relazione alle seguenti attivit&agrave;: discesa con slitta, slittino, bob, gommoni; uso di gonfiabili e strutture adibite a giochi, anche se svolte all&rsquo;interno di Parchi Giochi sulla Neve per Bambini specificamente adibiti a tali scopi.', 2, 2, NULL);
INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(6, 'E'' consapevole che l&rsquo;assicurazione non opera in relazione a sci fuoripista e free-ride, sci-alpinismo, (trova l&rsquo;elenco completo nelle Condizioni di Assicurazione e le esclusioni applicate); e al di fuori del territorio europeo.', 2, 3, NULL);
INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(7, 'Sei interessato a assicurarti contro gli infortuni e i danni, sia subiti che provocati, durante la pratica di uno sport invernale?', 5, 1, NULL);
INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(8, 'Sei a conoscenza del fatto che la copertura è valida solo per la pratica amatoriale di sport invernali in impianti o zone adibite a tale scopo?', 5, 2, NULL);
INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(9, 'Sei a conoscenza della presenza di massimali, franchigie e scoperti presenti nel set informativo precontrattuale?', 5, 3, NULL);
INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(10, 'Sei interessato a assicurarti contro gli infortuni e i danni, sia subiti che provocati, durante la pratica di uno sport invernale?', 6, 1, NULL);
INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(11, 'Sei a conoscenza del fatto che la copertura è valida solo per la pratica amatoriale di sport invernali in impianti o zone adibite a tale scopo?', 6, 2, NULL);
INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(12, 'Sei a conoscenza della presenza di massimali, franchigie e scoperti presenti nel set informativo precontrattuale?', 6, 3, NULL);
INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(13, 'Sei interessato a assicurarti contro gli infortuni e i danni, sia subiti che provocati, durante la pratica di uno sport invernale?', 7, 1, NULL);
INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(14, 'Sei a conoscenza del fatto che la copertura è valida solo per la pratica amatoriale di sport invernali in impianti o zone adibite a tale scopo?', 7, 2, NULL);
INSERT INTO product.survey_questions
(id, "content", product_id, "position", external_code)
VALUES(15, 'Sei a conoscenza della presenza di massimali, franchigie e scoperti presenti nel set informativo precontrattuale?', 7, 3, NULL);

INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(1, 'Si', 1, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(2, 'No', 1, 2, false, 'prevent_checkout', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(3, 'Si', 2, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(4, 'No', 2, 2, false, 'prevent_checkout', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(5, 'Si', 3, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(6, 'No', 3, 2, false, 'prevent_checkout', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(7, 'Si', 4, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(8, 'No', 4, 2, false, 'prevent_checkout', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(9, 'Si', 5, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(10, 'No', 5, 2, false, 'prevent_checkout', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(11, 'Si', 6, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(12, 'No', 6, 2, false, 'prevent_checkout', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(13, 'Si', 7, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(14, 'No', 7, 2, false, 'prevent_checkout', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(15, 'Si', 8, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(16, 'No', 8, 2, false, 'prevent_checkout', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(17, 'Si', 9, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(18, 'No', 9, 2, false, 'prevent_checkout', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(19, 'Si', 10, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(20, 'No', 10, 2, false, 'prevent_checkout', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(21, 'Si', 11, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(22, 'No', 11, 2, false, 'prevent_checkout', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(23, 'Si', 12, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(24, 'No', 12, 2, false, 'prevent_checkout', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(25, 'Si', 13, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(26, 'No', 13, 2, false, 'prevent_checkout', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(27, 'Si', 14, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(28, 'No', 14, 2, false, 'prevent_checkout', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(29, 'Si', 15, 1, false, '', NULL);
INSERT INTO product.survey_answers
(id, value, survey_questions_id, "position", default_value, "rule", external_code)
VALUES(30, 'No', 15, 2, false, 'prevent_checkout', NULL);

INSERT INTO product."configuration"
(id, product_id, emission, emission_prefix, certificate, can_open_claim, claim_type, claim_provider, withdraw_type, deactivate_type, properties, deactivate_provider)
VALUES(4, 4, 'external', NULL, 'internal', false, 'external', 'MotionsCloud', 'StandardWithdraw', 'StandardDeactivate', '{"properties": [{"name": "claimProvider", "value": "MotionsCloud"}, {"name": "claimType", "value": "external"}, {"name": "claimTypeCompany", "value": "digital"}, {"name": "language", "value": "ita"}, {"name": "ClaimUserMessageKey", "value": "APERTURA_CLAIM_UTENTE", "placeHolder": ["nome", "cognome"]}, {"name": "ClaimCompanyMessageKey", "value": "APERTURA_CLAIM_UTENTE", "placeHolder": []}, {"name": "WithdrawCompanyMessageKey", "value": "TIM_RICHIESTA_RECESSO_STANDARD", "placeHolder": ["numeroOrdine", "numeroPolizza", "numeroPolizzaEsterno", "messaggio"]}, {"name": "WithdrawUserMessageKey", "value": "TIM_MY_PET_RECESSO", "placeHolder": ["nome"]}, {"name": "DeactivateUserMessageKey", "value": "TIM_MY_PET_DISATTIVAZIONE", "placeHolder": ["nome"]}, {"name": "messageType", "value": "html"}, {"name": "companyMail", "value": "<EMAIL>"}, {"name": "policyType", "value": ["rc", "pet_veterinary_expense_reimbursement"]}]}'::jsonb, NULL);
INSERT INTO product."configuration"
(id, product_id, emission, emission_prefix, certificate, can_open_claim, claim_type, claim_provider, withdraw_type, deactivate_type, properties, deactivate_provider)
VALUES(1, 1, 'external', NULL, 'external_async', false, NULL, NULL, 'StandardWithdraw', 'StandardDeactivate', '{"properties": [{"name": "claimTypeCompany", "value": "digital"}, {"name": "language", "value": "ita"}, {"name": "ClaimUserMessageKey", "value": "APERTURA_CLAIM_UTENTE", "placeHolder": ["nome", "cognome"]}, {"name": "ClaimCompanyMessageKey", "value": "APERTURA_CLAIM_UTENTE", "placeHolder": []}, {"name": "WithdrawCompanyMessageKey", "value": "EHEALTH_QUIXA_RICHIESTA_RECESSO", "placeHolder": ["numeroOrdine", "numeroPolizza", "numeroPolizzaEsterno", "messaggio"]}, {"name": "WithdrawUserMessageKey", "value": "EHEALTH_QUIXA_DISATTIVAZIONE", "placeHolder": ["nome"]}, {"name": "messageType", "value": "html"}, {"name": "companyMail", "value": "<EMAIL>"}, {"name": "policyType", "value": ["rc", "pet_veterinary_expense_reimbursement"]}]}'::jsonb, NULL);
INSERT INTO product."configuration"
(id, product_id, emission, emission_prefix, certificate, can_open_claim, claim_type, claim_provider, withdraw_type, deactivate_type, properties, deactivate_provider)
VALUES(7, 7, 'external', NULL, 'internal', false, 'internal', '', 'StandardWithdraw', 'StandardDeactivate', '{"properties": [{"name": "claimType", "value": "internal"}, {"name": "language", "value": "ita"}, {"name": "ClaimUserMessageKey", "value": "APERTURA_CLAIM_UTENTE"}, {"name": "ClaimCompanyMessageKey", "value": "APERTURA_CLAIM_YOLO", "placeHolder": ["nome", "cognome", "email", "telefono", "numeroPolizza", "numeroClaim", "messaggio", "dataSinistro"]}, {"name": "WithdrawCompanyMessageKey", "value": "TIM_FOR_SKY_SCI&SNOWBOARD_RICHIESTA_RECESSO", "placeHolder": ["numeroOrdine", "numeroPolizza", "messaggio"]}, {"name": "WithdrawUserMessageKey", "value": "TIM_FOR_SKY_SCI&SNOWBOARD_RECESSO", "placeHolder": []}, {"name": "DeactivateUserMessageKey", "value": "DISATTIVAZIONE_MI_FIDO", "placeHolder": ["nome", "productName", "dataRinnovo"]}, {"name": "messageType", "value": "html"}, {"name": "companyMail", "value": "<EMAIL>"}, {"name": "policyType", "value": ["rc", "pet_veterinary_expense_reimbursement"]}]}'::jsonb, NULL);
INSERT INTO product."configuration"
(id, product_id, emission, emission_prefix, certificate, can_open_claim, claim_type, claim_provider, withdraw_type, deactivate_type, properties, deactivate_provider)
VALUES(3, 3, 'internal', NULL, 'internal', false, 'internal', NULL, 'StandardWithdraw', 'StandardDeactivate', '{"properties": [{"name": "claimProvider", "value": null}, {"name": "claimType", "value": "internal"}, {"name": "language", "value": "ita"}, {"name": "claimUserMessageKey", "value": "APERTURA_CLAIM_UTENTE"}, {"name": "claimCompanyMessageKey", "value": "APERTURA_CLAIM_UTENTE"}, {"name": "messageType", "value": "html"}, {"name": "companyMail", "value": "<EMAIL>"}, {"name": "WithdrawCompanyMessageKey", "value": "TIM_MY_HOME_RICHIESTA_RECESSO_COMPAGNIA", "placeHolder": ["numeroOrdine", "numeroPolizza", "numeroPolizzaEsterno", "messaggio"]}, {"name": "WithdrawUserMessageKey", "value": "TIM_MY_HOME_RECESSO", "placeHolder": ["nome"]}, {"name": "ClaimCompanyUserMessageKey", "value": "TIM_APERTURA_SINISTRO", "placeHolder": ["nome", "cognome", "email", "telefono", "numeroPolizza", "numeroClaim", "messaggio", "dataSinistro"]}]}'::jsonb, NULL);
INSERT INTO product."configuration"
(id, product_id, emission, emission_prefix, certificate, can_open_claim, claim_type, claim_provider, withdraw_type, deactivate_type, properties, deactivate_provider)
VALUES(2, 2, 'external', NULL, 'external', false, NULL, NULL, 'StandardWithdraw', 'StandardDeactivate', '{}'::jsonb, NULL);
INSERT INTO product."configuration"
(id, product_id, emission, emission_prefix, certificate, can_open_claim, claim_type, claim_provider, withdraw_type, deactivate_type, properties, deactivate_provider)
VALUES(5, 5, 'external', NULL, 'internal', false, 'internal', '', 'StandardWithdraw', 'StandardDeactivate', '{"properties": [{"name": "claimType", "value": "internal"}, {"name": "language", "value": "ita"}, {"name": "ClaimUserMessageKey", "value": "APERTURA_CLAIM_UTENTE"}, {"name": "ClaimCompanyMessageKey", "value": "APERTURA_CLAIM_YOLO", "placeHolder": ["nome", "cognome", "email", "telefono", "numeroPolizza", "numeroClaim", "messaggio", "dataSinistro"]}, {"name": "WithdrawCompanyMessageKey", "value": "TIM_FOR_SKY_SCI&SNOWBOARD_RICHIESTA_RECESSO", "placeHolder": ["numeroOrdine", "numeroPolizza", "messaggio"]}, {"name": "WithdrawUserMessageKey", "value": "TIM_FOR_SKY_SCI&SNOWBOARD_RECESSO", "placeHolder": []}, {"name": "DeactivateUserMessageKey", "value": "DISATTIVAZIONE_MI_FIDO", "placeHolder": ["nome", "productName", "dataRinnovo"]}, {"name": "messageType", "value": "html"}, {"name": "companyMail", "value": "<EMAIL>"}, {"name": "policyType", "value": ["rc", "pet_veterinary_expense_reimbursement"]}]}'::jsonb, NULL);
INSERT INTO product."configuration"
(id, product_id, emission, emission_prefix, certificate, can_open_claim, claim_type, claim_provider, withdraw_type, deactivate_type, properties, deactivate_provider)
VALUES(6, 6, 'external', NULL, 'internal', false, 'internal', '', 'StandardWithdraw', 'StandardDeactivate', '{"properties": [{"name": "claimType", "value": "internal"}, {"name": "language", "value": "ita"}, {"name": "ClaimUserMessageKey", "value": "APERTURA_CLAIM_UTENTE"}, {"name": "ClaimCompanyMessageKey", "value": "APERTURA_CLAIM_YOLO", "placeHolder": ["nome", "cognome", "email", "telefono", "numeroPolizza", "numeroClaim", "messaggio", "dataSinistro"]}, {"name": "WithdrawCompanyMessageKey", "value": "TIM_FOR_SKY_SCI&SNOWBOARD_RICHIESTA_RECESSO", "placeHolder": ["numeroOrdine", "numeroPolizza", "messaggio"]}, {"name": "WithdrawUserMessageKey", "value": "TIM_FOR_SKY_SCI&SNOWBOARD_RECESSO", "placeHolder": []}, {"name": "DeactivateUserMessageKey", "value": "DISATTIVAZIONE_MI_FIDO", "placeHolder": ["nome", "productName", "dataRinnovo"]}, {"name": "messageType", "value": "html"}, {"name": "companyMail", "value": "<EMAIL>"}, {"name": "policyType", "value": ["rc", "pet_veterinary_expense_reimbursement"]}]}'::jsonb, NULL);







COMMIT;
