INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(8, NULL, NULL, 'CSSRND80A01F205O', NULL, NULL, NULL, NULL, NULL, NULL, 'CSSRND80A01F205O', 'Male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, '+3939333333333', NULL, NULL, 'false', NULL, NULL, NULL, '2023-09-13 18:45:52.781', '2023-09-13 18:45:52.781', 'CSSRND80A01F205O', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Armando", "email": "<EMAIL>", "ndgCode": "CSSRND80A01F205O", "surname": "Casas", "username": "CSSRND80A01F205O", "birthDate": "01-01-1980", "legalForm": "false", "phoneNumber": "+3939333333333", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(17, 'f57045b9-b6ff-4271-9279-5c9dba3867fe', NULL, 'NZZGNN00E01F205K', NULL, NULL, NULL, NULL, NULL, NULL, 'NZZGNN00E01F205K', 'Male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, '+39**********', NULL, NULL, 'false', NULL, NULL, NULL, '2023-10-05 09:31:37.117', '2023-10-05 09:31:37.118', 'NZZGNN00E01F205K', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Gianni", "email": "<EMAIL>", "ndgCode": "NZZGNN00E01F205K", "surname": "Nizzero", "username": "NZZGNN00E01F205K", "birthDate": "01-05-2000", "legalForm": "false", "phoneNumber": "+39**********", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(18, 'cb30e8e6-e14d-4385-ae7e-b5372f36c573', NULL, 'GAIGNN80A01F205M', NULL, NULL, NULL, NULL, NULL, NULL, 'GAIGNN80A01F205M', 'Male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, '+393484324488', NULL, NULL, 'false', NULL, NULL, NULL, '2023-10-05 09:37:17.492', '2023-10-05 09:37:17.493', 'GAIGNN80A01F205M', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Gianni", "email": "<EMAIL>", "ndgCode": "GAIGNN80A01F205M", "surname": "Gai", "username": "GAIGNN80A01F205M", "birthDate": "01-01-1980", "legalForm": "false", "phoneNumber": "+393484324488", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(3, NULL, NULL, 'PRVLSN80A01F205F', NULL, NULL, NULL, NULL, NULL, NULL, 'PRVLSN80A01F205F', 'Male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, '+*********', NULL, NULL, 'false', NULL, NULL, NULL, '2023-09-08 15:04:07.607', '2023-09-08 15:04:07.607', 'PRVLSN80A01F205F', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Alessandro", "email": "<EMAIL>", "ndgCode": "PRVLSN80A01F205F", "surname": "Prova", "username": "PRVLSN80A01F205F", "birthDate": "01-01-1980", "legalForm": "false", "phoneNumber": "+*********", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(12, NULL, NULL, 'LSNBCK80A01F205X', NULL, NULL, NULL, NULL, NULL, NULL, 'LSNBCK80A01F205X', 'Male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, '+39393333333333', NULL, NULL, 'false', NULL, NULL, NULL, '2023-09-18 13:52:53.019', '2023-09-18 13:52:53.019', 'LSNBCK80A01F205X', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Brock", "email": "<EMAIL>", "ndgCode": "LSNBCK80A01F205X", "surname": "Lesnar", "username": "LSNBCK80A01F205X", "birthDate": "01-01-1980", "legalForm": "false", "phoneNumber": "+39393333333333", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(5, NULL, NULL, 'RSSMRA04A01F205J', NULL, NULL, NULL, NULL, NULL, NULL, 'RSSMRA04A01F205J', 'Male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, '+393333333333', NULL, NULL, 'false', NULL, NULL, NULL, '2023-09-09 12:29:28.090', '2023-09-09 12:29:28.090', 'RSSMRA04A01F205J', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Mario", "email": "<EMAIL>", "ndgCode": "RSSMRA04A01F205J", "surname": "Rossi", "username": "RSSMRA04A01F205J", "birthDate": "01-01-2004", "legalForm": "false", "phoneNumber": "+393333333333", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(6, NULL, NULL, 'FGLLCU80M01F205P', NULL, NULL, '1980-08-01', 'Milano', NULL, NULL, 'FGLLCU80M01F205P', 'Male', 'Milano', '10', NULL, NULL, 110, '20100', NULL, 3613, '<EMAIL>', NULL, '+393484324488', NULL, NULL, 'false', NULL, NULL, NULL, '2023-09-11 14:37:14.719', '2023-09-21 09:07:01.679', 'FGLLCU80M01F205P', 6911, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Luca", "email": "<EMAIL>", "ndgCode": "FGLLCU80M01F205P", "surname": "Foglio", "username": "FGLLCU80M01F205P", "birthDate": "01-08-1980", "legalForm": "false", "phoneNumber": "+393484324488", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(2, NULL, NULL, 'CCCGTN97S03F839K', 'Gaetano', 'Ciccone', '1997-11-03', 'Napoli', 'Italia', 'Napoli', 'CCCGTN97S03F839K', 'Male', 'Napoli', '326', 'Napoli', 'Italia', 110, '80139', 'Napoli', 3607, '<EMAIL>', NULL, '+393333333333', NULL, NULL, 'false', NULL, NULL, NULL, '2023-09-08 15:02:05.214', '2023-10-03 13:54:19.821', 'CCCGTN97S03F839K', 7388, NULL, NULL, NULL, 'NA', 'NA', NULL, '{"name": "Gaetano", "email": "<EMAIL>", "ndgCode": "CCCGTN97S03F839K", "surname": "Ciccone", "username": "CCCGTN97S03F839K", "birthDate": "03-11-1997", "legalForm": "false", "phoneNumber": "+393333333333", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(50, 'LQR3F', NULL, 'TCHUSR29C23D122S', 'Rullo', 'DiTamburi', '1990-06-02', 'Napoli', 'Italia', 'Napoli', 'RULDIT88M30C129C', 'Male', 'Via casa asd', NULL, 'Napoli', 'Italia', 110, '80060', 'Napoli', 3675, '<EMAIL>', NULL, '+393333333331', NULL, NULL, 'false', NULL, NULL, NULL, '2023-06-23 09:29:31.583', '2023-06-26 20:28:17.207', 'TCHUSR29C23D122S', 7810, 3675, 31, 110, 'NA', NULL, NULL, '{"name": "Luca", "email": "<EMAIL>", "ndgCode": "FGLLCU80M01F205P", "surname": "Foglio", "username": "FGLLCU80M01F205P", "birthDate": "01-08-1980", "legalForm": "false", "phoneNumber": "+393484324488", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(13, '94f63d1e-5879-4a09-a426-ce85d8fc75fc', NULL, 'PLTGNN80A01F205G', NULL, NULL, NULL, NULL, NULL, NULL, 'PLTGNN80A01F205G', 'Male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, '+39**********', NULL, NULL, 'false', NULL, NULL, NULL, '2023-10-02 10:49:30.347', '2023-10-02 10:49:30.347', 'PLTGNN80A01F205G', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Gianni", "email": "<EMAIL>", "ndgCode": "PLTGNN80A01F205G", "surname": "Polti", "username": "PLTGNN80A01F205G", "birthDate": "01-01-1980", "legalForm": "false", "phoneNumber": "+39**********", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(7, NULL, NULL, 'FRNFNC80A01F205Q', NULL, NULL, NULL, NULL, NULL, NULL, 'FRNFNC80A01F205Q', 'Male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, '+39333456677', NULL, NULL, 'false', NULL, NULL, NULL, '2023-09-13 16:10:02.542', '2023-09-13 16:10:02.542', 'FRNFNC80A01F205Q', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Franco", "email": "<EMAIL>", "ndgCode": "FRNFNC80A01F205Q", "surname": "Franco", "username": "FRNFNC80A01F205Q", "birthDate": "01-01-1980", "legalForm": "false", "phoneNumber": "+39333456677", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(11, NULL, NULL, 'TSTNNI80E01F205I', NULL, NULL, NULL, NULL, NULL, NULL, 'TSTNNI80E01F205I', 'Male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, '+39**********', NULL, NULL, 'false', NULL, NULL, NULL, '2023-09-18 10:27:35.711', '2023-09-18 10:27:35.711', 'TSTNNI80E01F205I', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Nino", "email": "<EMAIL>", "ndgCode": "TSTNNI80E01F205I", "surname": "Testcasa", "username": "TSTNNI80E01F205I", "birthDate": "01-05-1980", "legalForm": "false", "phoneNumber": "+39**********", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(1, NULL, NULL, 'CSSRND85M20F205N', 'Lehilser', 'Alvarez', '2009-10-27', 'Milano', 'Ecuador', 'Milano', 'LVRLLS97R27Z605S', 'Male', 'Milano', '12', 'Milano', 'Italia', 110, '20144', 'Milano', 3690, '<EMAIL>', NULL, '+39333333333', NULL, NULL, 'false', NULL, NULL, NULL, '2023-09-08 14:04:35.904', '2023-10-10 09:58:29.184', 'CSSRND85M20F205N', 111, NULL, NULL, NULL, 'MI', 'MI', NULL, '{"name": "Armando", "email": "<EMAIL>", "ndgCode": "CSSRND85M20F205N", "surname": "Casas", "username": "CSSRND85M20F205N", "birthDate": "20-08-1985", "legalform": "false", "phoneNumber": "+39333333333", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(19, '6da2fe85-7d96-40fc-a93e-0632a908a5b5', NULL, 'PCCLNI10E01F205D', NULL, NULL, '2009-05-01', 'Milano', NULL, NULL, 'PCCLNI10E01F205D', 'Male', 'Via GrigioVerdi', '12', NULL, NULL, 110, '20100', NULL, 3613, '<EMAIL>', NULL, '+39**********', NULL, NULL, 'false', NULL, NULL, NULL, '2023-10-06 07:37:23.141', '2023-10-09 14:20:21.343', 'PCCLNI10E01F205D', 6911, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Lino", "email": "<EMAIL>", "ndgCode": "PCCLNI10E01F205D", "surname": "Piccolo", "username": "PCCLNI10E01F205D", "birthDate": "01-05-2009", "legalForm": "false", "phoneNumber": "+39**********", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(21, '69b72bde-15c6-46a1-b568-33808a429ea5', NULL, 'RTYDDD23R10I347O', NULL, NULL, NULL, NULL, NULL, NULL, 'RTYDDD23R10I347O', 'Male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, '+393334567890', NULL, NULL, 'false', NULL, NULL, NULL, '2023-10-10 08:11:46.919', '2023-10-10 08:11:46.919', 'RTYDDD23R10I347O', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Ale", "email": "<EMAIL>", "ndgCode": "RTYDDD23R10I347O", "surname": "Sta", "username": "RTYDDD23R10I347O", "birthDate": "13-11-1953", "legalForm": "false", "phoneNumber": "+393334567890", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(14, '19a2f182-cc78-4028-8080-fb753668ac3c', NULL, 'PLTPLA00E01F205C', NULL, NULL, '2000-05-01', 'Milano', NULL, NULL, 'PLTPLA00E01F205C', 'Male', 'Milano', '12', NULL, NULL, 110, '20100', NULL, 3656, '<EMAIL>', NULL, '+3939**********', NULL, NULL, 'false', NULL, NULL, NULL, '2023-10-04 12:15:22.440', '2023-10-04 12:19:25.614', 'PLTPLA00E01F205C', 358, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Paolo", "email": "<EMAIL>", "ndgCode": "PLTPLA00E01F205C", "surname": "Paolotti", "username": "PLTPLA00E01F205C", "birthDate": "01-05-2000", "legalForm": "false", "phoneNumber": "+3939**********", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(15, 'df1f9423-cfb4-402d-8dc3-6e4ccfd94939', NULL, '****************', NULL, NULL, NULL, NULL, NULL, NULL, '****************', 'Male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, '+393336992102', NULL, NULL, 'false', NULL, NULL, NULL, '2023-10-05 08:52:36.237', '2023-10-05 08:52:36.237', '****************', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Marco", "email": "<EMAIL>", "ndgCode": "****************", "surname": "Polo", "username": "****************", "birthDate": "23-02-1981", "legalForm": "false", "phoneNumber": "+393336992102", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(16, '8fbc7146-18e1-4164-adbe-ff8f206cfab2', NULL, '****************', NULL, NULL, NULL, NULL, NULL, NULL, '****************', 'Male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, '+39**********', NULL, NULL, 'false', NULL, NULL, NULL, '2023-10-05 09:05:36.445', '2023-10-05 09:05:36.445', '****************', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Paolo", "email": "<EMAIL>", "ndgCode": "****************", "surname": "Nuovo", "username": "****************", "birthDate": "01-01-1980", "legalForm": "false", "phoneNumber": "+39**********", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(22, '0145824a-85de-4dba-908f-57ef55fdbb41', NULL, '14EFB39F-45EA-4128-B917-75EC4C104C13', 'Ippo', 'Franco', '1996-11-06', NULL, NULL, NULL, '14EFB39F-45EA-4128-B917-75EC4C104C13', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, 'false', NULL, NULL, NULL, '2023-10-16 10:28:33.729', '2023-10-16 10:28:33.729', '14EFB39F-45EA-4128-B917-75EC4C104C13', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"data": "[{\"key\":\"product_code\", \"value\":\"net-homeflix\"}, {\"key\":\"order\", \"value\":\"Y-67d547f\"}]", "name": "Ippo", "email": "<EMAIL>", "ndgCode": "14efb39f-45ea-4128-b917-75ec4c104c13", "surname": "Franco", "username": "14efb39f-45ea-4128-b917-75ec4c104c13", "birthDate": "06-11-1996", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(20, '4af8bfd5-6b30-47fa-a050-d0a2f352baed', NULL, 'BSTLCU80A01F205H', NULL, NULL, NULL, NULL, NULL, NULL, 'BSTLCU80A01F205H', 'Male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, '+39345845896', NULL, NULL, 'false', NULL, NULL, NULL, '2023-10-10 07:49:35.592', '2023-10-10 07:49:35.592', 'BSTLCU80A01F205H', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Lucio", "email": "<EMAIL>", "ndgCode": "BSTLCU80A01F205H", "surname": "Basta", "username": "BSTLCU80A01F205H", "birthDate": "01-01-1980", "legalForm": "false", "phoneNumber": "+39345845896", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(23, '40e4aaca-eea0-42fb-b2f2-f6c436ad7048', NULL, '68BD6715-9E40-4663-8B91-A76A5C9DC66A', 'Gaetano', 'Ciccone', '1998-11-11', NULL, NULL, NULL, '68BD6715-9E40-4663-8B91-A76A5C9DC66A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-17 09:30:17.891', '2023-10-17 09:30:17.891', '68BD6715-9E40-4663-8B91-A76A5C9DC66A', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Gaetano", "email": "<EMAIL>", "ndgCode": "68bd6715-9e40-4663-8b91-a76a5c9dc66a", "surname": "Ciccone", "username": "68bd6715-9e40-4663-8b91-a76a5c9dc66a", "birthDate": "11-11-1998", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(24, '1ead53b9-0dbf-4fa9-8ac6-0d2c1d05a416', NULL, '82E0DD01-19DA-458E-9E10-E7ABB8182294', 'Sebastian', 'Maris', '1930-11-07', NULL, NULL, NULL, '82E0DD01-19DA-458E-9E10-E7ABB8182294', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-17 13:07:33.997', '2023-10-17 13:07:33.997', '82E0DD01-19DA-458E-9E10-E7ABB8182294', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Sebastian", "email": "<EMAIL>", "ndgCode": "82e0dd01-19da-458e-9e10-e7abb8182294", "surname": "Maris", "username": "82e0dd01-19da-458e-9e10-e7abb8182294", "birthDate": "07-11-1930", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(25, '7cdce846-2611-4e03-93a4-50757333c0f7', NULL, '9CF7F5CC-75E8-488C-83D6-2BDA9FD4C3EA', 'Fausto', 'Evento', '2003-05-01', NULL, NULL, NULL, '9CF7F5CC-75E8-488C-83D6-2BDA9FD4C3EA', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-17 13:41:06.651', '2023-10-17 13:41:06.652', '9CF7F5CC-75E8-488C-83D6-2BDA9FD4C3EA', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"data": "[{\"key\":\"product_code\", \"value\":\"net-homeflix\"}, {\"key\":\"order\", \"value\":\"Y-918807d\"}]", "name": "Fausto", "email": "<EMAIL>", "ndgCode": "9cf7f5cc-75e8-488c-83d6-2bda9fd4c3ea", "surname": "Evento", "username": "9cf7f5cc-75e8-488c-83d6-2bda9fd4c3ea", "birthDate": "01-05-2003", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(28, 'f16d3ca9-d6f9-4e66-a6b3-5f46b274ce54', NULL, '086931A0-B1C4-4B7A-94FE-3A2C73C5C3F6', 'vito', 'ricci', '1993-10-09', 'Ceglie Messapica', NULL, NULL, '086931A0-B1C4-4B7A-94FE-3A2C73C5C3F6', 'Male', 'via dei ceci ', '21', NULL, NULL, 110, '20125', NULL, 3677, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-24 09:42:27.790', '2023-10-24 09:51:48.324', '086931A0-B1C4-4B7A-94FE-3A2C73C5C3F6', 899, NULL, NULL, NULL, NULL, NULL, NULL, '{"data": "[{\"key\":\"product_code\", \"value\":\"net-homeflix\"}, {\"key\":\"order\", \"value\":\"Y-03c97c2\"}]", "name": "vito", "email": "<EMAIL>", "ndgCode": "086931a0-b1c4-4b7a-94fe-3a2c73c5c3f6", "surname": "ricci", "username": "086931a0-b1c4-4b7a-94fe-3a2c73c5c3f6", "birthDate": "09-10-1993", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(4, NULL, NULL, 'DPRFBA74M01F205T', NULL, NULL, '1974-08-01', 'Milano', 'Italia', 'Milano', 'DPRFBA74M01F205T', 'Male', 'Milano', '100', 'Abbiategrasso', 'Italia', 110, '20100', 'Milano', 3613, '<EMAIL>', NULL, '+39**********', NULL, NULL, 'false', NULL, NULL, NULL, '2023-09-08 15:51:12.178', '2023-10-24 08:40:52.514', 'DPRFBA74M01F205T', 15, 3613, 6911, 110, NULL, NULL, NULL, '{"name": "Fabio", "email": "<EMAIL>", "ndgCode": "DPRFBA74M01F205T", "surname": "Di Pierro", "username": "DPRFBA74M01F205T", "birthDate": "01-08-1974", "legalForm": "false", "phoneNumber": "+39**********", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(26, '4ec49957-6511-4860-a257-5c1ddd186cdf', NULL, 'B3B60A1F-F622-415E-A3D9-A91A94EBC148', 'Andrea', 'Manzella', '1989-12-18', 'Torino', NULL, NULL, 'MNZNDR89T18L219J', 'Male', 'Corso Galileo Ferraris', '160', NULL, NULL, 110, '10134', NULL, 3602, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-18 16:00:31.907', '2023-10-20 08:32:48.849', 'B3B60A1F-F622-415E-A3D9-A91A94EBC148', 12403, NULL, NULL, NULL, NULL, NULL, NULL, '{"data": "[{\"key\":\"product_code\", \"value\":\"net-homeflix\"}, {\"key\":\"order\", \"value\":\"Y-e72235a\"}]", "name": "Andrea", "email": "<EMAIL>", "ndgCode": "b3b60a1f-f622-415e-a3d9-a91a94ebc148", "surname": "Manzella", "username": "b3b60a1f-f622-415e-a3d9-a91a94ebc148", "birthDate": "18-12-1989", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(27, '58f1586e-16ed-40e9-8817-afa6f67ad744', NULL, '18766CC3-8B12-4669-92F4-7A7322991548', 'Marco', 'Polo', '1981-02-23', NULL, NULL, NULL, '18766CC3-8B12-4669-92F4-7A7322991548', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-20 15:21:37.090', '2023-10-20 15:21:37.090', '18766CC3-8B12-4669-92F4-7A7322991548', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"data": "[{\"key\":\"product_code\", \"value\":\"net-homeflix\"}, {\"key\":\"order\", \"value\":\"Y-52e0f8a\"}]", "name": "Marco", "email": "<EMAIL>", "ndgCode": "18766cc3-8b12-4669-92f4-7a7322991548", "surname": "Polo", "username": "18766cc3-8b12-4669-92f4-7a7322991548", "birthDate": "23-02-1981", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(10, NULL, NULL, 'TSTYLO80A01F205B', 'Yolo', 'Test', '1980-01-01', 'Milano', 'Italia', 'Milano', 'TSTYLO80A01F205B', 'Male', 'Via Bella', '12', 'Milano', 'Italia', 110, '20144', 'Milano', 3690, '<EMAIL>', NULL, '+39333333333', NULL, NULL, 'false', NULL, NULL, NULL, '2023-09-18 09:23:27.126', '2023-10-27 14:32:26.864', 'TSTYLO80A01F205B', 238, NULL, NULL, NULL, 'MI', 'MI', NULL, '{"name": "Yolo", "email": "<EMAIL>", "ndgCode": "TSTYLO80A01F205B", "surname": "Test", "username": "TSTYLO80A01F205B", "birthDate": "01-01-1980", "legalform": "false", "phoneNumber": "+39333333333", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(33, 'd02ef4ff-59fe-4abe-83b4-90fbfdbd2de0', NULL, '4059C4E2-DAA9-4394-B075-10666B9BC628', 'Test', 'Test', '1947-11-07', NULL, NULL, NULL, '4059C4E2-DAA9-4394-B075-10666B9BC628', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-25 14:02:00.152', '2023-10-25 14:02:00.152', '4059C4E2-DAA9-4394-B075-10666B9BC628', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "Test", "email": "<EMAIL>", "ndgCode": "4059c4e2-daa9-4394-b075-10666b9bc628", "surname": "Test", "username": "4059c4e2-daa9-4394-b075-10666b9bc628", "birthDate": "07-11-1947", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(34, 'ce07bbb3-559d-4e63-a564-141fc221bb1f', NULL, '6439E569-E154-4826-82D2-B4AEBDB48B45', 'TestAle', 'TestStasi', '1972-11-11', NULL, NULL, NULL, '6439E569-E154-4826-82D2-B4AEBDB48B45', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-25 14:03:07.687', '2023-10-25 14:03:07.687', '6439E569-E154-4826-82D2-B4AEBDB48B45', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "TestAle", "email": "<EMAIL>", "ndgCode": "6439e569-e154-4826-82d2-b4aebdb48b45", "surname": "TestStasi", "username": "6439e569-e154-4826-82d2-b4aebdb48b45", "birthDate": "11-11-1972", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(35, 'fbca7bec-dcec-4059-814b-19afc6350974', NULL, 'D4527ACB-52BF-46C6-A573-7E8D0218BE89', 'test', 'test', '1943-11-27', NULL, NULL, NULL, 'D4527ACB-52BF-46C6-A573-7E8D0218BE89', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-25 14:16:16.497', '2023-10-25 14:16:16.497', 'D4527ACB-52BF-46C6-A573-7E8D0218BE89', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "test", "email": "<EMAIL>", "ndgCode": "d4527acb-52bf-46c6-a573-7e8d0218be89", "surname": "test", "username": "d4527acb-52bf-46c6-a573-7e8d0218be89", "birthDate": "27-11-1943", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(9, NULL, NULL, 'DPRFBA70M01F205P', 'Fabio', 'Di Pierro', '1970-08-01', 'Milano', 'Italia', 'Milano', 'DPRFBA70M01F205P', 'Male', 'Via Albania', '100', 'Milano', 'Italia', 110, '20100', 'Milano', 3613, '<EMAIL>', NULL, '+39**********', NULL, NULL, 'false', NULL, NULL, NULL, '2023-09-14 08:21:52.246', '2023-10-24 12:51:17.053', 'DPRFBA70M01F205P', 15, NULL, NULL, NULL, 'MI', 'MI', NULL, '{"name": "Fabio", "email": "<EMAIL>", "ndgCode": "DPRFBA70M01F205P", "surname": "Di Pierro", "username": "DPRFBA70M01F205P", "birthDate": "01-08-1970", "legalForm": "false", "phoneNumber": "+39**********", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(29, 'ebc3dac8-1c76-43cb-adb8-7843f3d240e9', NULL, '021A2B54-917A-4B78-9E46-502482409402', 'Fausto', 'Testi', '2003-05-01', 'Milano', 'Italia', 'Milano', '021A2B54-917A-4B78-9E46-502482409402', NULL, NULL, NULL, 'Abbiategrasso', 'Italia', 110, NULL, 'Milano', 3613, '<EMAIL>', NULL, '**********', NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-24 16:33:36.746', '2023-10-24 16:36:11.958', '021A2B54-917A-4B78-9E46-502482409402', 15, 3613, 6911, 110, NULL, NULL, NULL, '{"data": "[{\"key\":\"product_code\", \"value\":\"net-homeflix\"}, {\"key\":\"order\", \"value\":\"Y-d4fcbe8\"}]", "name": "Fausto", "email": "<EMAIL>", "ndgCode": "021a2b54-917a-4b78-9e46-502482409402", "surname": "Testi", "username": "021a2b54-917a-4b78-9e46-502482409402", "birthDate": "01-05-2003", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(31, 'f4aeb65b-eb13-495d-a034-5f85bfb5a8fa', NULL, 'DDF48AE2-2B9E-4219-8E4C-E144D7C136EF', 'Test', 'Test', '2000-06-12', NULL, NULL, NULL, 'DDF48AE2-2B9E-4219-8E4C-E144D7C136EF', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-25 09:41:58.766', '2023-10-25 09:41:58.766', 'DDF48AE2-2B9E-4219-8E4C-E144D7C136EF', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"data": "[{\"key\":\"product_code\", \"value\":\"net-homeflix\"}, {\"key\":\"order\", \"value\":\"Y-5269a4c\"}]", "name": "Test", "email": "<EMAIL>", "ndgCode": "ddf48ae2-2b9e-4219-8e4c-e144d7c136ef", "surname": "Test", "username": "ddf48ae2-2b9e-4219-8e4c-e144d7c136ef", "birthDate": "12-06-2000", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(32, 'af42c1fe-efe4-4266-b27d-9331f3a96d86', NULL, '569D220D-1424-4D51-8AD2-E7D5CEA436FE', 'Test', 'Test', '2004-07-12', NULL, NULL, NULL, '569D220D-1424-4D51-8AD2-E7D5CEA436FE', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-25 09:47:20.026', '2023-10-25 09:47:20.026', '569D220D-1424-4D51-8AD2-E7D5CEA436FE', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"data": "[{\"key\":\"product_code\", \"value\":\"net-homeflix\"}, {\"key\":\"order\", \"value\":\"Y-6fc349b\"}]", "name": "Test", "email": "<EMAIL>", "ndgCode": "569d220d-1424-4d51-8ad2-e7d5cea436fe", "surname": "Test", "username": "569d220d-1424-4d51-8ad2-e7d5cea436fe", "birthDate": "12-07-2004", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(36, '985791cb-2717-4f3d-8bd3-e3b6bd02eda0', NULL, '2A8336B3-A90A-442F-956F-4E8BA829FE43', 'test', 'test', '1949-11-23', NULL, NULL, NULL, '2A8336B3-A90A-442F-956F-4E8BA829FE43', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-25 14:24:42.374', '2023-10-25 14:24:42.374', '2A8336B3-A90A-442F-956F-4E8BA829FE43', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "test", "email": "<EMAIL>", "ndgCode": "2a8336b3-a90a-442f-956f-4e8ba829fe43", "surname": "test", "username": "2a8336b3-a90a-442f-956f-4e8ba829fe43", "birthDate": "23-11-1949", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(37, '7c00f34b-b124-462b-b9ee-369cbfed36fb', NULL, '96115BDC-B6A3-40A7-A024-00C7F98600C6', 'testAle', 'test', '1949-11-23', NULL, NULL, NULL, '96115BDC-B6A3-40A7-A024-00C7F98600C6', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-25 14:25:00.852', '2023-10-25 14:25:00.852', '96115BDC-B6A3-40A7-A024-00C7F98600C6', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"name": "testAle", "email": "<EMAIL>", "ndgCode": "96115bdc-b6a3-40a7-a024-00c7f98600c6", "surname": "test", "username": "96115bdc-b6a3-40a7-a024-00c7f98600c6", "birthDate": "23-11-1949", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);
INSERT INTO customer.customers
(id, customer_code, external_code, username, "name", surname, date_of_birth, birth_city, birth_country, birth_state, tax_code, gender, street, street_number, city, country, country_id, zip_code, state, state_id, primary_mail, secondary_mail, primary_phone, secondary_phone, "language", legal_form, education, salary, profession, created_at, updated_at, ndg, city_id, birth_state_id, birth_city_id, birth_country_id, state_abbr, birth_state_abbr, id_card, registration_info, retired_code, ndg_provider, utm_source, vatcode, company)
VALUES(30, 'fa88cb79-08f8-4d8f-a28e-4957a3b27427', NULL, '05796192-0E69-445D-98AD-B532E9AABA2D', 'Martino', 'Manzo', '1966-11-18', 'Salerno', 'Italia', 'Salerno', '05796192-0E69-445D-98AD-B532E9AABA2D', 'Male', 'via test', '11', 'Matera', 'Italia', 110, '12345', 'Matera', 3683, '<EMAIL>', NULL, '+39333333333', NULL, NULL, NULL, NULL, NULL, NULL, '2023-10-25 08:00:12.402', '2023-10-27 14:27:30.901', '05796192-0E69-445D-98AD-B532E9AABA2D', 6716, 3676, 10261, 110, NULL, NULL, NULL, '{"name": "Martino", "email": "<EMAIL>", "ndgCode": "05796192-0e69-445d-98ad-b532e9aaba2d", "surname": "Manzo", "username": "05796192-0e69-445d-98ad-b532e9aaba2d", "birthDate": "18-11-1966", "legalForm": "false", "userAcceptances": [{"tag": "profiling", "value": true}, {"tag": "third_parts_comunication", "value": true}]}'::jsonb, NULL, NULL, NULL, NULL, NULL);

INSERT INTO customer.flags
(id, description, "position", created_at, updated_at, deleted_at, tag, kind, mandatory)
VALUES(2, 'Acconsento ad essere contattato per finalità promozionali e di marketing come individuato nell’<b>Informativa</b>', 2, '2021-02-22 09:44:48.779', '2021-03-23 10:55:11.384', NULL, 'marketingConsent', 'privacy', false);
INSERT INTO customer.flags
(id, description, "position", created_at, updated_at, deleted_at, tag, kind, mandatory)
VALUES(3, 'Acconsento alla comunicazione dei miei dati personali a partner commerciali di Yolo come individuato nell’<b>Informativa</b>', 3, '2021-02-22 09:46:50.219', '2021-03-23 10:54:58.909', NULL, 'privacyConsent', 'privacy', false);
INSERT INTO customer.flags
(id, description, "position", created_at, updated_at, deleted_at, tag, kind, mandatory)
VALUES(4, 'Acconsento al trattamento dei miei dati personali per attività di profilazione come individuato nell’<b>Informativa</b>', 4, '2021-02-22 09:49:33.851', '2021-03-16 15:10:15.529', NULL, 'communicationConsent', 'privacy', false);
INSERT INTO customer.flags
(id, description, "position", created_at, updated_at, deleted_at, tag, kind, mandatory)
VALUES(5, 'Acconsento ad essere contattato per finalità di comunicazione personalizzata sulla base della mia geo-localizzazione come individuato nell’<b>Informativa</b>', 5, '2021-02-22 09:50:44.985', '2021-03-16 15:10:17.989', NULL, 'profilingConsent', 'privacy', false);
INSERT INTO customer.flags
(id, description, "position", created_at, updated_at, deleted_at, tag, kind, mandatory)
VALUES(1, 'Accettazione <b>informativa privacy</b>', 1, '2021-02-22 09:40:15.920', '2021-02-22 14:22:25.434', NULL, 'tdConsent', 'privacy', false);